import React, { useState, useEffect, useRef } from "react"
import { fetchAPI } from "@/lib/api"
import { handleAuthError } from "@/lib/auth-utils"
import Link from "next/link"
import Modal from "@mui/material/Modal"
import { options } from "@/pages/api/auth/[...nextauth]"
import { getServerSession } from "next-auth/next"
import Swap from "@/components/swap"
import { withRouter } from "next/router"
import UserConnection from "@/components/userConnection"
import { signOut, useSession } from "next-auth/react"
import TawkMessengerWrapper from "@/components/tawkWrapper"
import { useTranslation } from "next-i18next"
import { serverSideTranslations } from "next-i18next/serverSideTranslations"
import { destroyCookie } from "nookies"

// Helper Functions
const removeLeadingZeros = (numberString) => {
  return String(numberString).replace(/^0+/, "")
}

// Function to calculate next week day
function getNextWeekDay(lastday) {
  // Parse the initial date - this contains the original start date
  const receivedDate = new Date(lastday)
  
  // We want to find the next Tuesday (2)
  const targetDay = 2 // Tuesday is 2 in JavaScript (0 = Sunday, 1 = Monday, etc.)
  
  // Get today's date
  const today = new Date()
  
  // Calculate days until next Tuesday
  let daysUntilNextTuesday
  
  if (today.getDay() === targetDay) {
    // If today is Tuesday, get next Tuesday (7 days later)
    daysUntilNextTuesday = 7
  } else if (today.getDay() < targetDay) {
    // If today is before Tuesday in the week
    daysUntilNextTuesday = targetDay - today.getDay()
  } else {
    // If today is after Tuesday in the week
    daysUntilNextTuesday = 7 - (today.getDay() - targetDay)
  }
  
  // Calculate the next Tuesday
  const nextTuesday = new Date(today)
  nextTuesday.setDate(today.getDate() + daysUntilNextTuesday)
  
  // Format as DD/MM
  return `${nextTuesday.getDate()}/${nextTuesday.getMonth() + 1}`
}

const getCompletedScenarioIds = (senarioList) => {
  return senarioList.reduce((acc, obj) => {
    const [, scenarios] = Object.entries(obj)[0] || []
    return [...acc, ...(scenarios || [])]
  }, [])
}

const determineActiveGroup = (groups, completedScenarioIds) => {
  const orderedGroups = [...groups].sort((a, b) => a.index - b.index)

  for (let i = 0; i < orderedGroups.length; i++) {
    const currentGroup = orderedGroups[i]
    const hasUncompletedScenarios = currentGroup.senarios.some(
      (s) => !completedScenarioIds.includes(s.id)
    )

    if (hasUncompletedScenarios) {
      return {
        group: currentGroup,
        groupIndex: i,
        lastCompletedScenario: currentGroup.senarios.reduce(
          (last, scenario, index) =>
            completedScenarioIds.includes(scenario.id) ? index : last,
          -1
        ),
      }
    }
  }

  return {
    group: orderedGroups[orderedGroups.length - 1],
    groupIndex: orderedGroups.length - 1,
    lastCompletedScenario: -1,
    allCompleted: true,
  }
}

// ScenarioList Component
const ScenarioList = ({
  scenarios,
  group,
  groups,
  senarioList,
  numScenarios,
  t,
  startIndex,
}) => {
  const firstRedScenarioRef = useRef(null)
  const lastCompletedRef = useRef(null)
  let scenarioNumber = startIndex

  // Get all scenarios in a flat array
  const getAllScenarios = () => {
    return groups.reduce((acc, group) => {
      return [
        ...acc,
        ...group.senarios.map((scenario) => ({
          ...scenario,
          groupId: group.id,
          groupIndex: group.index,
        })),
      ]
    }, [])
  }

  // Get completed scenarios
  const completedScenarioIds = senarioList.reduce((acc, obj) => {
    const [, scenarios] = Object.entries(obj)[0] || []
    return [...acc, ...(scenarios || [])]
  }, [])

  // Get scenario state
  const getScenarioState = (scenario) => {
    // If scenario is completed, return completed
    if (completedScenarioIds.includes(scenario.id)) {
      return "completed"
    }

    // Get all scenarios and find current scenario's index
    const allScenarios = getAllScenarios()
    const currentIndex = allScenarios.findIndex((s) => s.id === scenario.id)

    // Find first uncompleted scenario's index
    const firstUncompletedIndex = allScenarios.findIndex(
      (s) => !completedScenarioIds.includes(s.id)
    )

    // If this is the first uncompleted scenario and numScenarios > 0
    if (currentIndex === firstUncompletedIndex && numScenarios > 0) {
      return "next"
    }

    // Check if this scenario should be available based on weeks passed
    // If numScenarios is 0 or negative, we shouldn't show any upcoming scenarios
    if (numScenarios <= 0) {
      return "locked" // Lock all non-completed scenarios if no weeks have passed
    }

    // Only make scenarios available if they're within the allowed range (numScenarios)
    const positionAfterFirstUncompleted = currentIndex - firstUncompletedIndex
    if (
      positionAfterFirstUncompleted >= 0 &&
      positionAfterFirstUncompleted < numScenarios
    ) {
      return "upcoming"
    }

    return "locked"
  }
  useEffect(() => {
    if (firstRedScenarioRef.current) {
      setTimeout(() => {
        firstRedScenarioRef.current.scrollIntoView({
          behavior: "smooth",
          block: "center",
        })
      }, 500)
    }
  }, [group.id, numScenarios])

  const renderScenario = (scenario) => {
    const state = getScenarioState(scenario)
    const currentScenarioNumber = scenarioNumber++

    // Determine if the scenario should be red
    const isNext = state === "next"
    const isUpcoming = state === "upcoming"
    const shouldBeRed = isNext || isUpcoming

    // Make upcoming scenarios clickable or not based on availability
    const UpcomingScenarioContent = () => (
      <>
        <h3
          className="banner-subtitle-text1 mb-0 pl-3 pt-2 pb-2 w-100"
          style={shouldBeRed ? { color: "#FF0000" } : {}}
        >
          {t("SCENARIO")} {currentScenarioNumber} : {scenario.title}
        </h3>
        <h3
          className="banner-subtitle-text2 pl-2 ml-2 pb-3 w-100"
          style={shouldBeRed ? { color: "#FF0000" } : {}}
        >
          {scenario.description}
        </h3>
      </>
    )

    switch (state) {
      case "completed":
        return (
          <div className="row-fluid flex-row d-flex ml-4 justify-content-between w-100">
            <div className="column-image ml-0 justify-content-between">
              <img
                ref={lastCompletedRef}
                src="/images/check-icon.png"
                className="img mr-3 pt-1 mt-3"
                alt="completed"
              />
            </div>
            <div className="column-title mt-1 mr-5 ml-0 justify-content-between">
              <Link href={`/senario/preview/${scenario.id}`}>
                <h3 className="banner-subtitle-text1 hidden-ip mb-0 pl-3 pt-2 pb-2 w-100">
                  {t("SCENARIO")} {currentScenarioNumber} : {scenario.title}
                </h3>
                <h3 className="banner-subtitle-text1 d-visible-ip mb-0 pl-3 pt-2 pb-2 w-100">
                  {scenario.title}
                </h3>
              </Link>
              <h3 className="banner-subtitle-text2 pl-2 ml-2 pb-3 w-100">
                {scenario.description}
              </h3>
            </div>
          </div>
        )

      case "next":
        return (
          <div
            ref={firstRedScenarioRef}
            className="row-fluid flex-row d-flex ml-4 justify-content-between w-100"
          >
            <div className="column-image ml-0 justify-content-between">
              <img
                src="/images/right-arrow.png"
                className="img mr-3 pt-1 mt-3"
                alt="scenario"
              />
            </div>
            <div className="column-title mt-1 mr-5 ml-0 justify-content-between">
              <Link href={`/senario/preview/${scenario.id}`}>
                <UpcomingScenarioContent />
              </Link>
            </div>
          </div>
        )

      case "upcoming":
        return (
          <div className="row-fluid flex-row d-flex ml-4 justify-content-between w-100">
            <div className="column-image ml-0 justify-content-between">
              <img
                src="/images/3d-lock.png"
                className="img-lock mr-2"
                alt="locked"
              />
            </div>
            <div className="column-title mt-1 ml-0 mr-5 justify-content-between">
              {/* Make upcoming scenarios clickable if they're within the allowed range */}
              {numScenarios > 0 ? (
                <Link href={`/senario/preview/${scenario.id}`}>
                  <UpcomingScenarioContent />
                </Link>
              ) : (
                <UpcomingScenarioContent />
              )}
            </div>
          </div>
        )

      default:
        return (
          <div className="row-fluid flex-row d-flex ml-4 justify-content-between w-100">
            <div className="column-image ml-0 justify-content-between">
              <img
                src="/images/3d-lock.png"
                className="img-lock mr-2"
                alt="locked"
              />
            </div>
            <div className="column-title mt-1 ml-0 mr-5 justify-content-between">
              <h3 className="banner-subtitle-text1-disable hidden-ip mb-0 pt-2 pl-3 pb-2 w-100">
                {t("SCENARIO")} {currentScenarioNumber} : {scenario.title}
              </h3>
              <h3 className="banner-subtitle-text1-disable d-visible-ip mb-0 pt-2 pl-3 pb-2 w-100">
                {scenario.title}
              </h3>
              <h3 className="banner-subtitle-text2-disable pl-2 ml-2 pb-3 w-100">
                {t("LOCKED_SCENARIO")}
              </h3>
            </div>
          </div>
        )
    }
  }

  return (
    <div className="column-scenarios mt-3 mr-4">
      {scenarios.map((scenario) => (
        <div key={scenario.id}>{renderScenario(scenario)}</div>
      ))}
    </div>
  )
}

// Main Index Component
const Index = ({
  groups,
  numScenarios,
  users,
  completedGroups,
  senarioList,
  senarioScoreCount,
  initialStartDate,
  charactersData,
  charModalId,
  error,
}) => {
  const starterGroups = groups.filter((group) => group.index === 0)
  const individualGroups = groups.filter((group) => group.index === 1)
  const assimulationGroups = groups.filter((group) => group.index === 2)
  const bilanGroups = groups.filter((group) => group.index === 3)
  console.log(users)
  const { t } = useTranslation("common")
  const { data: session } = useSession()
  const [open, setOpen] = useState(true)
  const [isLandscape, setIsLandscape] = useState(false)

  // Determine active tab based on starter completion
  const [activeTab, setActiveTab] = useState(() => {
    const isStarterComplete = starterGroups.every(
      (group) =>
        completedGroups.includes(String(group.id)) ||
        completedGroups.includes(group.id)
    )

    const isIndividualComplete = individualGroups.every(
      (group) =>
        completedGroups.includes(String(group.id)) ||
        completedGroups.includes(group.id)
    )

    if (
      isStarterComplete &&
      isIndividualComplete &&
      assimulationGroups.length > 0
    ) {
      return "assimulation"
    } else if (isStarterComplete && individualGroups.length > 0) {
      return "individual"
    } else {
      return "starter"
    }
  })

  useEffect(() => {
    const checkSession = async () => {
      try {
        await fetchAPI(
          "/users/me",
          {},
          {
            headers: {
              Authorization: `Bearer ${session?.jwt}`,
            },
          }
        )
      } catch (error) {
        if (error.status === 401 || error?.response?.status === 401) {
          await handleAuthError()
        }
      }
    }

    const intervalId = setInterval(checkSession, 5 * 60 * 1000)
    return () => clearInterval(intervalId)
  }, [session])

  useEffect(() => {
    if (error === "UNAUTHORIZED") {
      handleLogout()
    }
  }, [error])

  useEffect(() => {
    const checkOrientation = () => {
      const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent)
      setIsLandscape(
        isMobile ? window.matchMedia("(orientation: landscape)").matches : true
      )
    }

    checkOrientation()
    window.addEventListener("resize", checkOrientation)
    return () => window.removeEventListener("resize", checkOrientation)
  }, [])

  const handleLogout = async () => {
    await signOut({ redirect: false })
    destroyCookie(null, "next-auth.session-token")
    destroyCookie(null, "next-auth.csrf-token")
    destroyCookie(null, "__Secure-next-auth.session-token")
    router.push("/login")
  }

  const handleClose = async () => {
    setOpen(false)
    await fetchAPI(
      `/user-answer/notice/${users.id}`,
      {},
      {
        method: "PUT",
        body: JSON.stringify({
          notice: false,
          page: 1,
        }),
        headers: {
          Authorization: `Bearer ${session.jwt}`,
        },
      }
    )
  }

  const handleDownloadReport = async () => {
    try {
      const response = await fetchAPI(
        `/reports/get/${users.id}`,
        {},
        {
          headers: {
            Authorization: `Bearer ${session.jwt}`,
          },
        }
      )

      if (response?.url) {
        const fileUrl = `${process.env.NEXT_PUBLIC_API_URL}${response.url}`
        const fileResponse = await fetch(fileUrl, {
          headers: {
            Authorization: `Bearer ${session.jwt}`,
          },
        })

        if (!fileResponse.ok) throw new Error("File download failed")

        const blob = await fileResponse.blob()
        const downloadUrl = window.URL.createObjectURL(blob)
        const link = document.createElement("a")
        link.href = downloadUrl

        const contentDisposition = fileResponse.headers.get(
          "content-disposition"
        )
        const fileName = contentDisposition
          ? contentDisposition.split("filename=")[1].replace(/['"]/g, "")
          : "report.pdf"

        link.download = fileName
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(downloadUrl)
      }
    } catch (error) {
      console.error("Error downloading report:", error)
    }
  }

  const renderGroupFooter = (group) => (
    <h5 className="scenario-category ml-3 px-2">
      {senarioList.some(
        (obj) => obj[group.id] && obj[group.id].length === group.senarios.length
      ) ? (
        <Link href={`/group/${group.id}`}>
          <div className="btn-white mt-1 mx-4 mb-4 py-3 px-5">
            {t("COMPLETE_COURSE")}
          </div>
        </Link>
      ) : numScenarios <= 0 ? (
        <Link href={`/group/${group.id}`}>
          <div className="btn-continue mt-1 ml-4 mr-5 mb-3 py-3 px-5">
            {t("SEE_YOU_ON", { date: getNextWeekDay(initialStartDate) })}
          </div>
        </Link>
      ) : (
        <Link href={`/group/${group.id}`}>
          <div className="btn-continue mt-1 ml-4 mr-5 mb-3 py-2 px-5">
            <img
              src="/images/continue.WebP"
              className="img-btn-enable pb-1 mr-2"
              alt="continue"
            />
            {t("CONTINUE_THE_JOURNEY")}
          </div>
        </Link>
      )}
    </h5>
  )

  const renderGroup = (group, index, previousScenariosCount = 0) => (
    <div className="banner-container-group-card w-20 ml-5 mr-0" key={group.id}>
      <div className="banner-content pt-4">
        <div className="center-content pb-3 w-100">
          <div className="card-head d-flex flex-row py-0 mt-0">
            <h3 className="scenario-title pb-1 mt-4">
              {t("COURSE")} {index + 1} : {group.title}
              <h3 className="scenario-type hidden-ip w-100">
                {group.description}
              </h3>
            </h3>
          </div>

          <div className="card-details w-100">
            <div className="card-core row-fluid d-flex flex-row w-100">
              <ScenarioList
                scenarios={group.senarios}
                groups={groups}
                group={group}
                senarioList={senarioList}
                numScenarios={numScenarios}
                t={t}
                startIndex={previousScenariosCount + 1}
              />
              <div className="column-group-image">
                <img
                  src={process.env.NEXT_PUBLIC_API_URL + group?.badge?.url}
                  className="img-group my-4"
                  alt={group.title}
                />
              </div>
            </div>
          </div>

          {renderGroupFooter(group)}
        </div>
      </div>
    </div>
  )

  // Calculate total score and check starter completion
  let totalScore = 0
  users?.score?.forEach((score) => {
    totalScore +=
      score.score_skill1 + score.score_skill2 + score.score_skill3 + 2
  })

  const isStarterComplete = starterGroups.every(
    (group) =>
      completedGroups.includes(String(group.id)) ||
      completedGroups.includes(group.id)
  )

  // Calculate first passed group index
  const firstPassedGroupIndex =
    completedGroups?.length > 0 ? completedGroups?.length : 0

  return (
    <>
      {isLandscape ? (
        <div className={"space2-" + activeTab}>
          <div className="group-card-page">
            {/* Header Section */}
            <div className="row-hub d-flex w-100 flex-row pl-3">
              <a className="w-40" href="/dashboard">
                <img
                  src="/images/psynarios-logo-light.png"
                  className="logo-index pl-5 ml-5 w-80"
                  alt="logo"
                />
              </a>

              <h2 className="page-title w-50 pt-3 mr-3 pl-4 mt-1">
                Managers <br /> Behaviour Lab
              </h2>

              <img src="/images/hub.png" className="img-hub py-3" alt="hub" />

              <div className="row d-flex w-80 flex-row py-0">
                <h2 className="list-subtitle-name ml-3 pt-3 mt-2">
                  {users.username} 〡 {t("LEVEL")}{" "}
                  {removeLeadingZeros((totalScore / 75).toFixed() + 1)}
                  <br />
                  {t("COLLECTED_STARS")} : {totalScore}
                </h2>
              </div>

              <UserConnection
                t={t}
                avatarSrc={`/images/${users.avatar}.png`}
                userName={users?.username || ""}
              />
            </div>

            {/* Course Type Selector */}
            <div className="parcour-buttons-container mt-5">
              <button
                className={`starter-button px-3 mx-3 py-3 ${
                  activeTab === "starter" ? "active" : ""
                }`}
                onClick={() => setActiveTab("starter")}
              >
                {t("STARTER_PARCOUR")}
              </button>
              <button
                className={`individual-button px-3 mx-3 py-3 ${
                  activeTab === "individual" ? "active" : ""
                }`}
                onClick={() =>
                  individualGroups.length !== 0
                    ? setActiveTab("individual")
                    : setActiveTab("starter")
                }
              >
                {t("INDIVIDUAL_PARCOUR")}
              </button>
              <button
                className={`assimulation-button px-3 mx-3 py-3 ${
                  activeTab === "assimulation" ? "active" : ""
                }`}
                onClick={() => {
                  const isStarterComplete = starterGroups.every(
                    (group) =>
                      completedGroups.includes(String(group.id)) ||
                      completedGroups.includes(group.id)
                  )
                  const isIndividualComplete =
                    individualGroups.length > 0 &&
                    individualGroups.every(
                      (group) =>
                        completedGroups.includes(String(group.id)) ||
                        completedGroups.includes(group.id)
                    )

                  if (
                    isStarterComplete &&
                    isIndividualComplete &&
                    assimulationGroups.length > 0
                  ) {
                    setActiveTab("assimulation")
                  }
                }}
              >
                {t("ASSIMULATION_PARCOUR")}
              </button>
              <button
                className={`bilan-button hidden-ip px-3 mx-3 py-3 ${
                  activeTab === "bilan" ? "active" : ""
                }`}
              >
                {t("BILAN_END_PARCOUR")}
              </button>
            </div>

            {/* Course Content Based on Active Tab */}
            {activeTab === "starter" ? (
              <>
                {/* Starter Course Completion Card */}
                {isStarterComplete && (
                  <div className="banner-container-group-card w-20 ml-5 mr-0 mt-4">
                    <div className="banner-content">
                      <div className="center-content w-100">
                        <div className="congrats-card-container">
                          <div className="confetti">
                            {[...Array(13)].map((_, i) => (
                              <div key={i} className="confetti-piece"></div>
                            ))}
                            <img
                              src="/images/congrats.png"
                              alt="Congratulations"
                              className="w-24 h-24 mb-4"
                            />
                            <h3 className="text-congrats text-2xl font-bold text-center mb-2">
                              {t("CONGRATS_STARTER_COMPLETE")}
                            </h3>
                            <p className="text-congrats-desc text-center text-gray-600 mb-6">
                              {users.comment
                                ? users.comment
                                : t("STARTER_COMPLETE_DESC")}
                            </p>

                            <div className="download-button-container">
                              {users?.showReport && (
                                <button
                                  onClick={handleDownloadReport}
                                  className="download-report-button px-4 py-3 mb-3"
                                >
                                  <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                  >
                                    <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4M7 10l5 5 5-5M12 15V3" />
                                  </svg>
                                  {t("DOWNLOAD_STARTER_REPORT")}
                                </button>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Render Starter Groups */}
                {starterGroups.map((group, index) => {
                  const previousScenariosCount = starterGroups
                    .slice(0, index)
                    .reduce((sum, g) => sum + g.senarios.length, 0)
                  return renderGroup(group, index, previousScenariosCount)
                })}
              </>
            ) : activeTab === "individual" ? (
              <>
                {/* Individual Course Welcome Card */}
                <div className="banner-container-group-card w-20 ml-5 mr-0 mt-4">
                  <div className="banner-content pt-4">
                    <div className="center-content pb-3 w-100">
                      <div className="welcome-card-container">
                        <h3 className="text-congrats font-bold text-center mb-3">
                          {t("WELCOME_IND_PARCOUR")}
                        </h3>
                        <p className="text-congrats-desc text-center text-gray-600 mb-6">
                          {t("IND_PARCOUR_DESC")}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Render Individual Groups */}
                {individualGroups.map((group, index) => {
                  const starterScenariosCount = starterGroups.reduce(
                    (sum, g) => sum + g.senarios.length,
                    0
                  )
                  const previousIndividualScenariosCount = individualGroups
                    .slice(0, index)
                    .reduce((sum, g) => sum + g.senarios.length, 0)
                  return renderGroup(
                    group,
                    index,
                    starterScenariosCount + previousIndividualScenariosCount
                  )
                })}
              </>
            ) : activeTab === "assimulation" ? (
              <>
                {/* Assimulation Course Welcome Card */}
                <div className="banner-container-group-card w-20 ml-5 mr-0 mt-4">
                  <div className="banner-content pt-4">
                    <div className="center-content pb-3 w-100">
                      <div className="welcome-card-container">
                        <h3 className="text-congrats font-bold text-center mb-3">
                          {t("WELCOME_ASS_PARCOUR")}
                        </h3>
                        <p className="text-congrats-desc text-center text-gray-600 mb-6">
                          {t("ASS_PARCOUR_DESC")}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Render Assimulation Groups */}
                {assimulationGroups.map((group, index) => {
                  const starterScenariosCount = starterGroups.reduce(
                    (sum, g) => sum + g.senarios.length,
                    0
                  )
                  const individualScenariosCount = individualGroups.reduce(
                    (sum, g) => sum + g.senarios.length,
                    0
                  )
                  const previousAssimulationScenariosCount = assimulationGroups
                    .slice(0, index)
                    .reduce((sum, g) => sum + g.senarios.length, 0)

                  return renderGroup(
                    group,
                    index,
                    starterScenariosCount +
                      individualScenariosCount +
                      previousAssimulationScenariosCount
                  )
                })}
              </>
            ) : null}
          </div>
          <TawkMessengerWrapper />
        </div>
      ) : (
        <Swap />
      )}

      {/* Notice Modal */}
      {users.notice && (
        <Modal
          id="tutoriel-modal"
          aria-labelledby="simple-modal-title"
          aria-describedby="simple-modal-description"
          open={open}
          onClick={handleClose}
        >
          <div className="disclaimer-column flex-column">
            <div>
              <button
                className="close-button-index pb-2"
                onClick={handleClose}
                aria-label="Close"
              >
                ×
              </button>
            </div>
            <div className="disclaimer-title">{t("LEARNING_NOTICE")}</div>
            <h3 className="group-disclaimer pt-4 mt-2">
              {t("ALL_OUR_SCENARIOS_I")} <br />
              {t("ALL_OUR_SCENARIOS_II")} <br />
              {t("SCENARIOS_ARE_DESIGNED")}
            </h3>
            <h3 className="group-disclaimer mt-0">
              {t("EVERY_COURSE_I")} <br />
              {t("EVERY_COURSE_II")}
            </h3>
            <div className="logo-line mb-2 mt-3 ml-2">
              <a href="/">
                <img
                  src="/images/psynarios-logo-vibrant.png"
                  className="logo"
                  alt="logo"
                />
              </a>
            </div>
          </div>
        </Modal>
      )}
    </>
  )
}

export async function getServerSideProps(context) {
  try {
    const session = await getServerSession(context.req, context.res, options)
    const { res } = context
    const locale = context.locale || "fr"
    let senarioScoreCount = 0
    const senarioList = []
    const groupScore = []

    if (!session || !session?.jwt) {
      res.setHeader("Set-Cookie", [
        "next-auth.session-token=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT",
        "next-auth.csrf-token=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT",
        "__Secure-next-auth.session-token=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT",
      ])

      return {
        redirect: {
          destination: "/login",
          permanent: false,
        },
      }
    }

    try {
      // Fetch user data
      const users = await fetchAPI(
        "/users/me",
        {
          populate: {
            score: {
              populate: "*",
            },
          },
        },
        {
          headers: {
            Authorization: `Bearer ${session.jwt}`,
          },
        }
      )

      // Check onboarding status
      if (users?.onboarding) {
        res.writeHead(302, { Location: "/onboarding" })
        res.end()
        return { props: {} }
      }

      // Fetch groups data
      const group_Object = await fetchAPI(
        `/groups/list/${users?.id}`,
        {},
        {
          headers: {
            Authorization: `Bearer ${session.jwt}`,
          },
        }
      )

      const groups = group_Object.groups
      const numScenarios = group_Object.num
      const initialStartDate = group_Object.initialStart

      // Process groups and scenarios
      groups.forEach((group) => {
        let passedSenarios = []
        let senarios_ids = []
        let senarioScore = []
        let groupPartition = {}
        groupPartition[group.id] = []

        // Get all scenario IDs for this group
        group.senarios.forEach((senario) => {
          senarios_ids.push(senario.id)
        })

        // Check completed scenarios
        users.score?.forEach((score) => {
          if (
            !passedSenarios.includes(score.senarios[0].id) &&
            senarios_ids.includes(score.senarios[0].id)
          ) {
            passedSenarios.push(score.senarios[0].id)
            senarioScore.push(score)
            senarioScoreCount++
            groupPartition[group.id].push(score.senarios[0].id)
          }
        })

        // Check if group is completed
        if (passedSenarios.length === group.senarios.length) {
          groupScore.push({ [group.id]: true })
        } else {
          groupScore.push({ [group.id]: false })
        }

        senarioList.push(groupPartition)
      })

      // Get completed groups
      const completedGroups = groupScore
        .filter((score) => Object.values(score)[0] === true)
        .map((score) => Object.keys(score)[0])


      let charactersData = null

      // Load translations
      const translations = await serverSideTranslations(
        locale,
        ["common"],
        null,
        ["fr", "en"]
      )

      return {
        props: {
          ...translations,
          groups,
          numScenarios,
          users,
          completedGroups,
          senarioList,
          senarioScoreCount,
          initialStartDate,
          charactersData
        },
      }
    } catch (error) {
      if (error.response?.status === 401) {
        return {
          props: {
            error: "UNAUTHORIZED",
          },
        }
      }
      throw error
    }
  } catch (error) {
    console.error("Server-side error:", error)
    context.res.setHeader("Set-Cookie", [
      "next-auth.session-token=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT",
      "next-auth.csrf-token=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT",
      "__Secure-next-auth.session-token=; Path=/; Expires=Thu, 01 Jan 1970 00:00:00 GMT",
    ])

    return {
      redirect: {
        destination: "/login",
        permanent: false,
      },
    }
  }
}

export default withRouter(Index)
