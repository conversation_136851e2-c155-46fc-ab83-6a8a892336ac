import { fetchAPI } from "@/lib/api"
import Link from "next/link"
import React, { useState, useEffect } from "react"
import { getSession } from "next-auth/react"
import { options } from "@/pages/api/auth/[...nextauth]"
import { getServerSession } from "next-auth/next"
import Swap from "@/components/swap"
import UserConnection from "@/components/userConnection"
import { useTranslation } from "next-i18next"
import { serverSideTranslations } from "next-i18next/serverSideTranslations"

const GroupPage = ({
  group,
  numScenarios,
  skills,
  users,
  passedSenarios,
  allGroups,
}) => {
  const { t } = useTranslation("common")
  const [isLandscape, setIsLandscape] = useState(false)

  // Helper function to check if all previous groups are completed
  const isPreviousGroupsCompleted = () => {
    const currentGroupIndex = allGroups.findIndex((g) => g.id === group.id)

    // Get all completed scenario IDs across all groups
    const completedScenarioIds = []
    users.score?.forEach((score) => {
      if (!completedScenarioIds.includes(score.senarios[0].id)) {
        completedScenarioIds.push(score.senarios[0].id)
      }
    })

    // Check if any previous group has uncompleted scenarios
    for (let i = 0; i < currentGroupIndex; i++) {
      const prevGroup = allGroups[i]
      const hasUncompletedScenarios = prevGroup.senarios.some(
        (scenario) => !completedScenarioIds.includes(scenario.id)
      )
      if (hasUncompletedScenarios) {
        return false
      }
    }

    return true
  }

  const renderScenarioCard = (scenario, index) => {
    const isCompleted = passedSenarios.includes(scenario.id)
    const canStartThisGroup = isPreviousGroupsCompleted()
    const firstUncompletedIndex = group.senarios.findIndex(
      (s) => !passedSenarios.includes(s.id)
    )

    // Only show as available if:
    // 1. All previous groups are completed
    // 2. This is the first uncompleted scenario in this group
    // 3. We have scenarios available to play (numScenarios > 0)
    const isAvailable =
      canStartThisGroup && index === firstUncompletedIndex && numScenarios > 0

    return (
      <div className="banner-container-scenario-card" key={scenario.id}>
        <div className="banner-content flex-column py-1">
          <h3 className="scenario-title">
            {scenario.title}
            <h3 className="scenario-type hidden-ip">
              {scenario.type} │ {scenario.difficulty_level}
            </h3>
          </h3>

          <div className="custome-box-image w-80 mb-3">
            <img
              src={process.env.NEXT_PUBLIC_API_URL + scenario.image?.url}
              className="img"
              alt={scenario.title}
            />
          </div>

          {isCompleted ? (
            <Link href={`/senario/preview/${scenario.id}`} className="link">
              <div className="btn-grad mb-3 mt-1 mx-4 py-1 px-2">
                <img
                  src="/images/complete.png"
                  className="img-btn pb-1 mr-2"
                  alt="completed"
                />
                {t("SCENARIO_COMPLETED")}
              </div>
            </Link>
          ) : isAvailable ? (
            <Link href={`/senario/preview/${scenario.id}`} className="link">
              <div className="btn-continue mb-3 mt-1 mx-3 py-1">
                <img
                  src="/images/continue.WebP"
                  className="img-btn pt-1 pb-2 mr-2"
                  alt="continue"
                />
                {t("START")}
              </div>
            </Link>
          ) : (
            <div className="btn-disable mb-3 mx-4 mt-1 py-1 px-1">
              <img
                src="/images/3d-lock.png"
                className="img-lock pb-2 pt-1 mr-2"
                alt="locked"
              />
              {t("LOCKED_SCENARIO")}
            </div>
          )}
        </div>
      </div>
    )
  }

  useEffect(() => {
    const checkOrientation = () => {
      const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent)
      setIsLandscape(
        isMobile ? window.matchMedia("(orientation: landscape)").matches : true
      )
    }

    checkOrientation()
    window.addEventListener("resize", checkOrientation)
    return () => window.removeEventListener("resize", checkOrientation)
  }, [])

  if (!isLandscape) {
    return <Swap />
  }

  // Calculate completion stats
  const completedCount = passedSenarios.length
  const totalCount = group.senarios.length
  const groupScore = Math.round((completedCount / totalCount) * 100)

  return (
    <div className="banner-container-scenario-group">
      <div className="left-content d-flex flex-column w-100">
        <div className="row-fluid-head pl-5 mb-4 d-flex flex-row">
          <a className="w-30" href="/">
            <img
              src="/images/psynarios-logo-light.png"
              className="logo pull-left mt-1 pl-5 ml-5 mt-2 w-50"
              alt="logo"
            />
          </a>
          <h2 className="banner-title mt-0 ml-5 pl-1">
            {group.title}
            <br />
            <p className="banner-subtitle">
              {completedCount === totalCount
                ? t("COMPLETE_COURSE")
                : "En Progression"}{" "}
              | {completedCount} sur {totalCount} {t("SCENARIO")} | Score :{" "}
              {groupScore}%
            </p>
          </h2>
          <UserConnection
            t={t}
            avatarSrc={`/images/${users.avatar}.png`}
            userName={users?.username || ""}
          />
        </div>
      </div>

      <div className="center-content d-flex flex-column w-100 mb-5">
        <div className="card-head d-flex flex-row mx-5 px-5">
          {group.senarios?.map((scenario, index) =>
            renderScenarioCard(scenario, index)
          )}
        </div>
      </div>

      <div className="objective-column flex-column">
        <div className="objective-title">{t("LEARNING_OBJECTIVES")}</div>
        <div className="objective-intro mt-4 ml-3 mb-3">
          {t("ON_COURSE_COMPLETION")}
        </div>
        <div className="objective-content line-break ml-3">
          {group.objective}
        </div>
      </div>

      <div className="skill-column flex-column">
        <div className="skill-title">{t("BEHAVIORS_PRACTICES")}</div>
        <div className="container-fluid-skills mt-4 justify-content-between">
          {skills?.data.map((skill) => (
            <div className="row-fluid pb-3 d-flex flex-row" key={skill.id}>
              <div className="btn mb-1 mr-3 py-3 px-4">
                {skill.attributes.title}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export async function getServerSideProps(context) {
  try {
    const session = await getServerSession(context.req, context.res, options)
    const { params } = context
    const locale = context.locale || "fr"

    if (!session?.jwt) {
      return {
        redirect: {
          destination: "/login",
          permanent: false,
        },
      }
    }

    // Get user data
    const users = await fetchAPI(
      "/users/me",
      {
        populate: {
          score: {
            populate: "*",
          },
        },
      },
      {
        headers: {
          Authorization: `Bearer ${session.jwt}`,
        },
      }
    )

    if (users?.onboarding) {
      return {
        redirect: {
          destination: "/onboarding",
          permanent: false,
        },
      }
    }

    // Get current group data
    const groupData = await fetchAPI(
      `/groups/${params.id}/related/${users.id}`,
      {},
      {
        headers: {
          Authorization: `Bearer ${session.jwt}`,
        },
      }
    )

    // Get all groups
    const allGroupsResponse = await fetchAPI(
      `/groups/list/${users.id}`,
      {},
      {
        headers: {
          Authorization: `Bearer ${session.jwt}`,
        },
      }
    )

    // Get skills
    const skills = await fetchAPI(
      "/skills",
      {
        filters: {
          group_id: params.id,
        },
        populate: "title",
      },
      {
        headers: {
          Authorization: `Bearer ${session.jwt}`,
        },
      }
    )

    // Calculate passed scenarios
    const passedSenarios = []
    const currentGroupScenarioIds = groupData.group.senarios.map((s) => s.id)

    users.score?.forEach((score) => {
      if (
        currentGroupScenarioIds.includes(score.senarios[0].id) &&
        !passedSenarios.includes(score.senarios[0].id)
      ) {
        passedSenarios.push(score.senarios[0].id)
      }
    })

    return {
      props: {
        ...(await serverSideTranslations(locale, ["common"])),
        group: groupData.group,
        numScenarios: groupData.num,
        skills,
        users,
        passedSenarios,
        allGroups: allGroupsResponse.groups,
      },
    }
  } catch (error) {
    console.error("Error in getServerSideProps:", error)
    return {
      redirect: {
        destination: "/login",
        permanent: false,
      },
    }
  }
}

export default GroupPage
