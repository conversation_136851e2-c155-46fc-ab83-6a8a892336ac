/* eslint-disable react/no-unescaped-entities */
import React, { useState, useEffect } from "react"
import { <PERSON><PERSON>, Box, TextField, Chip, <PERSON><PERSON>, Alert } from "@mui/material"
import { styled } from "@mui/material/styles"
import { fetchAPI } from "@/lib/api"
import { createTheme, ThemeProvider } from "@mui/material/styles"

const CustomNotification = styled(Alert)(({ theme }) => ({
  position: "fixed",
  top: "11vh",
  right: "3vh",
  zIndex: 9999,
  maxWidth: "50vh",
  boxShadow: theme.shadows[4],
}))

const theme = createTheme({
  palette: {
    primary: {
      main: "#56B69B",
      contrastText: "#FFF",
    },
    secondary: {
      main: "#EBF4FF",
      contrastText: "#254cc4",
    },
  },
})

const RatingModal = ({ open, onClose, scenarioId, userId, jwt }) => {
  const [rating, setRating] = useState(0)
  const [feedback, setFeedback] = useState("")
  const [selectedChips, setSelectedChips] = useState([])
  const [error, setError] = useState("")
  const [notification, setNotification] = useState({
    open: false,
    message: "",
    severity: "success",
  })

  const chips = [
    "La situation",
    "Les acquis techniques",
    "Les analyses du guide",
    "Les comportements exercés",
  ]

  const ratingLabels = {
    5: "Très bien",
    4: "Pas mal",
    3: "Moyen",
    2: "Mauvais",
    1: "Je veux être rappelé"
  }

  const handleChipToggle = (chip) => {
    setSelectedChips((prev) =>
      prev.includes(chip) ? prev.filter((c) => c !== chip) : [...prev, chip]
    )
  }

  const validateForm = () => {
    if (rating === 0) {
      setError("Veuillez attribuer une note")
      return false
    }

    if (rating < 3 && !feedback.trim()) {
      setError(
        "Pour les notes inférieures à 3 étoiles, merci de nous laisser un commentaire"
      )
      return false
    }

    setError("")
    return true
  }

  const handleSubmit = async () => {
    if (!validateForm()) {
      return
    }

    try {
      const chipsString = selectedChips.join(", ")
      const response = await fetchAPI(
        "/custom-ratings",
        {},
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${jwt}`,
          },
          body: JSON.stringify({
            rating,
            description: feedback,
            chips: chipsString,
            scenarioId,
            userId,
          }),
        }
      )
      console.log("Rating submitted successfully:", response)

      setNotification({
        open: true,
        message: "Merci ! Nous apprécions votre feedback",
        severity: "success",
      })

      onClose()
    } catch (error) {
      console.error("Error submitting rating:", error)

      setNotification({
        open: true,
        message: "Une erreur est survenue. Veuillez réessayer",
        severity: "error",
      })
    }
  }

  useEffect(() => {
    if (notification.open) {
      const timer = setTimeout(() => {
        setNotification({ ...notification, open: false })
      }, 6000)

      return () => clearTimeout(timer)
    }
  }, [notification.open])

  // Reset error when rating or feedback changes
  useEffect(() => {
    setError("")
  }, [rating, feedback])

  return (
    <>
      <Modal className="hidden-ip" open={open} onClose={onClose}>
        <div className="rating-column hidden-ip flex-column">
          <div>
            <button
              className="close-button-rating hidden-ip"
              onClick={onClose}
              aria-label="Close"
            >
              ×
            </button>
          </div>
          <div className="anonymous-title">
            Avez-vous apprécié ce scénario ? Dites-nous tout !
          </div>
          <p className="rating-text">
            Qu'est ce qui vous a plu dans ce scénario ?
          </p>
          <Box sx={{ mt: 2, mb: 2, ml: 1, mr: 1 }}>
            <ThemeProvider theme={theme}>
              {chips.map((chip) => (
                <Chip
                  key={chip}
                  label={chip}
                  onClick={() => handleChipToggle(chip)}
                  color={selectedChips.includes(chip) ? "primary" : "secondary"}
                  sx={{ mr: 1, mb: 1 }}
                />
              ))}
            </ThemeProvider>
          </Box>
          <p className="rating-text">
            Laissez-nous quelques étoiles pour encourager l'équipe
          </p>

          <div className="rating-stars-container ml-3 mb-4 mt-2" style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>            
            <div style={{ display: 'flex' }}>
              {[...Array(5)].map((_, index) => (
                <img
                  key={index}
                  src={
                    index < rating
                      ? "/images/correct-star.png"
                      : "/images/missed.png"
                  }
                  alt={`star-${index + 1}`}
                  onClick={() => setRating(index + 1)}
                  className="rating-star mr-2"
                />
              ))}
            </div>

            {/* Display current rating label after the stars */}
            {rating > 0 && (
              <span className="rating-label" style={{ fontSize: '16px', fontWeight: 'bold', color: '#56B69B' }}>
                {ratingLabels[rating]}
              </span>
            )}
          </div>
          
          <ThemeProvider theme={theme}>
            <TextField
              fullWidth
              margin="normal"
              label="Le scénario en quelques mots"
              color="primary"
              multiline
              rows={4}
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              focused
              required={rating > 0 && rating < 3}
              error={!!error && rating < 3 && !feedback.trim()}
              helperText={error}
            />
          </ThemeProvider>

          <div className="btn-main mt-3" onClick={handleSubmit}>
            Soumettre
          </div>
        </div>
      </Modal>

      {notification.open && (
        <CustomNotification
          severity={notification.severity}
          onClose={() => setNotification({ ...notification, open: false })}
        >
          {notification.message}
        </CustomNotification>
      )}
    </>
  )
}

export default RatingModal