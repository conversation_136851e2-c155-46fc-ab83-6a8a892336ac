export const initialState = {
  user: null,
}

export const setUser = (user) => ({
  type: actionTypes.SET_USER,
  user,
})

export const clearUser = () => ({
  type: actionTypes.CLEAR_USER,
})
export const actionTypes = {
  SET_USER: "SET_USER",
  CLEAR_USER: "CLEAR_USER",
}

export const authReducer = (state, action) => {
  switch (action.type) {
    case actionTypes.SET_USER:
      return {
        ...state,
        user: action.user,
        isAuthenticated: true,
      }
    case actionTypes.CLEAR_USER:
      return {
        ...state,
        user: null,
        isAuthenticated: false,
      }
    default:
      return state
  }
}

export const login = (user) => ({
  type: "LOGIN",
  payload: user,
})

export const logout = () => ({
  type: "LOGOUT",
})
