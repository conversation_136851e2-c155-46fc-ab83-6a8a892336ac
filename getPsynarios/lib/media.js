import { getStrapiURL } from "./api"

export function getStrapiMedia(media) {
  const url = media?.data?.attributes?.url
  if (!url) {
    // Handle the case when the URL is undefined
    return null // Or you can return a default image URL or throw an error
  }

  const imageUrl = url.startsWith("/") ? getStrapiURL(url) : url
  return imageUrl
}

export function getVideoUrl(videoUrl) {
  const fileId = extractFileIdFromUrl(videoUrl)
  return `https://drive.google.com/uc?export=download&id=${fileId}`
}

const extractFileIdFromUrl = (url) => {
  const fileIdRegex = /\/d\/([^/]+)\//
  const matches = url?.match(fileIdRegex)
  if (matches && matches.length > 1) {
    return matches[1]
  }
  return ""
}
