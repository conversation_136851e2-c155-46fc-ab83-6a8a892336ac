import React, { useRef, useState } from "react"
import ReactPlayer from "react-player"

function VideoPlayer({ src }) {
  const [playing, setPlaying] = useState(false)
  const videoRef = useRef(null)

  function handlePlay() {
    setPlaying(true)
  }

  function handlePause() {
    setPlaying(false)
  }

  function handleProgressClick(e) {
    const progressBar = videoRef.current.querySelector(".progress")
    const progressRect = progressBar.getBoundingClientRect()
    const progressWidth = progressRect.width
    const clickX = e.clientX - progressRect.left
    const newTime = (clickX / progressWidth) * videoRef.current.duration
    videoRef.current.currentTime = newTime
  }

  return (
    <div className="video-container">
      <ReactPlayer
        ref={videoRef}
        url={src}
        playing={playing}
        controls
        onPlay={handlePlay}
        onPause={handlePause}
      />
      <div className="progress" onClick={handleProgressClick}>
        <div className="progress-bar" />
      </div>
    </div>
  )
}
export default VideoPlayer
