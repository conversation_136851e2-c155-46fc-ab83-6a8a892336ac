{"name": "psynarios-nextjs", "version": "1.0.2", "private": true, "scripts": {"develop": "next dev", "dev": "next dev --port 3001", "build": "next build", "start": "next start --port 3001", "deploy": "next build && next export", "lint": "next lint", "lint:fix": "next lint --fix"}, "dependencies": {"@material-ui/core": "^4.12.4", "bootstrap": "^5.2.3", "chart.js": "^4.2.1", "formik": "^2.2.9", "js-cookie": "^3.0.1", "lodash": "^4.17.21", "moment": "^2.24.0", "next": "^11.1.4", "next-auth": "^4.0.6", "qs": "^6.10.1", "react": "17.0.2", "react-bootstrap": "^2.7.0", "react-chartjs-2": "^5.2.0", "react-dom": "17.0.2", "react-markdown": "^4.2.2", "react-modal": "^3.16.1", "react-moment": "^0.9.6", "react-player": "^2.11.0", "react-tabs": "^6.0.0", "yup": "^1.0.2"}, "license": "MIT", "devDependencies": {"eslint": "^7.30.0", "eslint-config-next": "^11.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^3.4.0", "nextjs-kodyfire": "^0.1.14", "prettier": "^2.3.1", "react-kodyfire": "^0.1.14", "sass": "^1.57.1"}}