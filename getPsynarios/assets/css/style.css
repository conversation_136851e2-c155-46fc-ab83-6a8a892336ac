a {
  text-decoration: none;
}

h1 {
  
  font-size: 120px;
}

#category {
  font-family: <PERSON><PERSON>;
  font-weight: 500;
}

#title {
  letter-spacing: 0.4px;
  font-size: 22px;
  font-size: 1.375rem;
  line-height: 1.13636;
}

#banner {
  margin: 20px;
  height: 800px;
}

#editor {
  font-size: 16px;
  font-size: 1rem;
  line-height: 1.75;
}

.uk-navbar-container {
  background: #fff !important;
  font-family: <PERSON><PERSON> Franklin;
}

img:hover {
  opacity: 1;
  transition: opacity 0.25s cubic-bezier(0.39, 0.575, 0.565, 1);
}
.row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}
.d-flex {
  display: -webkit-box !important;
  display: -ms-flexbox !important;
  display: flex !important;
}
.col-1, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-10, .col-11, .col-12, .col,
.col-auto, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6, .col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12, .col-sm,
.col-sm-auto, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6, .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12, .col-md,
.col-md-auto, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6, .col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12, .col-lg,
.col-lg-auto, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6, .col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12, .col-xl,
.col-xl-auto {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
}

.col {
  -ms-flex-preferred-size: 0;
      flex-basis: 0;
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
  max-width: 100%;
}
.flex-row {
  -webkit-box-orient: horizontal !important;
  -webkit-box-direction: normal !important;
      -ms-flex-direction: row !important;
          flex-direction: row !important;
}

.flex-column {
  -webkit-box-orient: vertical !important;
  -webkit-box-direction: normal !important;
      -ms-flex-direction: column !important;
          flex-direction: column !important;
}
.bg-white {
  background-color: #fff !important;
}

.m-0 {
  margin: 0 !important;
}

.mt-0,
.my-0 {
  margin-top: 0 !important;
}

.mr-0,
.mx-0 {
  margin-right: 0 !important;
}

.mb-0,
.my-0 {
  margin-bottom: 0 !important;
}

.ml-0,
.mx-0 {
  margin-left: 0 !important;
}

.m-1 {
  margin: 0.25rem !important;
}

.mt-1,
.my-1 {
  margin-top: 0.25rem !important;
}

.mr-1,
.mx-1 {
  margin-right: 0.25rem !important;
}

.mb-1,
.my-1 {
  margin-bottom: 0.25rem !important;
}

.ml-1,
.mx-1 {
  margin-left: 0.25rem !important;
}

.m-2 {
  margin: 0.5rem !important;
}

.mt-2,
.my-2 {
  margin-top: 0.5rem !important;
}

.mr-2,
.mx-2 {
  margin-right: 0.5rem !important;
}

.mb-2,
.my-2 {
  margin-bottom: 0.5rem !important;
}

.ml-2,
.mx-2 {
  margin-left: 0.5rem !important;
}

.m-3 {
  margin: 1rem !important;
}

.mt-3,
.my-3 {
  margin-top: 1rem !important;
}

.mr-3,
.mx-3 {
  margin-right: 1rem !important;
}

.mb-3,
.my-3 {
  margin-bottom: 1rem !important;
}

.ml-3,
.mx-3 {
  margin-left: 1rem !important;
}

.m-4 {
  margin: 1.5rem !important;
}

.mt-4,
.my-4 {
  margin-top: 1.5rem !important;
}

.mr-4,
.mx-4 {
  margin-right: 1.5rem !important;
}

.mb-4,
.my-4 {
  margin-bottom: 1.5rem !important;
}

.ml-4,
.mx-4 {
  margin-left: 1.5rem !important;
}

.m-5 {
  margin: 3rem !important;
}

.mt-5,
.my-5 {
  margin-top: 3rem !important;
}

.mr-5,
.mx-5 {
  margin-right: 3rem !important;
}

.mb-5,
.my-5 {
  margin-bottom: 3rem !important;
}

.ml-5,
.mx-5 {
  margin-left: 3rem !important;
}

.p-0 {
  padding: 0 !important;
}

.pt-0,
.py-0 {
  padding-top: 0 !important;
}

.pr-0,
.px-0 {
  padding-right: 0 !important;
}

.pb-0,
.py-0 {
  padding-bottom: 0 !important;
}

.pl-0,
.px-0 {
  padding-left: 0 !important;
}

.p-1 {
  padding: 0.25rem !important;
}

.pt-1,
.py-1 {
  padding-top: 0.25rem !important;
}

.pr-1,
.px-1 {
  padding-right: 0.25rem !important;
}

.pb-1,
.py-1 {
  padding-bottom: 0.25rem !important;
}

.pl-1,
.px-1 {
  padding-left: 0.25rem !important;
}

.p-2 {
  padding: 0.5rem !important;
}

.pt-2,
.py-2 {
  padding-top: 0.5rem !important;
}

.pr-2,
.px-2 {
  padding-right: 0.5rem !important;
}

.pb-2,
.py-2 {
  padding-bottom: 0.5rem !important;
}

.pl-2,
.px-2 {
  padding-left: 0.5rem !important;
}

.p-3 {
  padding: 1rem !important;
}

.pt-3,
.py-3 {
  padding-top: 1rem !important;
}

.pr-3,
.px-3 {
  padding-right: 1rem !important;
}

.pb-3,
.py-3 {
  padding-bottom: 1rem !important;
}

.pl-3,
.px-3 {
  padding-left: 1rem !important;
}

.p-4 {
  padding: 1.5rem !important;
}

.pt-4,
.py-4 {
  padding-top: 1.5rem !important;
}

.pr-4,
.px-4 {
  padding-right: 1.5rem !important;
}

.pb-4,
.py-4 {
  padding-bottom: 1.5rem !important;
}

.pl-4,
.px-4 {
  padding-left: 1.5rem !important;
}

.p-5 {
  padding: 3rem !important;
}

.pt-5,
.py-5 {
  padding-top: 3rem !important;
}

.pr-5,
.px-5 {
  padding-right: 3rem !important;
}

.pb-5,
.py-5 {
  padding-bottom: 3rem !important;
}

.pl-5,
.px-5 {
  padding-left: 3rem !important;
}
.text-left {
  text-align: left !important;
}

.text-right {
  text-align: right !important;
}

.text-center {
  text-align: center !important;
}

/* GLOBAL */


.cursor {
  cursor: pointer;
}

.mt-50 {
  margin-top: 50px !important;
}

.display-inline {
  display: inline-block !important;
}

html, body {
  font-family: 'cabin', cursive;
  margin: 0;
  height: auto;
}

html a, body a {
  text-decoration: none;
}

html section, body section {
  padding-top: 73px;
  padding-bottom: 83px;
  text-transform: uppercase;
}

html section .section-title, body section .section-title {
  text-align: center;
  font-weight: bold;
  margin-bottom: 73px;
  font-style: normal;
  font-size: 18px;
  line-height: 37px;
  
}

html section .section-title:after, body section .section-title:after {
  content: ' ';
  width: 70px;
  height: 0px;
  border: 4px solid #ff7065;
  display: block;
  margin: 13px auto 32px;
}

html section .section-title .section-desc, body section .section-title .section-desc {
  font-family: cabin;
  font-style: normal;
  font-weight: normal;
  font-size: 18px;
  line-height: 37px;
  text-align: center;
  
}

html section .card, body section .card {
  max-width: 206px;
  height: 320px;
  border-radius: 0px;
  border-width: 0px;
  -webkit-box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
          box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
}

html section .card .card-header, body section .card .card-header {
  border-radius: 0px;
  margin: 4px;
  height: 100px;
  background-color: #0E3B7D;
}

html section .card .card-header img, body section .card .card-header img {
  width: 126px;
  height: 126px;
  margin: 20px auto;
  border-radius: 63px;
  display: block;
  padding: 2px;
  -webkit-box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
          box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
}

html section .card .card-body, body section .card .card-body {
  margin-top: 35px;
  text-align: center;
}

html section .card .card-body .card-title, body section .card .card-body .card-title {
  font-weight: bold;
  font-size: 16px;
  line-height: 37px;
  letter-spacing: 0.05em;
  margin-bottom: 0px;
}

html section .card .card-body .card-text, body section .card .card-body .card-text {
  font-weight: bold;
  font-size: 11px;
  line-height: 24px;
  color: rgba(45, 73, 113, 0.8);
  letter-spacing: 0.05em;
}

html section .card .card-body .company-logo, body section .card .card-body .company-logo {
  width: 67px;
}

html section .card .card-body .expert-title, body section .card .card-body .expert-title {
  font-weight: bold;
  font-size: 13px;
  line-height: 37px;
  color: rgba(45, 73, 113, 0.8);
  margin-top: 4px;
}

/* header */
header {
  height: 100px;
  margin-top: 25px;
  text-transform: uppercase;
}

header a:hover {
  text-decoration: none;
}

header .logo {
  width: 245px;
  height: 46px;
  overflow-y: hidden;
}

header .logo img {
  width: 245px;
}

header .slogan {
  text-transform: uppercase;
}

header ul {
  float: right;
  list-style-type: none;
  margin-bottom: 0px;
  margin-top: 1rem;
}

header ul li {
  display: inline;
  margin: 0 6px;
}

header ul button {
  text-transform: uppercase;
}

.top-menu {
  text-transform: uppercase;
  background-color: #F3F3F3;
  height: 60px;
}

.top-menu ul {
  list-style-type: none;
  line-height: 37px;
  font-size: 14px;
  font-weight: bold;
  margin-top: 11px;
  margin-bottom: 7px;
}

.top-menu ul li {
  display: inline;
  margin-right: 24px;
  margin-left: 24px;
}

/* end header

/* Bootstrap overwrites */
/* Forms */
.form-control {
  border-radius: 10px;
  border-color: #DADADA;
  color: rgba(45, 73, 113, 0.8);
}

/* end boostrap

/* helpers */
.odd-bg {
  background-color: white;
}

.even-bg {
  background-color: #F2F4F7;
}

.ng-mr-1 {
  margin-right: -12px;
}

.ng-ml-1 {
  margin-left: -12px;
}

/* end helpers */
/* end GLOBAL */
/* Home page */
/* Banner */

/* section advantages */
.advantages {
  padding-bottom: 40px;
}

.advantages .row .col-sm-12 {
  text-align: center;
}

.advantages .row .col-sm-12 .save-free-btn {
  color: white;
}

.advantages .row .col-sm-4 {
  text-align: center;
  margin-bottom: 70px;
}

.advantages .row .col-sm-4 img {
  height: 110px;
}

.advantages .row .col-sm-4 img.adjusted-img {
  height: 90px;
  margin-bottom: 20px;
}

.advantages .row .col-sm-4 .subtitle {
  margin-top: 37px;
  font-weight: bold;
  font-size: 18px;
  line-height: 37px;
  color: #0b3c79;
  text-align: center;
  letter-spacing: 0.02em;
}

/* end section advantages */


