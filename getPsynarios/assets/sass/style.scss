@import url("https://fonts.googleapis.com/css2?family=Libre+Franklin:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");
@import "./variables";
@import "./layout/footer";
html,
.sticky-top {
  position: sticky;
  top: 0;
  z-index: 1000; 
  background-color: #fff; 
}
.btn2 {
  background-color: #30225b;
  font-size: 20px !important;
  font-weight: 500;
  color: white;
  border-color: #4e3d7f;
  border-radius: 9px;
  border: 2px solid;
  display: inline-block;
}

.btn-main {
  padding: 2% 0;
  font-size: 15px!important;
  background-image: -webkit-gradient(linear,left top,right top,from(#0567b0),color-stop(60%,rgba(1,57,125,.925)),color-stop(91%,rgba(15,49,108,.953)));
  background-image: -webkit-linear-gradient(left,#0567b0,rgba(1,57,125,.925) 60%,rgba(15,49,108,.953) 91%);
  background-image: -moz-linear-gradient(left,#0567b0 0,rgba(1,57,125,.925) 60%,rgba(15,49,108,.953) 91%);
  background-image: linear-gradient(90deg,#0567b0 0,rgba(1,57,125,.925) 60%,rgba(15,49,108,.953) 91%);
  -webkit-transition: .5s;
  -moz-transition: .5s;
  transition: .5s;
  background-size: 200% auto;
  color: #fff;
  -webkit-box-shadow: 0 0 20px #eee;
  box-shadow: 0 0 20px #eee;
  border-radius: 8px;
  text-align: center;
  background-color: rgba(42,136,116,.1);
  font-weight: 500;
  cursor: pointer;

  .arrow-btn {
    width: 12%!important;
}
@media (max-width: 576px) {
  padding: 3%;
  margin-left: 7%;
  margin-bottom: 0% !important;
  width: 86% !important;
  font-size: 14px !important;
  
    
}

}
.btn2:hover {
  background-color: #30225b;
  color: white;
  transition: 0.5s;
  cursor: pointer;
}
body {
  font-family: "Libre Franklin", sans-serif;
}

.bg-theme {
  background-color: #ffffff;
}

.bg-primary {
  background-color: $primary;
}

.bg-secondary {
  background-color: $secondary;
}

.bg-third {
  background-color: $third;
}
.line-break {
  white-space: pre-wrap;
}
.w-20 {
  width: 20%;
}
.w-30 {
  width: 30%;
}

.w-40 {
  width: 40%;
}

.w-60 {
  width: 60%;
}

.w-70 {
  width: 70%;
}
.w-80 {
  width: 80%;
}
.w-90 {
  width: 90%;
}

.w-100 {
  width: 100%;
}
/* font-size */

.font-10 {
  font-size: 10px !important;
}
.font-12 {
  font-size: 12px !important;
}
.font-13 {
  font-size: 13px !important;
}
.font-14 {
  font-size: 14px !important;
}
.font-15 {
  font-size: 15px !important;
}
.font-16 {
  font-size: 16px !important;
}
.font-17 {
  font-size: 17px !important;
}
.font-18 {
  font-size: 18px !important;
}
.font-20 {
  font-size: 20px !important;
}
/* end font-size */


.visible-xs {
  display: none !important;
}
.d-visible-xs {
  display: none !important;
}
.flex-desktop {
  display: flex;
}
.hidden-xs {
  display: block !important;
}

@media (max-width: 576px) {
 
  .flex-desktop {
    display: none !important;
  }
  .w-xs-100 {
    width: 100% !important;
  }
  .visible-xs {
    display: flex !important;
  }
  .d-visible-xs {
    display: block !important;
  }
  .hidden-xs {
    display: none !important;
  }
  .text-mobile-center {
    text-align: center !important;
  }

}


.uk-navbar-left{
margin-left: 5%;
@media (max-width: 576px) {
  height: 70px !important;
    
}
.logo {
  height: 40px !important;
  @media (max-width: 576px) {
    height: 30px !important;
      
  }
  
}
}
.btn-header {
  
  padding: 2% 0;
  font-size: 15px!important;
  background-image: linear-gradient(to right, #ff4b1f 0%, #fe7b4b  51%, #ff4b1f  100%);
  -webkit-transition: .5s;
  -moz-transition: .5s;
  transition: .5s;
  background-size: 200% auto;
  color: #fff;
  -webkit-box-shadow: 0 0 20px #eee;
  box-shadow: 0 0 20px #eee;
  border-radius: 8px;
  text-align: center;
  background-color: rgba(42,136,116,.1);
  font-weight: 500;
  cursor: pointer;
  margin-left : 74% !important;

  .arrow-btn {
    width: 12%!important;
}

}
.btn-action {
  
  padding: 2% 0;
  background-image: linear-gradient(to right, #ff4b1f 0%, #fe7b4b  51%, #ff4b1f  100%);
  -webkit-transition: .5s;
  -moz-transition: .5s;
  transition: .5s;
  background-size: 200% auto;
  color: #fff;  
  border-radius: 8px;
  text-align: center;
  background-color: rgba(42,136,116,.1);
  font-weight: 500;
  cursor: pointer;
  position: relative;
 

  .arrow-btn {
    width: 12%!important;
}
@media (max-width: 576px) {
  margin-left: 5%;
  margin-bottom: 0% !important;
  width: 90% !important;
  font-size: 14px !important;
  
    
} 
}


/* Landing */
.banner {
  position: relative;
  overflow: hidden;
  user-select: none;
  background-image: linear-gradient(90deg,rgba(241,247,255,.165) 0,rgba(215,229,249,.098) 60%,rgba(196,221,253,.298) 91%);
  @media (max-width: 576px) {
    padding: 2% !important;  
  }

  .banner-container-main {
    
    position: relative;
    padding: 0 0 0 7%;
    background-image: url(/images/covermain.png);    background-size: cover;
    opacity: 100%;
    @media (max-width: 576px) {
      padding: 3%;
      margin-bottom: 1%;
      margin-bottom: 0% !important;
        
    }
    

    .banner-content {
      display: flex;
      flex-direction: row;
      align-items: left !important ;
      text-align: left;
      justify-content: left;
      height: 100%;
      width: 100%;
     

      .banner-title {
        color: #155dab;
    font-size: 36px;
    margin-top: 8%!important;
    line-height: 55px;
    font-weight: 400;
    @media (max-width: 576px) {
      margin-left: 8% !important;
      margin-bottom: 0%;
      line-height: 41px;
      font-size: 23px;
        
    }
        
      }

      .banner-title-bottom {
        color: #372c64;
        font-size: 30px;
        margin-bottom: 30px!important;
        line-height: 25px;
        font-weight: 500;
        @media (max-width: 576px) {
          margin-left: 8% !important;
          line-height: 39px;
          font-size: 22px;
          font-weight: 600;
      }
    }

      .banner-subtitle {
        line-height: 35px;
    font-size: 22px;
    color: #235a76d6;
    font-weight: 400;
    text-align: justify!important;
        @media (max-width: 576px) {
          
          line-height: 27px;
          font-size: 16px;
          margin-left: 8% !important;
          text-align: left!important;
            
        }
      }
    }
    .row {
      
      @media (max-width: 576px) {
        margin-top: 1% !important;
      }
    }
    .btn2 {
      background-color: #30225b;
      font-size: 20px !important;
      font-weight: 500;
      color: white;
      border-color: #4e3d7f;
      border-radius: 9px;
      border: 2px solid;
      display: inline-block;
      @media (max-width: 576px) {
        font-size: 18px !important;
        margin: 5% !important;
        position: center !important;
        justify-content: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        
          
      }
    }
    .btn2:hover {
      background-color: #30225b;
      color: white;
      transition: 0.5s;
      cursor: pointer;
    }
  }

  .banner-container-message {
    
    position: relative;
    padding: 0 0 0 0;
    margin: 4% 10% 0 10%;
    border-radius : 10px 10px 0 0;
    background-image: linear-gradient(90deg,#024980 0,#01397d 60%,#0f316c 91%);
    opacity: 100%;
    @media (max-width: 576px) {
      padding-left: 2% !important;
      margin: 3% 8% 0% 8% !important;
    }
    .banner-content {
      display: flex;
      flex-direction: row;
      align-items: left !important ;
      text-align: left;
      justify-content: left;
      height: 100%;
      width: 100%;
      
     

      .banner-title {
        color: white;
        font-size: 30px;
        margin-top: 2% !important;
        margin-bottom: 2% !important;
        line-height: 45px;
        font-weight: 500;
        @media (max-width: 576px) {
          font-size: 16px; 
          line-height: 25px;
          padding: 2% 6% 2% 6% !important;
        }
      }
      .custome-box-image{
        width: 12%;
      .img {
        width: 100%;
      }
    }
    
    }
  }
  

  
  .banner-container-advantage-right {
    
    position: relative;
    margin: 0% 10% 0% 10%;
    padding-left : 6%;
    padding-right : 6%;

    background-size: cover;
    border-top: 1px solid #b7c3d3;
    border-bottom: 1px solid #b7c3d3;


    @media (max-width: 576px) {
      margin: 0% 4% 0% 4%;
    padding-left : 1%;
    padding-right : 1%;
    border-top: 0px solid #b7c3d3;
    border-bottom: 1px solid #b7c3d348;
    }

    
    .banner-content {
      display: flex;
      flex-direction: row;
      align-items: left !important ;
      text-align: left;
      justify-content: left;
      height: 100%;
      width: 100%;
      @media (max-width: 576px) {
      padding: 1% !important;
    }
      .img {
        border-radius: 5px;
        
    }
    
    
      
      .banner-subtitle {
        color: #372c64;
        font-size: 30px;
        margin-top: 3% !important;
        padding-bottom: 20px !important;
        margin-bottom: 15px !important;
        line-height: 45px;
        font-weight: 500;
        
        border-image: linear-gradient(to right, #90afdc 2%, rgba(0,0,0,0) 45%); 
        border-image-slice: 1;
        @media (max-width: 576px) {
          font-size: 20px; 
          line-height: 30px;
          border-bottom: 0px;
          margin: 1% !important;
          padding: 1% !important;
          
        }
       
      }

      .banner-subtitle-text {
        line-height: 40px;
        color: #28566df5;
        font-weight: normal;
        font-size: 18px !important;
        text-align: justify;

        @media (max-width: 576px) {
          margin: 1% 1% 7% 1%!important;

          padding: 1% !important; 
          font-size: 14px !important; 
          line-height: 23px;
          
        }
      }
    }
    

    
  }

  .banner-container-advantage-left {
    position: relative;
    margin: 0% 10% 0% 10%;
    padding-left : 6%;
    padding-right : 6%;
    @media (max-width: 576px) {
      margin: 0% 4% 0% 4%;
    padding-left : 1%;
    padding-right : 1%;
    border-top: 0px solid #b7c3d3;
    border-bottom: 1px solid #b7c3d348;

    .img-mobile {
      border-radius: 5px;
      
    }
    }

    .banner-content {
      display: flex;
      flex-direction: row;
      align-items: left !important ;
      text-align: left;
      justify-content: left;
      height: 100%;
      width: 100%;
      @media (max-width: 576px) {
        margin: 0% !important; 
      padding-left : 1% !important;
      padding-right : 1% !important;
      }

      .img {
        border-radius: 7px;
        
      }
      

      .left-content {
       
          @media (max-width: 576px) {
            margin: 0% !important; 
            padding: 0% !important; 
          } 
        } 

      .banner-subtitle {
        color: #372c64;
        font-size: 30px;
        margin-top: 3% !important;
        padding-bottom: 20px !important;
        margin-bottom: 15px !important;
        line-height: 45px;
        font-weight: 500;
        border-image: linear-gradient(to right, #90afdc 2%, rgba(0,0,0,0) 45%); 
          border-image-slice: 1;
          @media (max-width: 576px) {
            margin: 1% !important;
            padding: 1% !important;
            font-size: 20px !important; 
          line-height: 30px;
          border-bottom: 0px;
          
          }
       
      }

      .banner-subtitle-text {
        line-height: 40px;
        color: #28566df5;
        font-weight: normal;
        font-size: 18px !important;
        text-align: justify;
        @media (max-width: 576px) {
         margin: 1% 1% 7% 1%!important;
          padding: 1% !important;
          font-size: 14px !important; 
          line-height: 23px;
        }
      }
    }
    
  }
  .banner-container-scenario-types {
    height: 100%;
    width: 100%;

    .banner-content {
      display: flex;
      flex-direction: row;
      height: 100%;
      width: 100%;
      
      padding: 5% 7% 2% 7%;

      .custome-box-image{
        width : 40% !important;
        @media (max-width: 576px) {
          
           width: 50%;
           margin-bottom: 7% !important;
           margin-top: 9% !important;
         }

        }
      .banner-subtitle2 {
        color: #064180;
        font-size: 30px;
        text-align: center;
        line-height: 45px;
        margin-bottom: 5%;
        @media (max-width: 576px) {
          
           font-size: 20px !important; 
           line-height: 30px;
           
         }
      }

      .col-sm12 {
        display: flex;
        flex-direction: column;
        align-items: center;
      }

      

      .reasons-col {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: 0 !important;
        margin-bottom: 10px !important;
        .subtitle-reason {
          text-align: center !important;
          color: #372c64;
          font-weight: 600;
          font-size: 22px !important; 
          @media (max-width: 576px) {
          
            font-size: 18px !important; 
            line-height: 25px;
          }
          
        }
        .desc-reason {
          text-align: center !important;
          color: #346e8b;
          font-size: 17px !important;
          line-height: 30px;

          @media (max-width: 576px) {
          
            font-size: 15px !important; 
            line-height: 25px;
            
          }
        }
      }
    }
  }


  
  .banner-container-tcc-testimony{
    user-select: none;
    position: relative;
    justify-content: center !important;
    margin-bottom: 10% !important;
    margin-right : 10% !important;
    margin-left : 10% !important;
    border-bottom: 1px solid #dae2ed;
    border-top: 1px solid #dae2ed;
    
    @media (max-width: 576px) {
      border-bottom: 0px solid #dae2ed !important;
      margin-left: 4% !important;
      margin-right: 4% !important;
    }
    .banner-content {
      
      
      
      
      text-align: center;
      justify-content: center;
      height: 100%;
      width: 100%;
     
      
      
   
    }
    .column-testimony {
      @media (max-width: 576px) {
    border-bottom: 1px solid #dae2ed;
    

  }
}
    .column1 {
      margin-top: 2%;
      width: 10%;  
      
      align-items: center;
      user-select: none;
      
  
      .custome-box-image {
        vertical-align: top !important;
        margin-top: 0% !important;
        width: 100%;
        align-content:start;
        align-items: start;
        
        }
  
    
        
      }
      .column2 {
        
        width: 100%;  
        margin-top: 2%;
        
    
        
          
        }
        .tcc-title{
          
          color: #372c64;
          font-size: 16px;
          text-align: left;
          line-height: 10px;
          @media (max-width: 576px) {
            font-size: 12px;
            line-height: 17px;
            
          }
          }
         
          .tcc-subtitle{
            text-align: justify!important;
            color:#346e8b;
            font-size: 14px;
            
           padding-bottom : 8px; 
            line-height: 28px;
            @media (max-width: 576px) {
              font-size: 12px;
              line-height: 25px;
              text-align: justify !important;
              border-bottom: 0px solid #dae2ed;
              padding-bottom : 2px; 
              margin-bottom : 0px !important;
              margin-left: 4% !important;
              margin-right: 4% !important;

            }

            }

            
            
  }
.banner-container-tcc-demo{
  margin-top: 3% !important; 
  user-select: none;
  padding-left: 5% !important;
    padding-right: 5% !important;
  @media (max-width: 576px) {
    padding: 1% !important;
    margin: 0% !important;

    
  }
  .banner-content{
      
    @media (max-width: 576px) {
      padding-top: 1% !important;
      
    }
  }
  .column1{
      
    @media (max-width: 576px) {
      padding: 1% !important;
      margin: 0% !important;
    }
  }
  .row-fluid{
    background-image: linear-gradient(90deg,#0b2d71 0,#1a357b 60%,#0f316c 91%);
    border-radius : 12px;
    
    @media (max-width: 576px) {
      padding: 3% !important;
      margin: 3% !important;
    }
    .tcc-offer{
      color:white;
      font-size: 48px;
      line-height: 67px;
    }
    .tcc-offer2{
      color:white;
      font-size: 34px;
      line-height: 62px;
    }
    .tcc-offer3{
      color:white;
      font-size: 30px;
      line-height: 60px;
      @media (max-width: 576px) {
        font-size: 22px;
        line-height: 40px;
      }
    }
    .tcc-offer4{
      color:rgb(184, 223, 255);
      font-size: 30px;
      line-height: 44px;
      @media (max-width: 576px) {
        font-size: 22px;
        line-height: 40px;
      }
      
    }
    .btn2{
      align-items: left;
      background-color: white;
      border-radius: 10px;
      margin-top: 4%;
      color: #4e3d7f;
      font-size: 22px !important;
      font-weight: 500;
      @media (max-width: 576px) {
        padding-left: 8% !important;
        padding-right: 8% !important;
        margin: 0% 6% 10% 6% !important;
        font-size: 20px !important;
        padding-top: 6% !important;
        padding-bottom: 6% !important;
      }
  }
}
}
  














  .banner-container-scenario-card {
    height: 100%;
    width: 100%;

    .banner-content {
      display: flex;
      flex-direction: row;
      height: 80%;
      width: 80%;
      background-color: white;
      margin: 10%;
      border-radius: 20px;

      .card-head {
        background-color:  #30225b !important;
        flex-direction: row;
        height: 100%;
        width: 90%;
        background-color: white;
        margin: 0 6% 1% 4%;
        border-radius: 10px;
        display: flex;

        .scenario-title {
          color: white;
          font-size: 22px;
          margin-top: 2%;
          text-align: left;
          line-height: 33px;
          margin-left: 4%;
        }
        .scenario-type {
          color: #c4ddfd;
          font-size: 16px;

          text-align: left;
          line-height: 37px;
        }

        .card-subhead {
          font-size: 20px;
          margin-top: 3%;

          line-height: 37px;
        }

        .scenario-category {
          color:#155dab;
          background-color: white;
          font-size: 16px;
          border-radius: 4px;
          

          text-align: center;
          line-height: 37px;
          margin-left: 4%;
        }
      }

      .card-details {
        flex-direction: row;
        display: flex;

        .card-core {
          flex-direction: row;
          height: 100%;
          width: 90%;
          background-color: white;
          margin: 0 2% 0 5%;
          border-radius: 20px;

          .banner-subtitle-text2 {
            margin-left: 4%;
            color: #636d73;
            font-size: 16px;
            margin-top: 3%;
            padding-bottom: 3%;
            text-align: left;
            line-height: 42px;
            font-weight: 300;
            border-bottom: 1px solid #cfe3ffbe;
          }

          .btn {
            padding: 2% 0 2% 0;
            font-size: 14px !important;
            background-color: white;
            color: #155dab !important;
            border-color: #4e3d7f;
            border-radius: 4px;
            border: 1px;
            display: inline-block;
            background-color: #f1f7ff;
          
          }
          .btn:hover {
            background-color: #4e3d7f;
            color: white !important;
            transition: 0.7s;
          }

          
        }
       
      }
    }
  }
  
  .row-fluid-logos{
    padding : 0% 7% 2% 7%;

    .custome-box-image-eura{
      
      @media (max-width: 576px) {
        width: 70% !important;

      }
    }
    .custome-box-image-neoma{
      width: 17% !important;
      @media (max-width: 576px) {
        width: 47% !important;

      }
  }
}
}

/* landing */






  

  
/* footer */
.footer-clean {
  padding: 10px 50px 10px 120px;
  background-image: linear-gradient(90deg,#0f316c 0,#01397d 60%,#0f316c 91%);

  color: white;
  user-select: none;
}

.footer-clean h3 {
  margin: 0px !important;
  
  font-weight: 400;
  font-size: 14px;
  color: white;
  opacity : 0.7;
  user-select: none;
}

.footer-clean ul {
  padding: 0;
  list-style: none;
  line-height: 1.6;
  font-size: 14px;
  margin-bottom: 0;
}

.footer-clean ul a {
  color: inherit;
  text-decoration: none;
  opacity: 0.8;
}

.footer-clean ul a:hover {
  opacity: 1;
}


.footer-clean .copyright {
  margin-top: 14px;
  margin-bottom: 0;
  font-size: 13px;
  opacity: 0.7;
}
/* end footer */






.cookie-notice {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-image: linear-gradient(90deg,#0f316c 0,#01397d 60%,#0f316c 91%);
  padding: 0px;
  font-size: 13px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  opacity: 0.8;
  z-index: 9999;
}

.cookie-notice.hidden {
  display: none;
}

.close-button {
  background-color: #0f316c;
  color: #fff;
  border: none;
  font-size: 18px;
  cursor: pointer;
}


  
.phrase {
  margin-left: 7%;
  margin-top: 1%;

}





@import "bootstrap/scss/bootstrap.scss";
