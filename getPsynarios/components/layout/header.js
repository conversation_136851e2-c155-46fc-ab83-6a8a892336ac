/* eslint-disable @next/next/no-img-element */
import React, { useEffect } from "react"
import Link from "next/link"
import { signOut, useSession } from "next-auth/react"

const Header = ({ categories }) => {
  const { data: session } = useSession()
  useEffect(() => {
    console.log("session", session)
    if (session == null) return
    console.log("session.jwt", session.jwt)
  }, [session])
  return (
    <div className="sticky-top">
      <nav className="uk-navbar-container" data-uk-navbar>
        <div className="uk-navbar-left">
          <ul className="uk-navbar-nav">
            <li>
              <a href="/">
                <img src="/images/logo.png" className="logo" alt="logo" />
              </a>
            </li>
          </ul>
        </div>
        <div className="uk-navbar-right mr-4 hidden-xs">
          <Link href="https://calendly.com/psynarios/30min">
            <a target="_blank" rel="noopener noreferrer">
            <div className="btn-header w-20 px-1 py-1 mr-5 mt-3 hidden-xs font-18">
              <img
                src="/images/continue.png"
                className="arrow-btn pb-2 mr-2 mt-1"
              />
              Demander une démo
            </div>
            </a>
          </Link>
        </div>
      </nav>
    </div>
  )
}

export default Header
