import React, { useState, useEffect } from "react"
import { fetchAPI } from "@/lib/api"
import Link from "next/link"
import Layout from "@/components/layout/layout"
function Response({ question }) {
  console.log(question)
  const [data, setData] = useState([])
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(async () => {
    const responses = await fetchAPI("/responses", {
      populate: "*",
    })

    const filtered = responses.data.filter((response) => {
      return response.attributes.question.data.id == question.id
    })
    console.log(filtered)
    setData(filtered)
  }, [question.id])
  console.log(data)

  return (
    <>
      {question?.attributes.response_type == "video" ? (
        <div className="container-fluid ull-width d-flex flex-column justify-content-between">
          <div className="row-fluid mt-4 ml-3 d-flex flex-row ">
            <video
              controls="play"
              src={
                "http://localhost:1337" +
                data[0]?.attributes.video.data.attributes.url
              }
              className="video-suggestion"
            >
              {" "}
            </video>{" "}
            <video
              controls="play"
              src={
                "http://localhost:1337" +
                data[1]?.attributes.video.data.attributes.url
              }
              className="video-suggestion"
            >
              {" "}
            </video>{" "}
            <video
              controls="play"
              src={
                "http://localhost:1337" +
                data[2]?.attributes.video.data.attributes.url
              }
              className="video-suggestion"
            >
              {" "}
            </video>
          </div>
        </div>
      ) : (
        <div className="container-fluid ull-width d-flex flex-column justify-content-between">
          <div className="row-fluid-text my-3 d-flex flex-row w-80">
            {" "}
            {data[0]?.attributes.text}{" "}
          </div>
          <div className="row-fluid-text my-3 d-flex flex-row w-80">
            {" "}
            {data[1]?.attributes.text}{" "}
          </div>
          <div className="row-fluid-text my-3 d-flex flex-row w-80">
            {" "}
            {data[2]?.attributes.text}{" "}
          </div>
        </div>
      )}
    </>
  )
}
export default Response
