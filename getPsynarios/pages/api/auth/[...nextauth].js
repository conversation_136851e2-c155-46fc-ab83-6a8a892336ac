import NextAuth from "next-auth"
import GoogleProvider from "next-auth/providers/google"
import CredentialsProvider from "next-auth/providers/credentials"

export const options = {
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    }),
    CredentialsProvider({
      // The name to display on the sign in form (e.g. "Sign in with...")
      name: "Credentials",
      // The credentials is used to generate a suitable form on the sign in page.
      // You can specify whatever fields you are expecting to be submitted.
      // e.g. domain, username, password, 2FA token, etc.
      // You can pass any HTML attribute to the <input> tag through the object.
      credentials: {
        username: { label: "Username", type: "text", placeholder: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials, req) {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_URL}/api/auth/local`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify(credentials),
          }
        )
        const data = await response.json()

        if (!data.error) {
          let { user, jwt } = data
          user.jwt = jwt
          // Any object returned will be saved in `user` property of the JWT
          return user
        } else {
          // You can also Reject this callback with an Error thus the user will be sent to the error page with the error message as a query parameter
          if (data.error) throw new Error(JSON.stringify(data.error))

          // If you return null then an error will be displayed advising the user to check their details.
          return null
        }
      },
    }),
  ],
  database: process.env.NEXT_PUBLIC_DATABASE_URL,
  session: {
    jwt: true,
  },
  secret: process.env.JWT_SECRET,
  callbacks: {
    session: async ({ session, token, user }) => {
      session.id = token.id
      session.jwt = token.jwt
      return Promise.resolve(session)
    },
    jwt: async ({ token, user }) => {
      const isSignIn = user ? true : false
      if (isSignIn) {
        token.jwt = user.jwt
        token.id = user.id
        token.name = user.username
        token.email = user.email
      }
      return Promise.resolve(token)
    },
  },
}

const Auth = (req, res) => NextAuth(req, res, options)

export default Auth
