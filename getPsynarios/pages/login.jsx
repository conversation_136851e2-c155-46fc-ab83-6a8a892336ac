import React, { useState, useContext } from "react"
import { getProviders, signIn, getSession, getCsrfToken } from "next-auth/react"
import { useFormik } from "formik"
import * as yup from "yup"
import { useRouter } from "next/router"
import { AuthContext } from "@/lib/authcontext"
import { setUser } from "@/lib/authreducer"
import { fetchAPI } from "@/lib/api"
import Cookies from "js-cookie"

const Login = ({ providers, csrfToken }) => {
  const router = useRouter()
  const { dispatch } = useContext(AuthContext)
  const [error, setError] = useState("")

  const [loading, setLoading] = useState(false)
  const initialValues = {
    email: "",
    password: "",
  }
  const formSchema = yup.object().shape({
    email: yup.string().email("invalid email").required("${path} is required"),
    password: yup.string().required("${path} is required"),
  })
  const signin = async (values) => {
    try {
      const data = {
        redirect: false,
        identifier: values.email,
        password: values.password,
      }

      setLoading(true)
      const res = await signIn("credentials", data)
      let { error } = res
      // display the error message if any
      if (error) {
        error = JSON.parse(error)
        displayError(error)
      } else {
        dispatch(setUser(data.identifier))
        setError(null)
        router.push("/")
      }
    } catch (error) {
      setLoading(false)
      displayError(error)
    }
  }

  const { values, errors, touched, handleBlur, handleChange, handleSubmit } =
    useFormik({
      onSubmit: signin,
      initialValues,
      validationSchema: formSchema,
    })

  const displayError = (error) => {
    const { message } = error ?? {}
    if (typeof message === "string") {
      setError(message)
    } else {
      if (Array.isArray(message)) {
        setError(t(message[0].messages[0].id) ?? "Une erreur s'est produite.")
      } else {
        setError("Une erreur s'est produite.")
      }
    }
  }

  return (
    <>
      <div id="textbox">
        <div className="row">
          <div className="right col-sm">
            <div id="ic">
              <h2>Connexion</h2>
              <h3>Découvrez nos nouveaux scénarios. </h3>
              <form
                name="login-form"
                id="sidebar-user-login"
                method="post"
                onSubmit={handleSubmit}
              >
                <input
                  type="hidden"
                  name="csrfToken"
                  defaultValue={csrfToken}
                />
                {error && (
                  <div
                    className="alert alert-danger alert-dismissible fade show"
                    role="alert"
                  >
                    {error}
                  </div>
                )}
                <div className="form-group">
                  <label
                    className="control-label"
                    htmlFor="inputNormal"
                  ></label>
                  <input
                    name="email"
                    className="bp-suggestions pl-3 form-control"
                    placeholder="email"
                    type="email"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    value={values.email || ""}
                    // eslint-disable-next-line react/no-unknown-property
                    error={(!!touched.email && !!errors.email) || ""}
                  />
                </div>
                <div className="form-group soninpt">
                  <label
                    className="control-label"
                    htmlFor="inputNormal"
                  ></label>
                  <input
                    type="password"
                    name="password"
                    className="bp-suggestions pl-3 form-control"
                    placeholder="mot de passe"
                    onBlur={handleBlur}
                    onChange={handleChange}
                    value={values.password || ""}
                    // eslint-disable-next-line react/no-unknown-property
                    error={(!!touched.password && !!errors.password) || ""}
                  />
                </div>
                <button
                  type="submit"
                  value="Se connecter"
                  {...(loading === true && "disabled")}
                  className="girisbtn"
                  tabIndex="100"
                >
                  <span className="ml-2">Se connecter</span>
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

Login.getInitialProps = async (context) => {
  const { req, res } = context
  //const lastPageVisited = req.session.lastPageVisited || '/'

  const session = await getSession({ req })

  if (session && res && session.jwt) {
    const users = await fetchAPI(
      "/users/me",
      {},
      {
        headers: {
          Authorization: `Bearer ${session.jwt}`,
        },
      }
    ).catch((error) => {
      console.log(error)

      return null
    })
    if (users.give_survey) {
      res.writeHead(302, { Location: "/survey" })
      res.end()
      return
    } else {
      res.writeHead(302, { Location: "/dashboard" })
      res.end()
      return
    }
  }
  return {
    session: undefined,
    providers: await getProviders(),
    csrfToken: await getCsrfToken(),
  }
}

export default Login
