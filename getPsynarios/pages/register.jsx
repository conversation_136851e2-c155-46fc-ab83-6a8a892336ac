/* eslint-disable react/no-unescaped-entities */
import React from "react"

const Register = () => {
  return (
    <>
      <div id="textbox">
        <div className="row">
          <div className="left col-sm">
            <div id="ic">
              <h2>Identification</h2>
              <h3>Devenez manager leader ave<PERSON>. </h3>
              <form
                name="signup_form"
                id="signup_form"
                method="post"
                encType="multipart/form-data"
                onSubmit={() => {}}
              >
                <div className="form-group">
                  <label className="control-label" htmlFor="inputNormal">
                    Prénom
                  </label>
                  <input
                    type="text"
                    name="signup_username"
                    id="signup_username"
                    className="bp-suggestions form-control"
                    cols="50"
                    rows="10"
                  ></input>
                </div>
                <div className="form-group">
                  <label className="control-label" htmlFor="inputNormal">
                    Nom
                  </label>
                  <input
                    type="text"
                    name="field_1"
                    id="field_1"
                    value=""
                    className="bp-suggestions form-control"
                    cols="50"
                    rows="10"
                  ></input>
                </div>
                <div className="form-group">
                  <label className="control-label" htmlFor="inputNormal">
                    Email
                  </label>
                  <input
                    type="text"
                    name="signup_email"
                    id="signup_email"
                    className="bp-suggestions form-control"
                    cols="50"
                    rows="10"
                  ></input>
                </div>
                <div className="form-group">
                  <label className="control-label" htmlFor="inputNormal">
                    Mot de passe
                  </label>
                  <input
                    type="password"
                    name="signup_password"
                    id="signup_password"
                    value=""
                    className="bp-suggestions form-control"
                    cols="50"
                    rows="10"
                  ></input>
                </div>
                <div className="form-group">
                  <label className="control-label" htmlFor="inputNormal">
                    Entreprise
                  </label>
                  <input
                    type="text"
                    name="field_2"
                    id="field_2"
                    className="bp-suggestions form-control"
                    cols="50"
                    rows="10"
                  ></input>
                </div>
                <input
                  type="submit"
                  name="signup_submit"
                  id="signup_submit"
                  value="S'identifier"
                  className="girisbtn"
                />
                <p>
                  En vous identifiant vous acceptez les{" "}
                  <a href="./legal">conditions générales d'utilisation</a>{" "}
                </p>
              </form>
            </div>
          </div>

          <div className="right col-sm">
            <div id="ic">
              <h2>Connexion</h2>
              <h3>Découvrez nos nouveaux scénarios. </h3>
              <form
                name="login-form"
                id="sidebar-user-login"
                method="post"
                onSubmit={() => {}}
              >
                <div className="form-group">
                  <label className="control-label" htmlFor="inputNormal">
                    Email
                  </label>
                  <input
                    type="text"
                    name="log"
                    className="bp-suggestions form-control"
                    cols="50"
                    rows="10"
                  ></input>
                </div>
                <div className="form-group soninpt">
                  <label className="control-label" htmlFor="inputNormal">
                    Mot de passe
                  </label>
                  <input
                    type="password"
                    name="pwd"
                    className="bp-suggestions form-control"
                    cols="50"
                    rows="10"
                  ></input>
                </div>
                <p>mot de passe oublié </p>
                <input
                  type="submit"
                  value="Se connecter"
                  className="girisbtn"
                  tabIndex="100"
                />
              </form>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

export default Register
