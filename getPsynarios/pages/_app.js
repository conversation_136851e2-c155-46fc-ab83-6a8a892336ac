import App from "next/app"
import Head from "next/head"
import "@/assets/css/style.css"
import "@/assets/sass/style.scss"
import { createContext, useEffect, useState } from "react"
import { fetchAPI } from "@/lib/api"
import { getStrapiMedia } from "@/lib/media"
import { SessionProvider, useSession } from "next-auth/react"
import { AuthProvider } from "@/lib/authcontext"

// Store Strapi Global object in context
export const GlobalContext = createContext({})

const MyApp = ({ Component, pageProps: { session, ...pageProps } }) => {
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    // Check if the user agent matches a mobile device pattern
    const isMobileDevice =
      /Mobi|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
      )
    setIsMobile(isMobileDevice)
  }, [])

  
  const { global } = pageProps
  console.log(session)
  return (
    <AuthProvider>
      <SessionProvider session={session}>
        <Head>
          <link
            rel="shortcut icon"
            href={getStrapiMedia(global?.attributes.favicon)}
          />
        </Head>
        <GlobalContext.Provider value={global?.attributes}>
          {Component.auth ? (
            <Auth>
              <Component {...pageProps} />
            </Auth>
          ) : (
            <Component {...pageProps} />
          )}
        </GlobalContext.Provider>
      </SessionProvider>
    </AuthProvider>
  )
}

function Auth({ children }) {
  // if `{ required: true }` is supplied, `status` can only be "loading" or "authenticated"
  const { status } = useSession({ required: true })

  if (status === "loading") {
    return <div>Chargement en cours...</div>
  }

  return children
}

// getInitialProps disables automatic static optimization for pages that don't
// have getStaticProps. So article, category and home pages still get SSG.
// Hopefully we can replace this with getStaticProps once this issue is fixed:
// https://github.com/vercel/next.js/discussions/10949
MyApp.getInitialProps = async (ctx) => {
  // Calls page's `getInitialProps` and fills `appProps.pageProps`
  const appProps = await App.getInitialProps(ctx)
  // Fetch global site settings from Strapi
  const globalRes = await fetchAPI("/global", {
    populate: {
      favicon: "*",
      defaultSeo: {
        populate: "*",
      },
    },
  })
  // Pass the data to our page via props
  return { ...appProps, pageProps: { global: globalRes.data } }
}

export default MyApp
