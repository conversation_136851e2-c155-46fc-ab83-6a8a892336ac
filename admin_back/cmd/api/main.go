package main

import (
	"context"
	"net/http"
	"os"
	"time"

	"github.com/psynarios/admin_back/internal/api"
	"github.com/psynarios/admin_back/internal/api/handlers"
	"github.com/psynarios/admin_back/internal/api/middleware"
	"github.com/psynarios/admin_back/internal/api/routes"
	"github.com/psynarios/admin_back/internal/auth/repository"
	"github.com/psynarios/admin_back/internal/auth/service"
	"github.com/psynarios/admin_back/internal/common/config"
	"github.com/psynarios/admin_back/internal/common/database"
	"github.com/psynarios/admin_back/internal/common/logger"
	companyRepo "github.com/psynarios/admin_back/internal/company/repository/postgres"
	companyService "github.com/psynarios/admin_back/internal/company/service"
	mailerRepo "github.com/psynarios/admin_back/internal/mailer/repository/postgres"
	mailerService "github.com/psynarios/admin_back/internal/mailer/service"
	scraperService "github.com/psynarios/admin_back/internal/scraper/service"
	templatingRepo "github.com/psynarios/admin_back/internal/templating/repository/postgres"
	templatingService "github.com/psynarios/admin_back/internal/templating/service"
	"github.com/psynarios/admin_back/pkg/utils"
)

func main() {
	// Initialize logger
	log := logger.Initialize(config.LoggerConfig{
		Level:      os.Getenv("LOG_LEVEL"),
		TimeFormat: time.RFC3339,
		Console:    true,
		File:       "logs/api.log",
	})

	// Load configuration
	cfg, err := config.LoadConfig(log)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to load configuration")
	}

	// Initialize database
	db, err := database.NewPostgresDB(database.Config{
		Host:     cfg.Database.Host,
		Port:     cfg.Database.Port,
		User:     cfg.Database.User,
		Password: cfg.Database.Password,
		DBName:   cfg.Database.DBName,
		SSLMode:  cfg.Database.SSLMode,
	})

	if err != nil {
		log.Fatal().Err(err).Msg("Failed to connect to database")
	}

	// Initialize encryption for secure password storage
	if err := utils.InitEncryption(); err != nil {
		log.Fatal().Err(err).Msg("Failed to initialize encryption")
	}

	defer db.Close()

	// Initialize repositories
	userRepo := repository.NewPostgresUserRepository(db)
	tokenRepo := repository.NewTokenRepository(db, log)
	companyRepo := companyRepo.NewCompanyRepository(db.GetPool(), log)
	mailRepo := mailerRepo.NewMailRepository(db.GetPool(), log)
	templateGroupRepo := templatingRepo.NewTemplateGroupRepository(db.GetPool(), log)
	templateRepo := templatingRepo.NewTemplateRepository(db.GetPool(), log)

	// Initialize services
	cookieConfig := service.CookieConfig{
		Domain:   cfg.Auth.CookieDomain,
		Path:     "/",
		MaxAge:   int(cfg.Auth.AccessLifetime.Seconds()),
		Secure:   cfg.Auth.CookieSecure,
		HTTPOnly: cfg.Auth.CookieHTTPOnly,
		SameSite: http.SameSiteStrictMode,
	}

	authService := service.NewAuthService(
		userRepo,
		tokenRepo,
		cfg.Auth.AccessSecret,
		cfg.Auth.RefreshSecret,
		cfg.Auth.AccessLifetime,
		cfg.Auth.RefreshLifetime,
		cookieConfig,
		log,
	)

	userService := service.NewUserService(userRepo, log)
	companyService := companyService.NewCompanyService(companyRepo, log)
	mailService := mailerService.NewMailService(mailRepo, log)
	templateService := templatingService.NewTemplateService(templateRepo, templateGroupRepo, log)

	// Import the scraper service package
	scraperService := scraperService.NewScraperService(
		companyRepo,
		mailService,
		templateService,
		log,
	)

	// Start a background token cleanup job
	go func() {
		ticker := time.NewTicker(24 * time.Hour) // Run once per day
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				ctx := context.Background()
				err := tokenRepo.CleanExpiredTokens(ctx)
				if err != nil {
					log.Error().Err(err).Msg("Failed to clean expired tokens")
				} else {
					log.Info().Msg("Successfully cleaned expired tokens")
				}
			}
		}
	}()

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(authService, log)
	userHandler := handlers.NewUserHandler(userService, log)
	companyHandler := handlers.NewCompanyHandler(companyService, log)
	mailHandler := handlers.NewMailHandler(mailService, log)
	templateHandler := handlers.NewTemplateHandler(templateService, log)
	scraperHandler := handlers.NewScraperHandler(scraperService, log)

	// Create middleware instances
	authMiddleware := middleware.NewAuthMiddleware(authService, log)

	// Setup router
	router := routes.SetupRouter(
		log,
		authHandler,
		userHandler,
		companyHandler,
		mailHandler,
		templateHandler,
		scraperHandler,
		authMiddleware,
	)

	// Create and start server
	server := api.NewServer(router, cfg.Server, log)
	if err := server.Start(); err != nil {
		log.Fatal().Err(err).Msg("Server failed")
	}
}
