// cmd/admin/main.go
package main

import (
	"net/http"
	"os"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/psynarios/admin_back/internal/api"
	"github.com/psynarios/admin_back/internal/api/handlers"
	"github.com/psynarios/admin_back/internal/api/middleware"
	"github.com/psynarios/admin_back/internal/auth/repository"
	"github.com/psynarios/admin_back/internal/auth/service"
	"github.com/psynarios/admin_back/internal/common/config"
	"github.com/psynarios/admin_back/internal/common/database"
	"github.com/psynarios/admin_back/internal/common/logger"
)

func main() {
	// Initialize logger
	log := logger.Initialize(config.LoggerConfig{
		Level:      os.Getenv("LOG_LEVEL"),
		TimeFormat: time.RFC3339,
		Console:    true,
		File:       "logs/admin.log",
	})

	// Load configuration
	cfg, err := config.LoadConfig(log)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to load configuration")
	}

	// Override server port for admin
	cfg.Server.Port = os.Getenv("ADMIN_PORT")
	if cfg.Server.Port == "" {
		cfg.Server.Port = "8081" // Different port from the main API
	}

	// Initialize database
	db, err := database.NewPostgresDB(database.Config{
		Host:     cfg.Database.Host,
		Port:     cfg.Database.Port,
		User:     cfg.Database.User,
		Password: cfg.Database.Password,
		DBName:   cfg.Database.DBName,
		SSLMode:  cfg.Database.SSLMode,
	})

	if err != nil {
		log.Fatal().Err(err).Msg("Failed to connect to database")
	}

	defer db.Close()

	// Initialize repositories
	userRepo := repository.NewPostgresUserRepository(db)
	tokenRepo := repository.NewTokenRepository(db, log)

	// Initialize services
	cookieConfig := service.CookieConfig{
		Domain:   cfg.Auth.CookieDomain,
		Path:     "/",
		MaxAge:   int(cfg.Auth.AccessLifetime.Seconds()),
		Secure:   cfg.Auth.CookieSecure,
		HTTPOnly: cfg.Auth.CookieHTTPOnly,
		SameSite: http.SameSiteStrictMode,
	}

	authService := service.NewAuthService(
		userRepo,
		tokenRepo,
		cfg.Auth.AccessSecret,
		cfg.Auth.RefreshSecret,
		cfg.Auth.AccessLifetime,
		cfg.Auth.RefreshLifetime,
		cookieConfig,
		log,
	)

	userService := service.NewUserService(userRepo, log)

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(authService, log)
	userHandler := handlers.NewUserHandler(userService, log)

	// Setup router with admin-only routes
	router := gin.New()
	router.Use(middleware.Recovery(log))
	router.Use(middleware.Logger(log))
	router.Use(middleware.CORS())

	// Admin-specific routes
	adminRoutes := router.Group("/admin")

	// Auth routes for admin
	auth := adminRoutes.Group("/auth")
	auth.POST("/login", authHandler.Login)

	// Authenticate all other admin routes
	authMiddleware := middleware.NewAuthMiddleware(authService, log)
	adminRoutes.Use(authMiddleware.Authenticate())
	adminRoutes.Use(authMiddleware.RequireRole("admin"))

	// Admin dashboard routes
	adminRoutes.GET("/dashboard", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"message": "Admin dashboard",
		})
	})

	// User management
	users := adminRoutes.Group("/users")
	users.GET("", userHandler.GetUsers)
	users.POST("", userHandler.CreateUser)
	users.GET("/:id", userHandler.GetUser)
	users.PUT("/:id", userHandler.UpdateUser)
	users.DELETE("/:id", userHandler.DeleteUser)

	// Create and start server
	server := api.NewServer(router, cfg.Server, log)
	if err := server.Start(); err != nil {
		log.Fatal().Err(err).Msg("Admin server failed")
	}
}
