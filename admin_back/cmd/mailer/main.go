// cmd/mailer/main.go
package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/psynarios/admin_back/internal/common/config"
	"github.com/psynarios/admin_back/internal/common/database"
	"github.com/psynarios/admin_back/internal/common/logger"
	companyRepo "github.com/psynarios/admin_back/internal/company/repository/postgres"
	companyService "github.com/psynarios/admin_back/internal/company/service"
	mailerRepo "github.com/psynarios/admin_back/internal/mailer/repository/postgres"
	mailerService "github.com/psynarios/admin_back/internal/mailer/service"
	templatingRepo "github.com/psynarios/admin_back/internal/templating/repository/postgres"
	templatingService "github.com/psynarios/admin_back/internal/templating/service"
)

func main() {
	// Initialize logger
	log := logger.Initialize(config.LoggerConfig{
		Level:      os.Getenv("LOG_LEVEL"),
		TimeFormat: time.RFC3339,
		Console:    true,
		File:       "logs/mailer.log",
	})

	// Load configuration
	cfg, err := config.LoadConfig(log)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to load configuration")
	}

	// Initialize database
	db, err := database.NewPostgresDB(database.Config{
		Host:     cfg.Database.Host,
		Port:     cfg.Database.Port,
		User:     cfg.Database.User,
		Password: cfg.Database.Password,
		DBName:   cfg.Database.DBName,
		SSLMode:  cfg.Database.SSLMode,
	})

	if err != nil {
		log.Fatal().Err(err).Msg("Failed to connect to database")
	}

	defer db.Close()

	// Initialize repositories
	companyRepo := companyRepo.NewCompanyRepository(db.GetPool(), log)
	mailRepo := mailerRepo.NewMailRepository(db.GetPool(), log)
	templateGroupRepo := templatingRepo.NewTemplateGroupRepository(db.GetPool(), log)
	templateRepo := templatingRepo.NewTemplateRepository(db.GetPool(), log)

	// Initialize services
	companySvc := companyService.NewCompanyService(companyRepo, log)
	mailSvc := mailerService.NewMailService(mailRepo, log)
	templateSvc := templatingService.NewTemplateService(templateRepo, templateGroupRepo, log)

	// Create a simplified email processor without scraper
	emailProcessor := mailerService.NewSimpleEmailProcessor(
		companySvc,
		mailSvc,
		templateSvc,
		log,
	)

	// Listen for OS signals
	shutdown := make(chan os.Signal, 1)
	signal.Notify(shutdown, os.Interrupt, syscall.SIGTERM)

	// Create processing context
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Start email processing
	log.Info().Msg("Email processor starting")
	go emailProcessor.Start(ctx)

	// Wait for shutdown signal
	<-shutdown
	log.Info().Msg("Shutdown signal received, stopping email processor")

	// Cancel the context to signal shutdown
	cancel()

	// Allow time for graceful shutdown
	shutdownTimeout := 30 * time.Second
	log.Info().Msgf("Allowing %s for graceful shutdown", shutdownTimeout)
	time.Sleep(shutdownTimeout)

	log.Info().Msg("Email processor stopped")
}
