CREATE TABLE IF NOT EXISTS template_groups (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    day_number INTEGER NOT NULL,
    description TEXT,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create a junction table for many-to-many relationship
CREATE TABLE IF NOT EXISTS template_group_companies (
    template_group_id UUID NOT NULL REFERENCES template_groups(id) ON DELETE CASCADE,
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    PRIMARY KEY (template_group_id, company_id)
);

-- Create index for efficient lookups
CREATE INDEX idx_template_group_companies ON template_group_companies(template_group_id, company_id);
