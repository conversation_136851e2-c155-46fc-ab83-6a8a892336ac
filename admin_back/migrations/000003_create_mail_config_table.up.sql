CREATE TABLE IF NOT EXISTS mail_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    company_id UUID NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
    sender_email VARCHAR(255) NOT NULL,
    sender_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    smtp_host VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    smtp_port INTEGER NOT NULL,
    smtp_username VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    smtp_password VARCHAR(255) NOT NULL,
    use_ssl BOOLEAN NOT NULL DEFAULT TRUE,
    active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

CREATE UNIQUE INDEX idx_active_mail_config 
    ON mail_configs (company_id) 
    WHERE active = TRUE;