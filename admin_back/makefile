BINARY_NAME=admin-dashboard

.PHONY: build run clean test migrate-up migrate-down docker-build docker-up docker-down docker-logs send-test-email

# Build the application
build:
	go build -o ${BINARY_NAME} ./cmd/api

# Run the application
run:
	go run ./cmd/api/main.go

# Clean build files
clean:
	go clean
	rm -f ${BINARY_NAME}

# Run tests
test:
	go test -v ./...

# Run database migrations up
migrate-up:
	migrate -path migrations -database "postgres://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}?sslmode=${DB_SSLMODE}" up

# Run database migrations down
migrate-down:
	migrate -path migrations -database "postgres://${DB_USER}:${DB_PASSWORD}@${DB_HOST}:${DB_PORT}/${DB_NAME}?sslmode=${DB_SSLMODE}" down

# Build docker image
docker-build:
	docker-compose build

# Start services with docker-compose
docker-up:
	docker-compose up -d

# Stop services with docker-compose
docker-down:
	docker-compose down

# View docker logs
docker-logs:
	docker-compose logs -f

# Send a test email via the API
send-test-email:
	curl -X POST http://localhost:8080/api/mail/send \
		-H "Content-Type: application/json" \
		-H "Authorization: Bearer $(shell curl -s -X POST http://localhost:8080/api/auth/login -H "Content-Type: application/json" -d '{"email":"<EMAIL>","password":"password123"}' | jq -r '.token')" \
		-d '{"to":["<EMAIL>"],"subject":"Test Email","html_body":"<h1>This is a test email</h1>","company_id":1}'

# View mail UI
open-mailhog:
	open http://localhost:8025