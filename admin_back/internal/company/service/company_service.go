package service

import (
	"context"
	"fmt"
	"time"

	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/company/models"
	"github.com/psynarios/admin_back/internal/company/repository"
)

// CompanyService defines the interface for company business logic
type CompanyService interface {
	CreateCompany(ctx context.Context, req models.CompanyRequest) (models.Company, error)
	GetCompany(ctx context.Context, id string) (models.Company, error)
	GetCompanyByID(ctx context.Context, id string) (models.Company, error)
	ListCompanies(ctx context.Context) ([]models.Company, error)
	UpdateCompany(ctx context.Context, id string, req models.CompanyRequest) (models.Company, error)
	DeleteCompany(ctx context.Context, id string) error
	UpdateCompanySSL(ctx context.Context, id string, useSSL bool) (models.Company, error)
}

// companyService implements the CompanyService interface
type companyService struct {
	repo   repository.CompanyRepository
	logger zerolog.Logger
}

// NewCompanyService creates a new company service
func NewCompanyService(repo repository.CompanyRepository, logger zerolog.Logger) CompanyService {
	return &companyService{
		repo:   repo,
		logger: logger.With().Str("component", "company_service").Logger(),
	}
}

// CreateCompany creates a new company
func (s *companyService) CreateCompany(ctx context.Context, req models.CompanyRequest) (models.Company, error) {
	s.logger.Debug().
		Str("name", req.Name).
		Str("connection_string", req.ConnectionString).
		Str("database_type", req.DatabaseType).
		Bool("use_ssl", req.UseSSL).
		Str("url", req.URL).
		Str("started_at", req.StartedAt).
		Msg("Creating company with these values")

	// Parse StartedAt from string to time.Time
	var startedAt time.Time
	if req.StartedAt != "" {
		// Parse StartedAt from string to time.Time only if not empty
		parsed, err := time.Parse(time.RFC3339, req.StartedAt)
		if err != nil {
			s.logger.Error().Err(err).Str("startedAt", req.StartedAt).Msg("Invalid StartedAt format")
			return models.Company{}, fmt.Errorf("invalid StartedAt format: %w", err)
		}
		startedAt = parsed
	}

	// Create company model from request
	company := models.Company{
		Name:             req.Name,
		ConnectionString: req.ConnectionString,
		DatabaseType:     req.DatabaseType,
		UseSSL:           req.UseSSL,
		URL:              req.URL,
		StartedAt:        startedAt,
	}

	s.logger.Debug().
		Str("name", company.Name).
		Str("connection_string", company.ConnectionString).
		Str("database_type", company.DatabaseType).
		Bool("use_ssl", company.UseSSL).
		Str("url", company.URL).
		Time("started_at", company.StartedAt).
		Msg("Sending company to repository for creation")

	// Create company in repository
	createdCompany, err := s.repo.Create(ctx, company)
	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to create company")
		return models.Company{}, fmt.Errorf("failed to create company: %w", err)
	}

	s.logger.Info().
		Str("id", createdCompany.ID).
		Str("name", createdCompany.Name).
		Str("url", createdCompany.URL).
		Msg("Company created successfully")

	return createdCompany, nil
}

// GetCompany gets a company by ID
func (s *companyService) GetCompany(ctx context.Context, id string) (models.Company, error) {
	s.logger.Debug().Str("id", id).Msg("Getting company")

	company, err := s.repo.GetByID(ctx, id)
	if err != nil {
		s.logger.Error().Err(err).Str("id", id).Msg("Failed to get company")
		return models.Company{}, fmt.Errorf("failed to get company: %w", err)
	}

	return company, nil
}

// GetCompanyByID gets a company by ID (alias for GetCompany for backward compatibility)
func (s *companyService) GetCompanyByID(ctx context.Context, id string) (models.Company, error) {
	s.logger.Debug().Str("id", id).Msg("Getting company by ID")
	return s.GetCompany(ctx, id)
}

// ListCompanies lists all companies
func (s *companyService) ListCompanies(ctx context.Context) ([]models.Company, error) {
	s.logger.Debug().Msg("Listing companies")

	companies, err := s.repo.List(ctx)
	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to list companies")
		return nil, fmt.Errorf("failed to list companies: %w", err)
	}

	s.logger.Debug().Int("count", len(companies)).Msg("Companies retrieved successfully")

	return companies, nil
}

// UpdateCompany updates a company
func (s *companyService) UpdateCompany(ctx context.Context, id string, req models.CompanyRequest) (models.Company, error) {
	s.logger.Debug().
		Str("id", id).
		Str("name", req.Name).
		Str("url", req.URL).
		Msg("Updating company")

	// Check if company exists
	_, err := s.repo.GetByID(ctx, id)
	if err != nil {
		s.logger.Error().Err(err).Str("id", id).Msg("Company not found for update")
		return models.Company{}, fmt.Errorf("company not found: %w", err)
	}

	// Parse StartedAt from string to time.Time
	var startedAt time.Time
	if req.StartedAt != "" {
		// Parse StartedAt from string to time.Time only if not empty
		parsed, err := time.Parse(time.RFC3339, req.StartedAt)
		if err != nil {
			s.logger.Error().Err(err).Str("startedAt", req.StartedAt).Msg("Invalid StartedAt format")
			return models.Company{}, fmt.Errorf("invalid StartedAt format: %w", err)
		}
		startedAt = parsed
	}

	// Create company model from request
	company := models.Company{
		ID:               id,
		Name:             req.Name,
		ConnectionString: req.ConnectionString,
		DatabaseType:     req.DatabaseType,
		UseSSL:           req.UseSSL,
		URL:              req.URL,
		StartedAt:        startedAt,
	}

	s.logger.Debug().
		Str("id", company.ID).
		Str("name", company.Name).
		Str("url", company.URL).
		Msg("Sending company to repository for update")

	// Update company in repository
	updatedCompany, err := s.repo.Update(ctx, company)
	if err != nil {
		s.logger.Error().Err(err).Str("id", id).Msg("Failed to update company")
		return models.Company{}, fmt.Errorf("failed to update company: %w", err)
	}

	s.logger.Info().
		Str("id", updatedCompany.ID).
		Str("name", updatedCompany.Name).
		Str("url", updatedCompany.URL).
		Msg("Company updated successfully")

	return updatedCompany, nil
}

// DeleteCompany deletes a company
func (s *companyService) DeleteCompany(ctx context.Context, id string) error {
	s.logger.Debug().Str("id", id).Msg("Deleting company")

	// Check if company exists
	_, err := s.repo.GetByID(ctx, id)
	if err != nil {
		s.logger.Error().Err(err).Str("id", id).Msg("Company not found for deletion")
		return fmt.Errorf("company not found: %w", err)
	}

	// Delete company from repository
	err = s.repo.Delete(ctx, id)
	if err != nil {
		s.logger.Error().Err(err).Str("id", id).Msg("Failed to delete company")
		return fmt.Errorf("failed to delete company: %w", err)
	}

	s.logger.Info().Str("id", id).Msg("Company deleted successfully")

	return nil
}

// UpdateCompanySSL updates the SSL setting for a company
func (s *companyService) UpdateCompanySSL(ctx context.Context, id string, useSSL bool) (models.Company, error) {
	s.logger.Debug().
		Str("id", id).
		Bool("useSSL", useSSL).
		Msg("Updating company SSL setting")

	// Get the current company
	company, err := s.repo.GetByID(ctx, id)
	if err != nil {
		s.logger.Error().Err(err).Str("id", id).Msg("Company not found for SSL update")
		return models.Company{}, fmt.Errorf("company not found: %w", err)
	}

	// Update the SSL setting
	company.UseSSL = useSSL

	// Save the updated company
	updatedCompany, err := s.repo.Update(ctx, company)
	if err != nil {
		s.logger.Error().Err(err).Str("id", id).Msg("Failed to update company SSL setting")
		return models.Company{}, fmt.Errorf("failed to update company: %w", err)
	}

	s.logger.Info().
		Str("id", updatedCompany.ID).
		Str("name", updatedCompany.Name).
		Bool("useSSL", updatedCompany.UseSSL).
		Msg("Company SSL setting updated successfully")

	return updatedCompany, nil
}
