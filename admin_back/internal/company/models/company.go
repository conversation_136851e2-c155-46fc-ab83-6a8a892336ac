package models

import (
	"time"
)

// Company represents a company in the system
type Company struct {
	ID               string    `json:"id" db:"id"`
	Name             string    `json:"name" db:"name"`
	ConnectionString string    `json:"connection_string" db:"connection_string"`
	DatabaseType     string    `json:"database_type" db:"database_type"`
	UseSSL           bool      `json:"use_ssl" db:"use_ssl"`
	URL              string    `json:"url" db:"url"`
	StartedAt        time.Time `json:"started_at" db:"started_at"`
	CreatedAt        time.Time `json:"created_at" db:"created_at"`
	UpdatedAt        time.Time `json:"updated_at" db:"updated_at"`
}

// CompanyRequest represents the request to create or update a company
type CompanyRequest struct {
	Name             string `json:"name" binding:"required"`
	ConnectionString string `json:"connection_string" binding:"required"`
	DatabaseType     string `json:"database_type" binding:"required"`
	UseSSL           bool   `json:"use_ssl"`
	URL              string `json:"url"`
	StartedAt        string `json:"started_at,omitempty"`
}
