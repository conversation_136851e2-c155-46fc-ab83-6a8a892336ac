package repository

import (
	"context"

	"github.com/psynarios/admin_back/internal/company/models"
)

// CompanyRepository defines the interface for company data access
type CompanyRepository interface {
	Create(ctx context.Context, company models.Company) (models.Company, error)
	GetByID(ctx context.Context, id string) (models.Company, error)
	GetByName(ctx context.Context, name string) (models.Company, error)
	List(ctx context.Context) ([]models.Company, error)
	Update(ctx context.Context, company models.Company) (models.Company, error)
	Delete(ctx context.Context, id string) error
}
