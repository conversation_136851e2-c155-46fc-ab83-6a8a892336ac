package postgres

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/company/models"
	"github.com/psynarios/admin_back/internal/company/repository"
)

// companyRepository implements the repository.CompanyRepository interface
type companyRepository struct {
	db     *pgxpool.Pool
	logger zerolog.Logger
}

// NewCompanyRepository creates a new company repository
func NewCompanyRepository(db *pgxpool.Pool, logger zerolog.Logger) repository.CompanyRepository {
	return &companyRepository{
		db:     db,
		logger: logger.With().Str("component", "company_repository").Logger(),
	}
}

// Create creates a new company
func (r *companyRepository) Create(ctx context.Context, company models.Company) (models.Company, error) {
	r.logger.Debug().
		Str("name", company.Name).
		Str("connection_string", company.ConnectionString).
		Str("database_type", company.DatabaseType).
		Bool("use_ssl", company.UseSSL).
		Str("url", company.URL).
		Time("started_at", company.StartedAt).
		Msg("Creating company in repository with these values")

	// First, insert the company
	insertQuery := `
		INSERT INTO companies (
			name, connection_string, database_type, use_ssl, url, started_at, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8
		) RETURNING id
	`

	now := time.Now()
	company.CreatedAt = now
	company.UpdatedAt = now

	var id string
	err := r.db.QueryRow(
		ctx,
		insertQuery,
		company.Name,
		company.ConnectionString,
		company.DatabaseType,
		company.UseSSL,
		company.URL,
		company.StartedAt,
		company.CreatedAt,
		company.UpdatedAt,
	).Scan(&id)

	if err != nil {
		r.logger.Error().Err(err).Msg("Failed to insert company")
		return models.Company{}, fmt.Errorf("failed to insert company: %w", err)
	}

	// Then, retrieve the company to ensure all fields are correctly set
	selectQuery := `
		SELECT
			id, name, connection_string, database_type, use_ssl, url, started_at, created_at, updated_at
		FROM companies
		WHERE id = $1
	`

	var createdCompany models.Company
	err = r.db.QueryRow(ctx, selectQuery, id).Scan(
		&createdCompany.ID,
		&createdCompany.Name,
		&createdCompany.ConnectionString,
		&createdCompany.DatabaseType,
		&createdCompany.UseSSL,
		&createdCompany.URL,
		&createdCompany.StartedAt,
		&createdCompany.CreatedAt,
		&createdCompany.UpdatedAt,
	)

	if err != nil {
		r.logger.Error().Err(err).Str("id", id).Msg("Failed to retrieve created company")
		return models.Company{}, fmt.Errorf("failed to retrieve created company: %w", err)
	}

	r.logger.Debug().
		Str("id", createdCompany.ID).
		Str("name", createdCompany.Name).
		Str("url", createdCompany.URL).
		Msg("Company created successfully in repository")

	return createdCompany, nil
}

// GetByID gets a company by ID
func (r *companyRepository) GetByID(ctx context.Context, id string) (models.Company, error) {
	query := `
		SELECT id, name, connection_string, database_type, use_ssl, url, started_at, created_at, updated_at
		FROM companies
		WHERE id = $1
	`

	var company models.Company
	err := r.db.QueryRow(ctx, query, id).Scan(
		&company.ID,
		&company.Name,
		&company.ConnectionString,
		&company.DatabaseType,
		&company.UseSSL,
		&company.URL,
		&company.StartedAt,
		&company.CreatedAt,
		&company.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return models.Company{}, fmt.Errorf("company not found: %s", id)
		}
		return models.Company{}, fmt.Errorf("failed to get company: %w", err)
	}

	return company, nil
}

// List lists all companies
func (r *companyRepository) List(ctx context.Context) ([]models.Company, error) {
	query := `
		SELECT id, name, connection_string, database_type, use_ssl, url, started_at, created_at, updated_at
		FROM companies
		ORDER BY name
	`

	rows, err := r.db.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to list companies: %w", err)
	}
	defer rows.Close()

	var companies []models.Company
	for rows.Next() {
		var company models.Company
		err := rows.Scan(
			&company.ID,
			&company.Name,
			&company.ConnectionString,
			&company.DatabaseType,
			&company.UseSSL,
			&company.URL,
			&company.StartedAt,
			&company.CreatedAt,
			&company.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan company: %w", err)
		}
		companies = append(companies, company)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating companies: %w", err)
	}

	return companies, nil
}

// Update updates a company
func (r *companyRepository) Update(ctx context.Context, company models.Company) (models.Company, error) {
	r.logger.Debug().
		Str("id", company.ID).
		Str("name", company.Name).
		Str("url", company.URL).
		Msg("Updating company in repository")

	query := `
		UPDATE companies
		SET
			name = $1,
			connection_string = $2,
			database_type = $3,
			use_ssl = $4,
			url = $5,
			started_at = $6,
			updated_at = $7
		WHERE id = $8
		RETURNING id, name, connection_string, database_type, use_ssl, url, started_at, created_at, updated_at
	`

	now := time.Now()
	var updatedCompany models.Company

	err := r.db.QueryRow(
		ctx,
		query,
		company.Name,
		company.ConnectionString,
		company.DatabaseType,
		company.UseSSL,
		company.URL,
		company.StartedAt,
		now,
		company.ID,
	).Scan(
		&updatedCompany.ID,
		&updatedCompany.Name,
		&updatedCompany.ConnectionString,
		&updatedCompany.DatabaseType,
		&updatedCompany.UseSSL,
		&updatedCompany.URL,
		&updatedCompany.StartedAt,
		&updatedCompany.CreatedAt,
		&updatedCompany.UpdatedAt,
	)

	if err != nil {
		r.logger.Error().Err(err).Str("id", company.ID).Msg("Failed to update company")
		return models.Company{}, fmt.Errorf("failed to update company: %w", err)
	}

	r.logger.Debug().
		Str("id", updatedCompany.ID).
		Str("name", updatedCompany.Name).
		Str("url", updatedCompany.URL).
		Msg("Company updated successfully in repository")

	return updatedCompany, nil
}

// Delete deletes a company
func (r *companyRepository) Delete(ctx context.Context, id string) error {
	query := `DELETE FROM companies WHERE id = $1`

	tag, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete company: %w", err)
	}

	if tag.RowsAffected() == 0 {
		return fmt.Errorf("company not found: %s", id)
	}

	return nil
}

// GetByName gets a company by name
func (r *companyRepository) GetByName(ctx context.Context, name string) (models.Company, error) {
	query := `
		SELECT id, name, connection_string, database_type, use_ssl, url, started_at, created_at, updated_at
		FROM companies
		WHERE name = $1
	`

	var company models.Company
	err := r.db.QueryRow(ctx, query, name).Scan(
		&company.ID,
		&company.Name,
		&company.ConnectionString,
		&company.DatabaseType,
		&company.UseSSL,
		&company.URL,
		&company.StartedAt,
		&company.CreatedAt,
		&company.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return models.Company{}, fmt.Errorf("company not found with name: %s", name)
		}
		return models.Company{}, fmt.Errorf("failed to get company by name: %w", err)
	}

	return company, nil
}
