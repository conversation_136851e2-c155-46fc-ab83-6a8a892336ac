package handlers

import (
	"bytes"
	"fmt"
	"io"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/templating/models"
	"github.com/psynarios/admin_back/internal/templating/service"
)

// Template<PERSON><PERSON><PERSON> handles template-related HTTP requests
type Template<PERSON>andler struct {
	templateService service.TemplateService
	logger          zerolog.Logger
}

// NewTemplateHandler creates a new template handler
func NewTemplateHandler(templateService service.TemplateService, logger zerolog.Logger) *TemplateHandler {
	return &TemplateHandler{
		templateService: templateService,
		logger:          logger.With().Str("component", "template_handler").Logger(),
	}
}

// CreateTemplateGroup handles creating a new template group
func (h *TemplateHandler) CreateTemplateGroup(c *gin.Context) {
	var req models.TemplateGroupRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn().Err(err).Msg("Invalid create template group request")
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}

	group, err := h.templateService.CreateTemplateGroup(c.Request.Context(), req)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to create template group")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create template group"})
		return
	}

	c.JSON(http.StatusCreated, group)
}

// GetTemplateGroup handles retrieving a template group by ID
func (h *TemplateHandler) GetTemplateGroup(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		h.logger.Warn().Str("id", "empty").Msg("Empty template group ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template group ID"})
		return
	}

	group, err := h.templateService.GetTemplateGroup(c.Request.Context(), id)
	if err != nil {
		h.logger.Error().Err(err).Str("id", id).Msg("Failed to get template group")
		c.JSON(http.StatusNotFound, gin.H{"error": "Template group not found"})
		return
	}

	c.JSON(http.StatusOK, group)
}

// GetTemplateGroupByDay handles retrieving a template group by day and company
func (h *TemplateHandler) GetTemplateGroupByDay(c *gin.Context) {
	dayNumber := c.Param("day")
	if dayNumber == "" {
		h.logger.Warn().Str("day", "empty").Msg("Empty day number")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid day number"})
		return
	}

	// Convert day string to int
	day := 0
	_, err := fmt.Sscanf(dayNumber, "%d", &day)
	if err != nil {
		h.logger.Warn().Err(err).Str("day", dayNumber).Msg("Invalid day number format")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid day number format"})
		return
	}

	companyID := c.Param("companyId")
	if companyID == "" {
		h.logger.Warn().Str("company_id", "empty").Msg("Empty company ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid company ID"})
		return
	}

	group, err := h.templateService.GetTemplateGroupByDay(c.Request.Context(), day, companyID)
	if err != nil {
		h.logger.Error().Err(err).Int("day", day).Str("company_id", companyID).Msg("Failed to get template group")
		c.JSON(http.StatusNotFound, gin.H{"error": "Template group not found"})
		return
	}

	c.JSON(http.StatusOK, group)
}

// ListTemplateGroups handles retrieving all template groups
func (h *TemplateHandler) ListTemplateGroups(c *gin.Context) {
	companyID := c.Query("company_id")

	var groups interface{}
	var err error

	if companyID != "" {
		groups, err = h.templateService.ListTemplateGroupsByCompany(c.Request.Context(), companyID)
	} else {
		groups, err = h.templateService.ListTemplateGroups(c.Request.Context())
	}

	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to list template groups")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list template groups"})
		return
	}

	c.JSON(http.StatusOK, groups)
}

// UpdateTemplateGroup handles updating a template group
func (h *TemplateHandler) UpdateTemplateGroup(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		h.logger.Warn().Str("id", "empty").Msg("Empty template group ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template group ID"})
		return
	}

	var req models.TemplateGroupRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn().Err(err).Msg("Invalid update template group request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}

	group, err := h.templateService.UpdateTemplateGroup(c.Request.Context(), id, req)
	if err != nil {
		h.logger.Error().Err(err).Str("id", id).Msg("Failed to update template group")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update template group"})
		return
	}

	c.JSON(http.StatusOK, group)
}

// DeleteTemplateGroup handles deleting a template group
func (h *TemplateHandler) DeleteTemplateGroup(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		h.logger.Warn().Str("id", "empty").Msg("Empty template group ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template group ID"})
		return
	}

	err := h.templateService.DeleteTemplateGroup(c.Request.Context(), id)
	if err != nil {
		h.logger.Error().Err(err).Str("id", id).Msg("Failed to delete template group")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete template group"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Template group deleted successfully"})
}

// Fix the CreateTemplate function
func (h *TemplateHandler) CreateTemplate(c *gin.Context) {
	var req models.TemplateRequest

	// Read the raw request body for debugging
	bodyBytes, err := io.ReadAll(c.Request.Body)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to read request body")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to read request body"})
		return
	}

	// Log the raw request body
	h.logger.Debug().Str("raw_body", string(bodyBytes)).Msg("Raw request body")

	// Restore the request body for binding
	c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn().Err(err).Msg("Invalid create template request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request", "details": err.Error()})
		return
	}

	// Log the parsed request for debugging
	h.logger.Debug().
		Str("name", req.Name).
		Str("subject", req.Subject).
		Int("week_number", req.WeekNumber).
		Str("group_id", req.GroupID).
		Str("type", req.Type).
		Msg("Parsed template request")

	// Create the template
	template, err := h.templateService.CreateTemplate(c.Request.Context(), req)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to create template")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create template"})
		return
	}

	c.JSON(http.StatusCreated, template)
}

// Fix the RenderTemplate function to support images
func (h *TemplateHandler) RenderTemplate(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		h.logger.Warn().Str("id", "empty").Msg("Empty template ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template ID"})
		return
	}

	var data map[string]interface{}
	if err := c.ShouldBindJSON(&data); err != nil {
		h.logger.Warn().Err(err).Msg("Invalid template data")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template data"})
		return
	}

	rendered, err := h.templateService.RenderTemplate(c.Request.Context(), id, data)
	if err != nil {
		h.logger.Error().Err(err).Str("id", id).Msg("Failed to render template")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to render template"})
		return
	}

	// Set content type to allow HTML and rich content
	c.Header("Content-Type", "application/json")
	c.JSON(http.StatusOK, rendered)
}

// GetTemplate handles retrieving a template by ID
func (h *TemplateHandler) GetTemplate(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		h.logger.Warn().Str("id", "empty").Msg("Empty template ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template ID"})
		return
	}

	template, err := h.templateService.GetTemplate(c.Request.Context(), id)
	if err != nil {
		h.logger.Error().Err(err).Str("id", id).Msg("Failed to get template")
		c.JSON(http.StatusNotFound, gin.H{"error": "Template not found"})
		return
	}

	c.JSON(http.StatusOK, template)
}

// ListTemplates handles retrieving all templates for a group
func (h *TemplateHandler) ListTemplates(c *gin.Context) {
	groupID := c.Param("groupId")
	if groupID == "" {
		h.logger.Warn().Str("group_id", "empty").Msg("Empty group ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid group ID"})
		return
	}

	templates, err := h.templateService.ListTemplatesByGroup(c.Request.Context(), groupID)
	if err != nil {
		h.logger.Error().Err(err).Str("group_id", groupID).Msg("Failed to list templates")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list templates"})
		return
	}

	c.JSON(http.StatusOK, templates)
}

// UpdateTemplate handles updating a template
func (h *TemplateHandler) UpdateTemplate(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		h.logger.Warn().Msg("Empty template ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template ID"})
		return
	}

	var req models.TemplateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn().Err(err).Msg("Invalid update template request")

		// Log the raw request body for debugging
		rawBody, _ := io.ReadAll(c.Request.Body)
		h.logger.Debug().Str("raw_body", string(rawBody)).Msg("Raw request body")

		// Special handling for WeekNumber field
		if req.WeekNumber == 0 {
			h.logger.Info().Msg("WeekNumber is 0, proceeding with update")
		} else {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
	}

	// Proceed with the update
	template, err := h.templateService.UpdateTemplate(c.Request.Context(), id, req)
	if err != nil {
		h.logger.Error().Err(err).Str("id", id).Msg("Failed to update template")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update template"})
		return
	}

	c.JSON(http.StatusOK, template)
}

// DeleteTemplate handles deleting a template
func (h *TemplateHandler) DeleteTemplate(c *gin.Context) {
	id := c.Param("id")
	if id == "" {
		h.logger.Warn().Str("id", "empty").Msg("Empty template ID")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid template ID"})
		return
	}

	err := h.templateService.DeleteTemplate(c.Request.Context(), id)
	if err != nil {
		h.logger.Error().Err(err).Str("id", id).Msg("Failed to delete template")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete template"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Template deleted successfully"})
}

// GetAlternativeTemplates returns a list of alternative templates for a company
func (h *TemplateHandler) GetAlternativeTemplates(c *gin.Context) {
	companyID := c.Param("companyId")
	if companyID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Company ID is required"})
		return
	}

	// Get alternative templates for the company
	templates, err := h.templateService.GetAlternativeTemplates(c.Request.Context(), companyID)
	if err != nil {
		h.logger.Error().Err(err).Str("companyID", companyID).Msg("Failed to get alternative templates")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get alternative templates"})
		return
	}

	c.JSON(http.StatusOK, templates)
}
