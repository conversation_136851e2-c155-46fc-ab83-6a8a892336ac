package handlers

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/auth/models"
	"github.com/psynarios/admin_back/internal/auth/service"
)

// AuthHandler handles authentication requests
type AuthHandler struct {
	authService service.AuthService
	logger      zerolog.Logger
}

// NewAuthHandler creates a new auth handler
func NewAuthHandler(authService service.AuthService, logger zerolog.Logger) *AuthHandler {
	return &AuthHandler{
		authService: authService,
		logger:      logger.With().Str("component", "auth_handler").Logger(),
	}
}

// GetAuthService returns the auth service for testing/debugging
func (h *AuthHandler) GetAuthService() service.AuthService {
	return h.authService
}

// Login handles user login
func (h *AuthHandler) Login(c *gin.Context) {
	var req struct {
		Email    string `json:"email" binding:"required,email"`
		Password string `json:"password" binding:"required"`
	}

	if err := c.Should<PERSON>ind<PERSON>(&req); err != nil {
		h.logger.Warn().Err(err).Msg("Invalid login request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}

	// Authenticate user
	tokens, user, err := h.authService.Login(c.Request.Context(), req.Email, req.Password)
	if err != nil {
		h.logger.Warn().Err(err).Str("email", req.Email).Msg("Login failed")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid credentials"})
		return
	}

	// Set auth cookies (HTTP-only for tokens)
	h.authService.SetAuthCookies(c.Writer, tokens)

	// Set CSRF token (non HTTP-only)
	h.authService.SetCsrfCookie(c.Writer, tokens.CsrfToken)

	h.logger.Info().
		Str("email", req.Email).
		Str("ip", c.ClientIP()).
		Bool("csrf_token_set", tokens.CsrfToken != "").
		Msg("User logged in successfully")

	// Return user info, expiry, and CSRF token in the response body
	c.JSON(http.StatusOK, gin.H{
		"message":    "Login successful",
		"user":       user,
		"csrf_token": tokens.CsrfToken, // Include CSRF token in response for frontend
		"expires_in": tokens.AtExpires - time.Now().Unix(),
	})
}

// Logout handles user logout
func (h *AuthHandler) Logout(c *gin.Context) {
	ctx := c.Request.Context()

	// Get refresh token from cookie
	_, refreshToken := h.authService.GetTokenFromCookies(c.Request)

	// Logout user
	err := h.authService.Logout(ctx, c.Writer, refreshToken)
	if err != nil {
		h.logger.Error().Err(err).Msg("Error during logout")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "logout_failed"})
		return
	}

	userID, _ := c.Get("user_id")
	h.logger.Info().Interface("user_id", userID).Str("ip", c.ClientIP()).Msg("User logged out")

	c.JSON(http.StatusOK, gin.H{
		"message": "Logout successful",
	})
}

// Register handles user registration
func (h *AuthHandler) Register(c *gin.Context) {
	var req models.CreateUserRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn().Err(err).Msg("Invalid registration request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}

	// Register user
	user, err := h.authService.Register(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error().Err(err).Str("email", req.Email).Msg("Registration failed")
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	h.logger.Info().Str("email", req.Email).Str("ip", c.ClientIP()).Msg("User registered successfully")

	c.JSON(http.StatusCreated, gin.H{
		"message": "Registration successful",
		"user":    user,
	})
}

// ChangePassword handles password change
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	var req models.UpdatePasswordRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn().Err(err).Msg("Invalid password change request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}

	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	// Change password
	err := h.authService.ChangePassword(
		c.Request.Context(),
		userID.(int64),
		req.CurrentPassword,
		req.NewPassword,
	)

	if err != nil {
		h.logger.Error().Err(err).Interface("user_id", userID).Msg("Password change failed")
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	h.logger.Info().Interface("user_id", userID).Str("ip", c.ClientIP()).Msg("Password changed successfully")

	c.JSON(http.StatusOK, gin.H{
		"message": "Password changed successfully",
	})
}

// GetCurrentUser returns the current user
func (h *AuthHandler) GetCurrentUser(c *gin.Context) {
	// Get user ID from context (set by auth middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"error": "Authentication required"})
		return
	}

	// Get user from database
	user, err := h.authService.GetUserByID(c.Request.Context(), userID.(int64))
	if err != nil {
		h.logger.Error().Err(err).Interface("user_id", userID).Msg("Failed to get user")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"user": user,
	})
}

// RefreshToken handles token refresh requests with improved CSRF handling
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	ctx := c.Request.Context()

	// Get refresh token from cookie only
	_, refreshToken := h.authService.GetTokenFromCookies(c.Request)

	// If no token in cookie, return error
	if refreshToken == "" {
		h.logger.Warn().
			Str("ip", c.ClientIP()).
			Str("path", c.Request.URL.Path).
			Msg("No refresh token in cookie")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "no_refresh_token"})
		return
	}

	// Attempt to refresh the token
	tokens, err := h.authService.RefreshToken(ctx, refreshToken)
	if err != nil {
		h.logger.Warn().
			Err(err).
			Str("ip", c.ClientIP()).
			Str("path", c.Request.URL.Path).
			Msg("Failed to refresh token")
		c.JSON(http.StatusUnauthorized, gin.H{"error": "invalid_refresh_token"})
		return
	}

	// Set new tokens in cookies
	h.authService.SetAuthCookies(c.Writer, tokens)

	// Set new CSRF token
	h.authService.SetCsrfCookie(c.Writer, tokens.CsrfToken)

	h.logger.Debug().
		Str("ip", c.ClientIP()).
		Bool("csrf_token_set", tokens.CsrfToken != "").
		Msg("Token refreshed successfully")

	// Return success response with expiry and CSRF token for frontend
	c.JSON(http.StatusOK, gin.H{
		"message":    "Token refreshed successfully",
		"csrf_token": tokens.CsrfToken, // Include the new CSRF token in response
		"expires_in": tokens.AtExpires - time.Now().Unix(),
	})
}

// CheckAuthStatus checks if user is authenticated and returns user info
// Useful for silent refresh implementations
func (h *AuthHandler) CheckAuthStatus(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{"authenticated": false})
		return
	}

	// Get user from database
	user, err := h.authService.GetUserByID(c.Request.Context(), userID.(int64))
	if err != nil {
		h.logger.Error().Err(err).Interface("user_id", userID).Msg("Failed to get user")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user"})
		return
	}

	// Get CSRF token from cookie for debugging
	csrfToken := h.authService.GetCsrfToken(c.Request)

	c.JSON(http.StatusOK, gin.H{
		"authenticated": true,
		"user":          user,
		"csrf_present":  csrfToken != "",
	})
}

// DebugCsrf is a special endpoint for debugging CSRF issues
func (h *AuthHandler) DebugCsrf(c *gin.Context) {
	// Get CSRF token from cookie
	csrfCookie := h.authService.GetCsrfToken(c.Request)

	// Get CSRF token from header
	csrfHeader := c.GetHeader("X-CSRF-Token")

	// Get user info if available
	userID, userExists := c.Get("user_id")
	username, usernameExists := c.Get("username")

	// Return status
	c.JSON(http.StatusOK, gin.H{
		"csrf_cookie_present": csrfCookie != "",
		"csrf_header_present": csrfHeader != "",
		"csrf_match":          csrfCookie == csrfHeader && csrfCookie != "",
		"user_id_present":     userExists,
		"user_id":             userID,
		"username_present":    usernameExists,
		"username":            username,
		"remote_ip":           c.ClientIP(),
		"request_path":        c.Request.URL.Path,
		"request_method":      c.Request.Method,
	})
}

// FixCsrf is a special endpoint for fixing CSRF issues
func (h *AuthHandler) FixCsrf(c *gin.Context) {
	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "Authentication required to fix CSRF token",
		})
		return
	}

	// Get user info
	user, err := h.authService.GetUserByID(c.Request.Context(), userID.(int64))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to get user information",
		})
		return
	}

	// Generate a new CSRF token directly
	tokens, err := h.authService.RefreshTokenForUser(c.Request.Context(), userID.(int64))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to regenerate CSRF token",
		})
		return
	}

	// Set the new CSRF token
	h.authService.SetCsrfCookie(c.Writer, tokens.CsrfToken)

	h.logger.Info().
		Int64("user_id", userID.(int64)).
		Str("ip", c.ClientIP()).
		Msg("CSRF token regenerated manually")

	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"message":    "CSRF token regenerated successfully",
		"csrf_token": tokens.CsrfToken,
		"user":       user,
	})
}
