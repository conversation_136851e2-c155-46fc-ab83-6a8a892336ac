package handlers

import (
	"net/http"
	"os"
	"path/filepath"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/company/models"
	"github.com/psynarios/admin_back/internal/company/service"
)

// CompanyHandler handles company-related HTTP requests
type CompanyHandler struct {
	companyService service.CompanyService
	logger         zerolog.Logger
}

// NewCompanyHandler creates a new company handler
func NewCompanyHandler(companyService service.CompanyService, logger zerolog.Logger) *CompanyHandler {
	return &CompanyHandler{
		companyService: companyService,
		logger:         logger.With().Str("component", "company_handler").Logger(),
	}
}

// CreateCompany creates a new company
func (h *CompanyHandler) CreateCompany(c *gin.Context) {
	var req models.CompanyRequest

	if err := c.ShouldBindJSO<PERSON>(&req); err != nil {
		h.logger.Warn().Err(err).Msg("Invalid create company request")
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}

	h.logger.Debug().
		Str("name", req.Name).
		Str("database_type", req.DatabaseType).
		Bool("use_ssl", req.UseSSL).
		Str("url", req.URL).
		Msg("Creating company")

	company, err := h.companyService.CreateCompany(c.Request.Context(), req)
	if err != nil {
		h.logger.Error().Err(err).Str("name", req.Name).Msg("Failed to create company")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create company"})
		return
	}

	c.JSON(http.StatusCreated, company)
}

// GetCompany handles retrieving a company by ID
func (h *CompanyHandler) GetCompany(c *gin.Context) {
	id := c.Param("id")

	company, err := h.companyService.GetCompany(c.Request.Context(), id)
	if err != nil {
		h.logger.Error().Err(err).Str("id", id).Msg("Failed to get company")
		c.JSON(http.StatusNotFound, gin.H{"error": "Company not found"})
		return
	}

	c.JSON(http.StatusOK, company)
}

// ListCompanies handles retrieving all companies
func (h *CompanyHandler) ListCompanies(c *gin.Context) {
	companies, err := h.companyService.ListCompanies(c.Request.Context())
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to list companies")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list companies"})
		return
	}

	c.JSON(http.StatusOK, companies)
}

// UpdateCompany updates a company
func (h *CompanyHandler) UpdateCompany(c *gin.Context) {
	id := c.Param("id")
	var req models.CompanyRequest

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn().Err(err).Msg("Invalid update company request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}

	// Log the full request body for debugging
	h.logger.Debug().
		Str("id", id).
		Str("name", req.Name).
		Str("database_type", req.DatabaseType).
		Bool("use_ssl", req.UseSSL).
		Str("url", req.URL).
		Interface("full_request", req).
		Msg("Updating company")

	company, err := h.companyService.UpdateCompany(c.Request.Context(), id, req)
	if err != nil {
		h.logger.Error().Err(err).Str("id", id).Msg("Failed to update company")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update company"})
		return
	}

	h.logger.Debug().
		Str("id", company.ID).
		Str("name", company.Name).
		Str("url", company.URL).
		Interface("full_company", company).
		Msg("Company updated successfully")

	c.JSON(http.StatusOK, company)
}

// DeleteCompany handles deleting a company
func (h *CompanyHandler) DeleteCompany(c *gin.Context) {
	id := c.Param("id")

	err := h.companyService.DeleteCompany(c.Request.Context(), id)
	if err != nil {
		h.logger.Error().Err(err).Str("id", id).Msg("Failed to delete company")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Company deleted successfully"})
}

// UploadCertificate handles uploading a certificate for a company
func (h *CompanyHandler) UploadCertificate(c *gin.Context) {
	companyID := c.Param("id")

	// Validate UUID format
	_, err := uuid.Parse(companyID)
	if err != nil {
		h.logger.Warn().Err(err).Str("id", companyID).Msg("Invalid company ID format")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid company ID format"})
		return
	}

	// Check if company exists
	_, err = h.companyService.GetCompanyByID(c.Request.Context(), companyID)
	if err != nil {
		h.logger.Error().Err(err).Str("id", companyID).Msg("Company not found")
		c.JSON(http.StatusNotFound, gin.H{"error": "Company not found"})
		return
	}

	// Get the file from the request
	file, err := c.FormFile("certificate")
	if err != nil {
		h.logger.Error().Err(err).Msg("No certificate file uploaded")
		c.JSON(http.StatusBadRequest, gin.H{"error": "No certificate file uploaded"})
		return
	}

	// Check file extension
	ext := filepath.Ext(file.Filename)
	if ext != ".crt" && ext != ".pem" {
		h.logger.Error().Str("extension", ext).Msg("Invalid certificate file format")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Only .crt or .pem files are supported"})
		return
	}

	// Skip trying to use the default certs directory and go straight to temp directory
	// This avoids permission issues that are common in containerized environments
	tempDir := os.TempDir()
	certsDir := filepath.Join(tempDir, "psynarios_certs")

	// Create the directory if it doesn't exist
	if _, err := os.Stat(certsDir); os.IsNotExist(err) {
		err = os.MkdirAll(certsDir, 0777)
		if err != nil {
			h.logger.Error().Err(err).Msg("Failed to create certs directory in temp location")
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create certs directory"})
			return
		}
	}

	h.logger.Info().Str("dir", certsDir).Msg("Using temporary directory for certificates")

	// Save the file with the company ID as the filename
	certPath := filepath.Join(certsDir, companyID+".crt")
	if err := c.SaveUploadedFile(file, certPath); err != nil {
		h.logger.Error().Err(err).Str("path", certPath).Msg("Failed to save certificate file")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save certificate file"})
		return
	}

	// Set permissions on the certificate file
	if err := os.Chmod(certPath, 0666); err != nil {
		h.logger.Warn().Err(err).Str("path", certPath).Msg("Failed to set permissions on certificate file")
		// Continue anyway since the file was saved successfully
	}

	// Update company to use SSL
	_, err = h.companyService.UpdateCompanySSL(c.Request.Context(), companyID, true)
	if err != nil {
		h.logger.Error().Err(err).Str("id", companyID).Msg("Failed to update company SSL setting")
		// Don't return an error, as the certificate was successfully uploaded
		h.logger.Warn().Msg("Certificate uploaded but company SSL setting not updated")
	}

	c.JSON(http.StatusOK, gin.H{
		"message":    "Certificate uploaded successfully",
		"company_id": companyID,
		"use_ssl":    true,
	})
}

// DeleteCertificate handles deleting a certificate for a company
func (h *CompanyHandler) DeleteCertificate(c *gin.Context) {
	companyID := c.Param("id")

	// Validate UUID format
	_, err := uuid.Parse(companyID)
	if err != nil {
		h.logger.Warn().Err(err).Str("id", companyID).Msg("Invalid company ID format")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid company ID format"})
		return
	}

	// Check if company exists
	_, err = h.companyService.GetCompanyByID(c.Request.Context(), companyID)
	if err != nil {
		h.logger.Error().Err(err).Str("id", companyID).Msg("Company not found")
		c.JSON(http.StatusNotFound, gin.H{"error": "Company not found"})
		return
	}

	// Use the temp directory for certificates
	tempDir := os.TempDir()
	certsDir := filepath.Join(tempDir, "psynarios_certs")
	certPath := filepath.Join(certsDir, companyID+".crt")

	// Check if certificate exists
	if _, err := os.Stat(certPath); os.IsNotExist(err) {
		h.logger.Error().Str("path", certPath).Msg("Certificate file not found")
		c.JSON(http.StatusNotFound, gin.H{"error": "Certificate file not found"})
		return
	}

	// Delete the certificate file
	if err := os.Remove(certPath); err != nil {
		h.logger.Error().Err(err).Str("path", certPath).Msg("Failed to delete certificate file")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete certificate file"})
		return
	}

	// Update company to not use SSL
	_, err = h.companyService.UpdateCompanySSL(c.Request.Context(), companyID, false)
	if err != nil {
		h.logger.Error().Err(err).Str("id", companyID).Msg("Failed to update company SSL setting")
		// Don't return an error, as the certificate was successfully deleted
		h.logger.Warn().Msg("Certificate deleted but company SSL setting not updated")
	}

	c.JSON(http.StatusOK, gin.H{
		"message":    "Certificate deleted successfully",
		"company_id": companyID,
		"use_ssl":    false,
	})
}

// CheckCertificate checks if a certificate exists for a company
func (h *CompanyHandler) CheckCertificate(c *gin.Context) {
	companyID := c.Param("id")

	// Validate UUID format
	_, err := uuid.Parse(companyID)
	if err != nil {
		h.logger.Warn().Err(err).Str("id", companyID).Msg("Invalid company ID format")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid company ID format"})
		return
	}

	// Check if company exists
	company, err := h.companyService.GetCompanyByID(c.Request.Context(), companyID)
	if err != nil {
		h.logger.Error().Err(err).Str("id", companyID).Msg("Company not found")
		c.JSON(http.StatusNotFound, gin.H{"error": "Company not found"})
		return
	}

	// Use the temp directory for certificates
	tempDir := os.TempDir()
	certsDir := filepath.Join(tempDir, "psynarios_certs")
	certPath := filepath.Join(certsDir, companyID+".crt")

	// Check if certificate exists
	certExists := true
	if _, err := os.Stat(certPath); os.IsNotExist(err) {
		certExists = false
	}

	c.JSON(http.StatusOK, gin.H{
		"company_id":         companyID,
		"use_ssl":            company.UseSSL,
		"certificate_exists": certExists,
	})
}
