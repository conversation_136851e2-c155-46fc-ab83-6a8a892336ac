package handlers

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/mailer/models"
	"github.com/psynarios/admin_back/internal/mailer/service"
)

// MailHandler handles HTTP requests for mail operations
type MailHandler struct {
	mailService service.MailService
	logger      zerolog.Logger
}

// NewMailHandler creates a new mail handler
func NewMailHandler(mailService service.MailService, logger zerolog.Logger) *MailHandler {
	return &MailHandler{
		mailService: mailService,
		logger:      logger.With().Str("component", "mail_handler").Logger(),
	}
}

// CreateMailConfig handles creating a new mail configuration
func (h *MailHandler) CreateMailConfig(c *gin.Context) {
	var request models.MailConfigRequest

	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error().Err(err).Msg("Invalid request body")
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	config := models.MailConfig{
		CompanyID:    request.CompanyID,
		SenderEmail:  request.SenderEmail,
		SenderName:   request.SenderName,
		SMTPHost:     request.SMTPHost,
		SMTPPort:     request.SMTPPort,
		SMTPUsername: request.SMTPUsername,
		SMTPPassword: request.SMTPPassword,
		UseSSL:       request.UseSSL,
		Active:       request.Active,
	}

	createdConfig, err := h.mailService.CreateMailConfig(c.Request.Context(), config)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to create mail config")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create mail configuration"})
		return
	}

	// Don't return the SMTP password in the response
	createdConfig.SMTPPassword = "[REDACTED]"

	c.JSON(http.StatusCreated, gin.H{
		"message": "Mail configuration created successfully",
		"config":  createdConfig,
	})
}

// GetMailConfig gets the active mail configuration
func (h *MailHandler) GetMailConfig(c *gin.Context) {
	config, err := h.mailService.GetMailConfig(c.Request.Context())
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to retrieve mail config")
		c.JSON(http.StatusNotFound, gin.H{"error": "No active mail configuration found"})
		return
	}

	// Don't return the SMTP password in the response
	config.SMTPPassword = "[REDACTED]"

	c.JSON(http.StatusOK, config)
}

// GetMailConfigByCompany gets the active mail configuration for a specific company
func (h *MailHandler) GetMailConfigByCompany(c *gin.Context) {
	companyID := c.Param("companyId")

	// Validate UUID format if needed
	_, err := uuid.Parse(companyID)
	if err != nil {
		h.logger.Error().Err(err).Str("company_id", companyID).Msg("Invalid company ID format")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid company ID format"})
		return
	}

	config, err := h.mailService.GetMailConfigByCompany(c.Request.Context(), companyID)
	if err != nil {
		h.logger.Error().Err(err).Str("company_id", companyID).Msg("Failed to retrieve mail config")
		c.JSON(http.StatusNotFound, gin.H{"error": "No active mail configuration found for this company"})
		return
	}

	// Don't return the SMTP password in the response
	config.SMTPPassword = "[REDACTED]"

	c.JSON(http.StatusOK, config)
}

// UpdateMailConfig updates a mail configuration
func (h *MailHandler) UpdateMailConfig(c *gin.Context) {
	configID := c.Param("id")

	// Validate UUID format
	_, err := uuid.Parse(configID)
	if err != nil {
		h.logger.Error().Err(err).Str("config_id", configID).Msg("Invalid config ID format")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid configuration ID format"})
		return
	}

	var request models.MailConfigRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error().Err(err).Msg("Invalid request body")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	config := models.MailConfig{
		ID:           configID,
		CompanyID:    request.CompanyID,
		SenderEmail:  request.SenderEmail,
		SenderName:   request.SenderName,
		SMTPHost:     request.SMTPHost,
		SMTPPort:     request.SMTPPort,
		SMTPUsername: request.SMTPUsername,
		SMTPPassword: request.SMTPPassword,
		UseSSL:       request.UseSSL,
		Active:       request.Active,
	}

	updatedConfig, err := h.mailService.UpdateMailConfig(c.Request.Context(), config)
	if err != nil {
		h.logger.Error().Err(err).Str("config_id", configID).Msg("Failed to update mail config")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update mail configuration"})
		return
	}

	// Don't return the SMTP password in the response
	updatedConfig.SMTPPassword = "[REDACTED]"

	c.JSON(http.StatusOK, gin.H{
		"message": "Mail configuration updated successfully",
		"config":  updatedConfig,
	})
}

// ListMailConfigs lists all mail configurations
func (h *MailHandler) ListMailConfigs(c *gin.Context) {
	configs, err := h.mailService.ListMailConfigs(c.Request.Context())
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to list mail configs")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to retrieve mail configurations"})
		return
	}

	// Redact passwords from all configs
	for i := range configs {
		configs[i].SMTPPassword = "[REDACTED]"
	}

	c.JSON(http.StatusOK, configs)
}

// SendEmail handles the email sending HTTP request
func (h *MailHandler) SendEmail(c *gin.Context) {
	var request models.EmailRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error().Err(err).Msg("Invalid email request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid email request"})
		return
	}

	// Validate the request
	if len(request.To) == 0 {
		h.logger.Error().Msg("No recipients provided")
		c.JSON(http.StatusBadRequest, gin.H{"error": "At least one recipient is required"})
		return
	}

	if request.Subject == "" {
		h.logger.Error().Msg("No subject provided")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Subject is required"})
		return
	}

	if request.Body == "" {
		h.logger.Error().Msg("No email content provided")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Email body is required"})
		return
	}

	// Send the email
	err := h.mailService.SendEmail(
		c.Request.Context(),
		request.To,
		request.Cc,
		request.Bcc,
		request.Subject,
		request.Body,
		request.CompanyID,
	)

	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to send email")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send email"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Email sent successfully",
	})
}

// TestMailConfig tests a mail configuration by sending a test email
func (h *MailHandler) TestMailConfig(c *gin.Context) {
	var request struct {
		Config    models.MailConfig `json:"config"`
		TestEmail string            `json:"testEmail"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error().Err(err).Msg("Invalid test request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}

	if request.TestEmail == "" {
		h.logger.Error().Msg("No test email address provided")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Test email address is required"})
		return
	}

	// Create temporary mail service with test config
	tempSender := service.NewEmailSender(h.logger)

	// Generate test email content
	subject := "Test Email from Your Application"
	body := "<h1>Test Email</h1><p>If you're reading this, your email configuration is working correctly!</p>"

	// Send test email
	err := tempSender.SendMail(request.Config, []string{request.TestEmail}, nil, nil, subject, body)
	if err != nil {
		h.logger.Error().Err(err).Msg("Mail configuration test failed")
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Test email sent successfully",
	})
}

// SendTemplateEmail handles sending an email using a template
func (h *MailHandler) SendTemplateEmail(c *gin.Context) {
	var request struct {
		To         []string               `json:"to" binding:"required"`
		Cc         []string               `json:"cc"`
		Bcc        []string               `json:"bcc"`
		TemplateID string                 `json:"template_id" binding:"required"`
		Data       map[string]interface{} `json:"data" binding:"required"`
		CompanyID  string                 `json:"company_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error().Err(err).Msg("Invalid template email request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request"})
		return
	}

	// Send the email using the template
	err := h.mailService.SendTemplateEmail(
		c.Request.Context(),
		request.To,
		request.Cc,
		request.Bcc,
		request.TemplateID,
		request.Data,
		request.CompanyID,
	)

	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to send template email")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send email: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Email sent successfully",
	})
}

// DeleteMailConfig deletes a mail configuration
func (h *MailHandler) DeleteMailConfig(c *gin.Context) {
	configID := c.Param("id")

	// Validate UUID format
	_, err := uuid.Parse(configID)
	if err != nil {
		h.logger.Error().Err(err).Str("config_id", configID).Msg("Invalid config ID format")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid configuration ID format"})
		return
	}

	// Delete the mail config
	err = h.mailService.DeleteMailConfig(c.Request.Context(), configID)
	if err != nil {
		h.logger.Error().Err(err).Str("config_id", configID).Msg("Failed to delete mail config")

		// Check if it's a "not found" error
		if err.Error() == fmt.Sprintf("mail config with ID %s not found", configID) {
			c.JSON(http.StatusNotFound, gin.H{"error": "Mail configuration not found"})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete mail configuration"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Mail configuration deleted successfully",
	})
}
