package handlers

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"

	mailerService "github.com/psynarios/admin_back/internal/mailer/service"
	"github.com/psynarios/admin_back/internal/scraper/models"
	scraperService "github.com/psynarios/admin_back/internal/scraper/service"
)

// ScraperHandler handles scraper-related HTTP requests
type ScraperHandler struct {
	service     scraperService.ScraperService
	mailService mailerService.MailService
	logger      zerolog.Logger
}

// NewScraperHandler creates a new scraper handler
func NewScraperHandler(service scraperService.ScraperService, mailService mailerService.MailService, logger zerolog.Logger) *ScraperHandler {
	return &ScraperHandler{
		service:     service,
		mailService: mailService,
		logger:      logger.With().Str("component", "scraper_handler").Logger(),
	}
}

// ProcessUsers handles the request to process a user file (keeping for backward compatibility)
func (h *ScraperHandler) ProcessUsers(c *gin.Context) {
	// Get the file from the request
	file, err := c.FormFile("file")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "No file uploaded",
		})
		return
	}

	// Check file extension
	ext := filepath.Ext(file.Filename)
	if ext != ".xlsx" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Only XLSX files are supported",
		})
		return
	}

	// Create a temporary directory to store the file
	tempDir := os.TempDir()
	tempFilePath := filepath.Join(tempDir, fmt.Sprintf("users_%d%s", time.Now().UnixNano(), ext))

	// Save the file to the temporary directory
	if err := c.SaveUploadedFile(file, tempFilePath); err != nil {
		h.logger.Error().Err(err).Msg("Failed to save uploaded file")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to save uploaded file",
		})
		return
	}

	// Process the file
	result, err := h.service.ProcessUserFile(c.Request.Context(), tempFilePath)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to process user file")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("Failed to process file: %v", err),
		})
		return
	}

	// Clean up the temporary file
	os.Remove(tempFilePath)

	// Return the result
	c.JSON(http.StatusOK, result)
}

// ProcessUsersJSON handles the request to process users from JSON data
func (h *ScraperHandler) ProcessUsersJSON(c *gin.Context) {
	// Parse the request body
	var request models.ProcessUsersRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error().Err(err).Msg("Invalid request body")
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request format",
		})
		return
	}

	// Validate the request
	if len(request.Users) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "No users provided",
		})
		return
	}

	// Check if detailed results are requested
	detailed := c.Query("detailed") == "true"

	var result interface{}
	var err error

	if detailed {
		// Process the users with detailed results
		result, err = h.service.ProcessUsersWithDetails(c.Request.Context(), request.Users)
	} else {
		// Process the users with summary results
		result, err = h.service.ProcessUsers(c.Request.Context(), request.Users)
	}

	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to process users")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": fmt.Sprintf("Failed to process users: %v", err),
		})
		return
	}

	// Return the result
	c.JSON(http.StatusOK, result)
}

// SelectTemplatesForUsers handles the request to select templates for users
func (h *ScraperHandler) SelectTemplatesForUsers(c *gin.Context) {
	var request struct {
		Users []models.UserData `json:"users" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error().Err(err).Msg("Invalid request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	h.logger.Info().Int("userCount", len(request.Users)).Msg("Received template selection request")

	// Process users and select templates
	result, err := h.service.SelectTemplatesForUsers(c.Request.Context(), request.Users)
	if err != nil {
		h.logger.Error().Err(err).Msg("Failed to select templates for users")
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to process users"})
		return
	}

	c.JSON(http.StatusOK, result)
}

// SendTemplateEmails handles sending emails with selected templates
func (h *ScraperHandler) SendTemplateEmails(c *gin.Context) {
	// Set a longer timeout for the context
	ctx, cancel := context.WithTimeout(c.Request.Context(), 2*time.Minute)
	c.Request = c.Request.WithContext(ctx)
	defer cancel()

	var request struct {
		Users []struct {
			Email      string                 `json:"email" binding:"required,email"`
			TemplateID string                 `json:"template_id" binding:"required"`
			Data       map[string]interface{} `json:"data" binding:"required"`
			CompanyID  string                 `json:"company_id" binding:"required"`
		} `json:"users" binding:"required"`
		SendToAll bool `json:"send_to_all"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		h.logger.Error().Err(err).Msg("Invalid request")
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	h.logger.Info().
		Int("userCount", len(request.Users)).
		Bool("sendToAll", request.SendToAll).
		Msg("Received email sending request")

	// Validate company IDs and check for mail configs before sending
	for _, user := range request.Users {
		if user.CompanyID == "" {
			h.logger.Error().Str("email", user.Email).Msg("Missing company ID")
			c.JSON(http.StatusBadRequest, gin.H{"error": "Company ID is required for all users"})
			return
		}
	}

	// Group users by template ID and company ID
	templateGroups := make(map[string][]struct {
		Email string
		Data  map[string]interface{}
	})

	for _, user := range request.Users {
		// Create a key combining template ID and company ID
		key := user.TemplateID + ":" + user.CompanyID

		// Add user to the appropriate group
		templateGroups[key] = append(templateGroups[key], struct {
			Email string
			Data  map[string]interface{}
		}{
			Email: user.Email,
			Data:  user.Data,
		})
	}

	// Convert groups to a slice for parallel processing
	type emailGroup struct {
		TemplateID string
		CompanyID  string
		Emails     []string
		Data       map[string]interface{}
	}

	groups := make([]emailGroup, 0, len(templateGroups))
	for key, users := range templateGroups {
		parts := strings.Split(key, ":")
		templateID := parts[0]
		companyID := parts[1]

		if len(users) == 0 {
			continue
		}

		emails := make([]string, len(users))
		for i, user := range users {
			emails[i] = user.Email
		}

		groups = append(groups, emailGroup{
			TemplateID: templateID,
			CompanyID:  companyID,
			Emails:     emails,
			Data:       users[0].Data, // Use first user's data
		})
	}

	// Set up worker pool for parallel processing of groups
	workerCount := 10
	if len(groups) < workerCount {
		workerCount = len(groups)
	}

	type result struct {
		Emails []string
		Error  error
	}

	jobs := make(chan int, len(groups))
	results := make(chan result, len(groups))

	// Start workers
	for w := 1; w <= workerCount; w++ {
		go func(id int) {
			for j := range jobs {
				group := groups[j]
				h.logger.Debug().
					Int("worker", id).
					Str("templateID", group.TemplateID).
					Str("companyID", group.CompanyID).
					Int("recipientCount", len(group.Emails)).
					Msg("Sending grouped email")

				// Get company email from the mail configuration
				companyEmail, err := h.getCompanyEmail(c.Request.Context(), group.CompanyID)
				if err != nil {
					h.logger.Warn().
						Err(err).
						Str("companyID", group.CompanyID).
						Msg("Failed to get company email, will use sender email as recipient")

					// If we can't get the company email, we'll proceed with an empty To field
					// The mail service will use the sender email as the recipient
					companyEmail = ""
				}

				// Use company email as the primary recipient
				to := []string{}
				if companyEmail != "" {
					to = []string{companyEmail}
				}

				err = h.service.SendTemplateEmail(
					c.Request.Context(),
					to,           // Company email as direct recipient
					[]string{},   // No CC
					group.Emails, // All user emails in BCC
					group.TemplateID,
					group.Data,
					group.CompanyID,
				)

				results <- result{
					Emails: group.Emails, // Keep track of all emails for reporting
					Error:  err,
				}
			}
		}(w)
	}

	// Send jobs to workers
	for i := range groups {
		jobs <- i
	}
	close(jobs)

	// Collect results
	successCount := 0
	errorCount := 0
	errors := make(map[string]string)

	for i := 0; i < len(groups); i++ {
		r := <-results
		if r.Error != nil {
			errorCount += len(r.Emails)
			for _, email := range r.Emails {
				errors[email] = r.Error.Error()
			}
			h.logger.Error().
				Err(r.Error).
				Strs("emails", r.Emails).
				Msg("Failed to send grouped email")
		} else {
			successCount += len(r.Emails)
			h.logger.Info().
				Strs("emails", r.Emails).
				Msg("Successfully sent grouped email")
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success_count": successCount,
		"error_count":   errorCount,
		"errors":        errors,
	})
}

// getCompanyEmail retrieves the company email from mail configuration
func (h *ScraperHandler) getCompanyEmail(ctx context.Context, companyID string) (string, error) {
	// Get mail configuration for the company
	config, err := h.mailService.GetMailConfigByCompany(ctx, companyID)
	if err != nil {
		return "", fmt.Errorf("failed to get mail configuration: %w", err)
	}

	// Return the sender email from the configuration
	return config.SenderEmail, nil
}
