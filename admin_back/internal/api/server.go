package api

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/common/config"
)

// Server represents the API server
type Server struct {
	router *gin.Engine
	server *http.Server
	logger zerolog.Logger
	config config.ServerConfig
}

// NewServer creates a new API server
func NewServer(
	router *gin.Engine,
	config config.ServerConfig,
	logger zerolog.Logger,
) *Server {
	return &Server{
		router: router,
		logger: logger.With().Str("component", "api_server").Logger(),
		config: config,
	}
}

// Start starts the API server
func (s *Server) Start() error {
	// Create HTTP server
	s.server = &http.Server{
		Addr:         fmt.Sprintf("%s:%s", s.config.Host, s.config.Port),
		Handler:      s.router,
		ReadTimeout:  s.config.ReadTimeout,
		WriteTimeout: s.config.WriteTimeout,
		IdleTimeout:  s.config.IdleTimeout,
	}

	// Channel to listen for errors coming from the listener
	serverErrors := make(chan error, 1)

	// Start the server
	go func() {
		s.logger.Info().Str("address", s.server.Addr).Msg("API server started")
		serverErrors <- s.server.ListenAndServe()
	}()

	// Listen for OS signals
	shutdown := make(chan os.Signal, 1)
	signal.Notify(shutdown, os.Interrupt, syscall.SIGTERM)

	// Block until an error or interrupt
	select {
	case err := <-serverErrors:
		return fmt.Errorf("server error: %w", err)

	case <-shutdown:
		s.logger.Info().Msg("Server is shutting down")

		// Give outstanding requests a deadline for completion
		ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
		defer cancel()

		// Shutdown the server
		err := s.server.Shutdown(ctx)
		if err != nil {
			s.logger.Error().Err(err).Msg("Failed to gracefully shutdown server")
			return fmt.Errorf("server shutdown error: %w", err)
		}

		s.logger.Info().Msg("Server gracefully stopped")
	}

	return nil
}

// Stop stops the API server
func (s *Server) Stop(ctx context.Context) error {
	return s.server.Shutdown(ctx)
}
