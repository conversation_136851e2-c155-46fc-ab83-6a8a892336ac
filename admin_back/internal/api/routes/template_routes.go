package routes

import (
	"github.com/gin-gonic/gin"

	"github.com/psynarios/admin_back/internal/api/handlers"
	"github.com/psynarios/admin_back/internal/api/middleware"
)

// SetupTemplateRoutes configures the template-related routes
func SetupTemplateRoutes(
	router *gin.RouterGroup,
	handler *handlers.TemplateHandler,
	authMiddleware *middleware.AuthMiddleware,
) {
	templates := router.Group("/templates")
	templates.Use(authMiddleware.Authenticate())

	// All template operations - CSRF removed
	templates.GET("/group/:groupId", handler.ListTemplates)
	templates.GET("/:id", handler.GetTemplate)
	templates.POST("/:id/render", handler.RenderTemplate)
	templates.GET("/alternatives/:companyId", handler.GetAlternativeTemplates) // New endpoint for alternative templates

	// Template group routes
	groups := templates.Group("/groups")
	groups.GET("", handler.ListTemplateGroups)
	groups.GET("/:id", handler.GetTemplateGroup)
	groups.GET("/day/:day/company/:companyId", handler.GetTemplateGroupByDay)

	// Admin-only group routes - CSRF protection removed
	adminGroups := groups.Group("")
	adminGroups.Use(authMiddleware.RequireRole("admin"))
	{
		adminGroups.POST("", handler.CreateTemplateGroup)
		adminGroups.PUT("/:id", handler.UpdateTemplateGroup)
		adminGroups.DELETE("/:id", handler.DeleteTemplateGroup)
	}

	// Admin-only template routes - CSRF protection removed
	adminTemplates := templates.Group("")
	adminTemplates.Use(authMiddleware.RequireRole("admin"))
	{
		adminTemplates.POST("", handler.CreateTemplate)
		adminTemplates.PUT("/:id", handler.UpdateTemplate)
		adminTemplates.DELETE("/:id", handler.DeleteTemplate)
	}
}
