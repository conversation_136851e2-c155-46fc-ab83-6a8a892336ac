package routes

import (
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/api/handlers"
	"github.com/psynarios/admin_back/internal/api/middleware"
)

// SetupRouter configures the API router
func SetupRouter(
	logger zerolog.Logger,
	authHandler *handlers.AuthHandler,
	userHandler *handlers.UserHandler,
	companyHandler *handlers.CompanyHandler,
	mailHandler *handlers.MailHandler,
	templateHandler *handlers.TemplateHandler,
	scraperHandler *handlers.ScraperHandler,
	authMiddleware *middleware.AuthMiddleware,
) *gin.Engine {
	// Create router with default middleware
	router := gin.New()

	// Add custom middleware
	router.Use(middleware.CORS())
	router.Use(middleware.Recovery(logger))
	router.Use(middleware.Logger(logger))

	// Setup route groups
	api := router.Group("/api")

	// Health check
	api.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
		})
	})

	// Setup specific routes
	SetupAuthRoutes(api, authHandler, authMiddleware)
	SetupUserRoutes(api, userHandler, authMiddleware)
	SetupCompanyRoutes(api, companyHandler, authMiddleware)
	SetupMailRoutes(api, mailHandler, authMiddleware)
	SetupTemplateRoutes(api, templateHandler, authMiddleware)
	SetupScraperRoutes(api, scraperHandler, authMiddleware)

	return router
}
