package routes

import (
	"github.com/gin-gonic/gin"

	"github.com/psynarios/admin_back/internal/api/handlers"
	"github.com/psynarios/admin_back/internal/api/middleware"
)

// SetupCompanyRoutes configures the company-related routes
func SetupCompanyRoutes(
	router *gin.RouterGroup,
	handler *handlers.CompanyHandler,
	authMiddleware *middleware.AuthMiddleware,
) {
	companies := router.Group("/companies")
	companies.Use(authMiddleware.Authenticate())

	// Routes for all authenticated users
	companies.GET("", handler.ListCompanies)
	companies.GET("/:id", handler.GetCompany)

	// Admin-only routes
	adminRoutes := companies.Group("")
	adminRoutes.Use(authMiddleware.RequireRole("admin"))
	{
		adminRoutes.POST("", handler.CreateCompany)
		adminRoutes.PUT("/:id", handler.UpdateCompany)
		adminRoutes.DELETE("/:id", handler.DeleteCompany)

		// Certificate management routes
		adminRoutes.POST("/:id/certificate", handler.UploadCertificate)
		adminRoutes.DELETE("/:id/certificate", handler.DeleteCertificate)
		adminRoutes.GET("/:id/certificate", handler.CheckCertificate)
	}
}
