package routes

import (
	"github.com/gin-gonic/gin"

	"github.com/psynarios/admin_back/internal/api/handlers"
	"github.com/psynarios/admin_back/internal/api/middleware"
)

// SetupAuthRoutes configures the authentication routes
func SetupAuthRoutes(router *gin.RouterGroup, handler *handlers.AuthHandler, authMiddleware *middleware.AuthMiddleware) {
	auth := router.Group("/auth")

	// Public routes
	auth.POST("/login", handler.Login)
	auth.POST("/refresh", handler.RefreshToken)
	auth.POST("/register", handler.Register)

	// Protected routes
	protected := auth.Group("")
	protected.Use(authMiddleware.Authenticate())
	{
		protected.POST("/logout", handler.Logout)
		protected.POST("/change-password", handler.ChangePassword)
		protected.GET("/me", handler.GetCurrentUser)
		protected.GET("/status", handler.CheckAuthStatus)
	}
}
