package routes

import (
	"github.com/gin-gonic/gin"

	"github.com/psynarios/admin_back/internal/api/handlers"
	"github.com/psynarios/admin_back/internal/api/middleware"
)

// SetupUserRoutes configures the user management routes
func SetupUserRoutes(
	router *gin.RouterGroup,
	handler *handlers.UserHandler,
	authMiddleware *middleware.AuthMiddleware,
) {
	users := router.Group("/users")
	users.Use(authMiddleware.Authenticate())

	// Admin-only routes
	adminRoutes := users.Group("")
	adminRoutes.Use(authMiddleware.RequireRole("admin"))

	// All operations - CSRF protection removed
	adminRoutes.GET("", handler.GetUsers)
	adminRoutes.GET("/:id", handler.GetUser)
	adminRoutes.POST("", handler.CreateUser)
	adminRoutes.PUT("/:id", handler.UpdateUser)
	adminRoutes.DELETE("/:id", handler.DeleteUser)
}
