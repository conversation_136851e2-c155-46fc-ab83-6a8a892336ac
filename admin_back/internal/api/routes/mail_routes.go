package routes

import (
	"github.com/gin-gonic/gin"

	"github.com/psynarios/admin_back/internal/api/handlers"
	"github.com/psynarios/admin_back/internal/api/middleware"
)

// SetupMailRoutes configures the mail-related routes
func SetupMailRoutes(
	router *gin.RouterGroup,
	handler *handlers.MailHandler,
	authMiddleware *middleware.AuthMiddleware,
) {
	// Main mail group for all mail-related endpoints
	mailGroup := router.Group("/mail")
	mailGroup.Use(authMiddleware.Authenticate())

	// Email sending endpoints (available to all authenticated users)
	mailGroup.POST("/send", handler.SendEmail)
	mailGroup.POST("/send-template", handler.SendTemplateEmail)

	// Mail configuration endpoints (admin only)
	configGroup := mailGroup.Group("/configs")
	configGroup.Use(authMiddleware.RequireRole("admin"))

	// Admin-only configuration operations
	configGroup.GET("", handler.ListMailConfigs)
	configGroup.GET("/:id", handler.GetMailConfig)
	configGroup.GET("/company/:companyId", handler.GetMailConfigByCompany)
	configGroup.POST("", handler.CreateMailConfig)
	configGroup.PUT("/:id", handler.UpdateMailConfig)
	configGroup.DELETE("/:id", handler.DeleteMailConfig)
	configGroup.POST("/test", handler.TestMailConfig)
}
