package routes

import (
	"github.com/gin-gonic/gin"

	"github.com/psynarios/admin_back/internal/api/handlers"
	"github.com/psynarios/admin_back/internal/api/middleware"
)

// SetupScraperRoutes configures the scraper-related routes
func SetupScraperRoutes(
	router *gin.RouterGroup,
	handler *handlers.ScraperHandler,
	authMiddleware *middleware.AuthMiddleware,
) {
	scraper := router.Group("/scraper")
	scraper.Use(authMiddleware.Authenticate())

	// Admin-only routes
	adminRoutes := scraper.Group("")
	adminRoutes.Use(authMiddleware.RequireRole("admin"))
	{
		adminRoutes.POST("/process-users", handler.ProcessUsers)
		adminRoutes.POST("/process-users-json", handler.ProcessUsersJSON)
		adminRoutes.POST("/select-templates", handler.SelectTemplatesForUsers)
		adminRoutes.POST("/send-template-emails", handler.SendTemplateEmails)
	}
}
