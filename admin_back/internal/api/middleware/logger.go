// internal/api/middleware/logger.go
package middleware

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"
)

// Logger is a middleware that logs requests
func Logger(logger zerolog.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Start timer
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		if raw != "" {
			path = path + "?" + raw
		}

		// Process request
		c.Next()

		// Calculate latency
		latency := time.Since(start)

		// Get client IP
		clientIP := c.ClientIP()

		// Get HTTP method
		method := c.Request.Method

		// Get status code
		statusCode := c.Writer.Status()

		// Get error if any
		errorMessage := ""
		for _, err := range c.Errors.Errors() {
			errorMessage += err + ";"
		}

		// Get user ID if authenticated
		userID, exists := c.Get("user_id")

		// Create log event
		logEvent := logger.Info()

		// Adjust log level based on status code
		if statusCode >= 400 && statusCode < 500 {
			logEvent = logger.Warn()
		} else if statusCode >= 500 {
			logEvent = logger.Error()
		}

		// Add common fields
		logEvent = logEvent.
			Str("method", method).
			Str("path", path).
			Int("status", statusCode).
			Str("ip", clientIP).
			Dur("latency", latency)

		// Add error if present
		if errorMessage != "" {
			logEvent = logEvent.Str("error", errorMessage)
		}

		// Add user ID if authenticated
		if exists {
			logEvent = logEvent.Interface("user_id", userID)
		}

		// Log the request
		logEvent.Msg("Request processed")
	}
}
