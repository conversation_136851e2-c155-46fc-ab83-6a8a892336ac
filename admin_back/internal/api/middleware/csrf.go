package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/auth/service"
)

// CsrfMiddleware handles CSRF protection
type CsrfMiddleware struct {
	authService service.AuthService
	logger      zerolog.Logger
}

// NewCsrfMiddleware creates a new CSRF middleware
func NewCsrfMiddleware(authService service.AuthService, logger zerolog.Logger) *CsrfMiddleware {
	return &CsrfMiddleware{
		authService: authService,
		logger:      logger.With().Str("component", "csrf_middleware").Logger(),
	}
}

// VerifyCsrf verifies CSRF token for state-changing requests
func (m *CsrfMiddleware) VerifyCsrf() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Early return for preflight OPTIONS request — no CSRF check here!
		if c.Request.Method == http.MethodOptions {
			c.Next()
			return
		}

		// Only check CSRF for state-changing methods
		if c.Request.Method != http.MethodGet &&
			c.Request.Method != http.MethodHead {

			// Get CSRF token from header
			csrfToken := c.GetHeader("X-CSRF-Token")

			// Log the token for debugging
			m.logger.Debug().
				Str("path", c.Request.URL.Path).
				Str("method", c.Request.Method).
				Str("csrf_token_header", csrfToken).
				Bool("has_csrf_cookie", m.authService.GetCsrfToken(c.Request) != "").
				Msg("CSRF token check")

			// Verify CSRF token
			if !m.authService.VerifyCsrfToken(c.Request, csrfToken) {
				m.logger.Warn().
					Str("path", c.Request.URL.Path).
					Str("method", c.Request.Method).
					Str("ip", c.ClientIP()).
					Str("token_from_header", csrfToken).
					Str("token_from_cookie", m.authService.GetCsrfToken(c.Request)).
					Msg("CSRF token validation failed")

				c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
					"error":   "csrf_validation_failed",
					"message": "Invalid or missing CSRF token",
				})
				return
			}

			m.logger.Debug().
				Str("path", c.Request.URL.Path).
				Str("method", c.Request.Method).
				Msg("CSRF token validated successfully")
		}

		c.Next()
	}
}

// ExemptPaths specifies paths that are exempt from CSRF protection
func (m *CsrfMiddleware) ExemptPaths(paths ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check if current path is in exempt list
		currentPath := c.Request.URL.Path
		for _, path := range paths {
			if path == currentPath {
				c.Next()
				return
			}
		}

		// Apply CSRF verification for non-exempt paths
		m.VerifyCsrf()(c)
	}
}

// ExemptPrefix specifies path prefixes that are exempt from CSRF protection
func (m *CsrfMiddleware) ExemptPrefix(prefixes ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check if current path starts with any exempt prefix
		currentPath := c.Request.URL.Path
		for _, prefix := range prefixes {
			if strings.HasPrefix(currentPath, prefix) {
				c.Next()
				return
			}
		}

		// Apply CSRF verification for non-exempt paths
		m.VerifyCsrf()(c)
	}
}
