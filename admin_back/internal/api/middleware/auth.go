// internal/api/middleware/auth.go
package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/auth/models"
	"github.com/psynarios/admin_back/internal/auth/service"
)

// AuthMiddleware handles authentication and authorization
type AuthMiddleware struct {
	authService service.AuthService
	logger      zerolog.Logger
}

// NewAuthMiddleware creates a new auth middleware
func NewAuthMiddleware(authService service.AuthService, logger zerolog.Logger) *AuthMiddleware {
	return &AuthMiddleware{
		authService: authService,
		logger:      logger.With().Str("component", "auth_middleware").Logger(),
	}
}

// Authenticate checks if the user is authenticated
func (m *AuthMiddleware) Authenticate() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from cookie
		accessToken, _ := m.authService.GetTokenFromCookies(c.Request)

		// If no token in cookie, check Authorization header as fallback
		if accessToken == "" {
			authHeader := c.<PERSON>eader("Authorization")
			if len(authHeader) > 7 && strings.HasPrefix(authHeader, "Bearer ") {
				accessToken = authHeader[7:]
			}
		}

		// Validate token
		if accessToken == "" {
			m.logger.Debug().
				Str("path", c.Request.URL.Path).
				Str("ip", c.ClientIP()).
				Msg("Authentication required but no token provided")

			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "authentication_required"})
			return
		}

		// Parse and validate token
		claims, err := m.authService.ValidateToken(accessToken)
		if err != nil {
			// Check if token expired - provide specific error for client to handle refresh
			if strings.Contains(err.Error(), "token is expired") {
				m.logger.Debug().
					Err(err).
					Str("path", c.Request.URL.Path).
					Msg("Token expired")

				c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
					"error":   "token_expired",
					"message": "Access token expired, please refresh",
				})
				return
			}

			// Other token validation errors
			m.logger.Warn().
				Err(err).
				Str("path", c.Request.URL.Path).
				Msg("Invalid token")

			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "invalid_token"})
			return
		}

		// Set user ID and role in context
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("role", claims.Role)

		m.logger.Debug().
			Int64("user_id", claims.UserID).
			Str("username", claims.Username).
			Str("role", string(claims.Role)).
			Str("path", c.Request.URL.Path).
			Msg("User authenticated")

		c.Next()
	}
}

// RequireRole ensures the user has the required role
func (m *AuthMiddleware) RequireRole(role string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRole, exists := c.Get("role")
		if !exists {
			m.logger.Warn().
				Str("path", c.Request.URL.Path).
				Str("ip", c.ClientIP()).
				Msg("Authentication required for role check")

			c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{"error": "authentication_required"})
			return
		}

		// Convert to string for comparison
		userRoleStr := string(userRole.(models.Role))

		if userRoleStr != role {
			userID, _ := c.Get("user_id")
			m.logger.Warn().
				Interface("user_id", userID).
				Str("required_role", role).
				Str("user_role", userRoleStr).
				Str("path", c.Request.URL.Path).
				Msg("Insufficient permissions")

			c.AbortWithStatusJSON(http.StatusForbidden, gin.H{"error": "insufficient_permissions"})
			return
		}

		c.Next()
	}
}
