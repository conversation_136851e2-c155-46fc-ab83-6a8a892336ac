package models

import (
	"time"
)

type MailConfig struct {
	ID           string    `json:"id" db:"id"`
	CompanyID    string    `json:"company_id" db:"company_id"`
	SenderEmail  string    `json:"sender_email" db:"sender_email"`
	SenderName   string    `json:"sender_name" db:"sender_name"`
	SMTPHost     string    `json:"smtp_host" db:"smtp_host"`
	SMTPPort     int       `json:"smtp_port" db:"smtp_port"`
	SMTPUsername string    `json:"smtp_username" db:"smtp_username"`
	SMTPPassword string    `json:"-" db:"smtp_password"`
	UseSSL       bool      `json:"use_ssl" db:"use_ssl"`
	Active       bool      `json:"active" db:"active"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time `json:"updated_at" db:"updated_at"`
}
