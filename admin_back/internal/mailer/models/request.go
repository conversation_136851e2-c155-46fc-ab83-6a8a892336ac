package models

// MailConfigRequest represents a request to create or update a mail configuration
type MailConfigRequest struct {
	CompanyID    string `json:"company_id" binding:"required,uuid"`
	SenderEmail  string `json:"sender_email" binding:"required,email"`
	SenderName   string `json:"sender_name" binding:"required"`
	SMTPHost     string `json:"smtp_host" binding:"required"`
	SMTPPort     int    `json:"smtp_port" binding:"required"`
	SMTPUsername string `json:"smtp_username"`
	SMTPPassword string `json:"smtp_password"`
	UseSSL       bool   `json:"use_ssl"`
	Active       bool   `json:"active"`
}

// EmailRequest represents a request to send an email
type EmailRequest struct {
	CompanyID string   `json:"company_id" binding:"required,uuid"`
	To        []string `json:"to" binding:"required"`
	Cc        []string `json:"cc"`
	Bcc       []string `json:"bcc"`
	Subject   string   `json:"subject" binding:"required"`
	Body      string   `json:"body"`
}
