package postgres

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/mailer/models"
	"github.com/psynarios/admin_back/internal/mailer/repository"
)

// mailRepository implements the mail Repository interface
type mailRepository struct {
	db     *pgxpool.Pool
	logger zerolog.Logger
}

// NewMailRepository creates a new mail repository
func NewMailRepository(db *pgxpool.Pool, logger zerolog.Logger) repository.Repository {
	return &mailRepository{
		db:     db,
		logger: logger.With().Str("component", "mail_repository").Logger(),
	}
}

// CreateMailConfig creates a new mail configuration
func (r *mailRepository) CreateMailConfig(ctx context.Context, config models.MailConfig) (models.MailConfig, error) {
	query := `
		INSERT INTO mail_configs (
			company_id, sender_email, sender_name, smtp_host, smtp_port,
			smtp_username, smtp_password, use_ssl, active, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11
		) RETURNING id, created_at, updated_at
	`

	now := time.Now()
	config.CreatedAt = now
	config.UpdatedAt = now

	err := r.db.QueryRow(
		ctx,
		query,
		config.CompanyID,
		config.SenderEmail,
		config.SenderName,
		config.SMTPHost,
		config.SMTPPort,
		config.SMTPUsername,
		config.SMTPPassword,
		config.UseSSL,
		config.Active,
		config.CreatedAt,
		config.UpdatedAt,
	).Scan(&config.ID, &config.CreatedAt, &config.UpdatedAt)

	if err != nil {
		return models.MailConfig{}, fmt.Errorf("failed to create mail config: %w", err)
	}

	// If active, deactivate other configs for this company
	if config.Active {
		deactivateQuery := `
			UPDATE mail_configs
			SET active = FALSE, updated_at = $1
			WHERE company_id = $2 AND id != $3
		`

		_, err = r.db.Exec(ctx, deactivateQuery, now, config.CompanyID, config.ID)
		if err != nil {
			r.logger.Error().Err(err).Str("company_id", config.CompanyID).
				Msg("Failed to deactivate other mail configs")
			// Continue anyway since the main operation succeeded
		}
	}

	return config, nil
}

// GetMailConfig gets the active mail configuration
func (r *mailRepository) GetMailConfig(ctx context.Context) (models.MailConfig, error) {
	query := `
		SELECT id, company_id, sender_email, sender_name, smtp_host, smtp_port,
			smtp_username, smtp_password, use_ssl, active, created_at, updated_at
		FROM mail_configs
		WHERE active = TRUE
		LIMIT 1
	`

	var config models.MailConfig
	row := r.db.QueryRow(ctx, query)

	err := row.Scan(
		&config.ID,
		&config.CompanyID,
		&config.SenderEmail,
		&config.SenderName,
		&config.SMTPHost,
		&config.SMTPPort,
		&config.SMTPUsername,
		&config.SMTPPassword,
		&config.UseSSL,
		&config.Active,
		&config.CreatedAt,
		&config.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return models.MailConfig{}, fmt.Errorf("no active mail config found")
		}
		return models.MailConfig{}, fmt.Errorf("failed to get mail config: %w", err)
	}

	return config, nil
}

// GetMailConfigByCompany gets the active mail configuration for a specific company
func (r *mailRepository) GetMailConfigByCompany(ctx context.Context, companyID string) (models.MailConfig, error) {
	query := `
		SELECT id, company_id, sender_email, sender_name, smtp_host, smtp_port,
			smtp_username, smtp_password, use_ssl, active, created_at, updated_at
		FROM mail_configs
		WHERE company_id = $1 AND active = TRUE
		LIMIT 1
	`

	var config models.MailConfig
	row := r.db.QueryRow(ctx, query, companyID)

	err := row.Scan(
		&config.ID,
		&config.CompanyID,
		&config.SenderEmail,
		&config.SenderName,
		&config.SMTPHost,
		&config.SMTPPort,
		&config.SMTPUsername,
		&config.SMTPPassword,
		&config.UseSSL,
		&config.Active,
		&config.CreatedAt,
		&config.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return models.MailConfig{}, fmt.Errorf("no active mail config found for company %s", companyID)
		}
		return models.MailConfig{}, fmt.Errorf("failed to get mail config: %w", err)
	}

	return config, nil
}

// UpdateMailConfig updates a mail configuration
func (r *mailRepository) UpdateMailConfig(ctx context.Context, config models.MailConfig) (models.MailConfig, error) {
	query := `
		UPDATE mail_configs
		SET
			company_id = $1,
			sender_email = $2,
			sender_name = $3,
			smtp_host = $4,
			smtp_port = $5,
			smtp_username = $6,
			smtp_password = CASE WHEN $7 = '' THEN smtp_password ELSE $7 END,
			use_ssl = $8,
			active = $9,
			updated_at = $10
		WHERE id = $11
		RETURNING id, company_id, sender_email, sender_name, smtp_host, smtp_port,
			smtp_username, smtp_password, use_ssl, active, created_at, updated_at
	`

	config.UpdatedAt = time.Now()

	var updatedConfig models.MailConfig
	row := r.db.QueryRow(
		ctx,
		query,
		config.CompanyID,
		config.SenderEmail,
		config.SenderName,
		config.SMTPHost,
		config.SMTPPort,
		config.SMTPUsername,
		config.SMTPPassword,
		config.UseSSL,
		config.Active,
		config.UpdatedAt,
		config.ID,
	)

	err := row.Scan(
		&updatedConfig.ID,
		&updatedConfig.CompanyID,
		&updatedConfig.SenderEmail,
		&updatedConfig.SenderName,
		&updatedConfig.SMTPHost,
		&updatedConfig.SMTPPort,
		&updatedConfig.SMTPUsername,
		&updatedConfig.SMTPPassword,
		&updatedConfig.UseSSL,
		&updatedConfig.Active,
		&updatedConfig.CreatedAt,
		&updatedConfig.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return models.MailConfig{}, fmt.Errorf("mail config not found: %s", config.ID)
		}
		return models.MailConfig{}, fmt.Errorf("failed to update mail config: %w", err)
	}

	// If active, deactivate other configs for this company
	if config.Active {
		deactivateQuery := `
			UPDATE mail_configs
			SET active = FALSE, updated_at = $1
			WHERE company_id = $2 AND id != $3
		`

		_, err = r.db.Exec(ctx, deactivateQuery, config.UpdatedAt, config.CompanyID, config.ID)
		if err != nil {
			r.logger.Error().Err(err).Str("company_id", config.CompanyID).
				Msg("Failed to deactivate other mail configs")
			// Continue anyway since the main operation succeeded
		}
	}

	return updatedConfig, nil
}

// ListMailConfigs lists all mail configs
func (r *mailRepository) ListMailConfigs(ctx context.Context) ([]models.MailConfig, error) {
	query := `
		SELECT id, company_id, sender_email, sender_name, smtp_host, smtp_port,
			smtp_username, smtp_password, use_ssl, active, created_at, updated_at
		FROM mail_configs
		ORDER BY id
	`

	rows, err := r.db.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to list mail configs: %w", err)
	}
	defer rows.Close()

	var configs []models.MailConfig
	for rows.Next() {
		var config models.MailConfig
		err := rows.Scan(
			&config.ID,
			&config.CompanyID,
			&config.SenderEmail,
			&config.SenderName,
			&config.SMTPHost,
			&config.SMTPPort,
			&config.SMTPUsername,
			&config.SMTPPassword,
			&config.UseSSL,
			&config.Active,
			&config.CreatedAt,
			&config.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan mail config: %w", err)
		}
		configs = append(configs, config)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating mail configs: %w", err)
	}

	return configs, nil
}

// DeleteMailConfig deletes a mail configuration by ID
func (r *mailRepository) DeleteMailConfig(ctx context.Context, id string) error {
	query := `DELETE FROM mail_configs WHERE id = $1`

	result, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete mail config: %w", err)
	}

	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		return fmt.Errorf("mail config with ID %s not found", id)
	}

	r.logger.Info().Str("id", id).Msg("Mail config deleted successfully")
	return nil
}
