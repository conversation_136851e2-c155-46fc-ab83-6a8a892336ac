package repository

import (
	"context"

	"github.com/psynarios/admin_back/internal/mailer/models"
)

// Repository defines the mail repository interface
type Repository interface {
	CreateMailConfig(ctx context.Context, config models.MailConfig) (models.MailConfig, error)
	GetMailConfig(ctx context.Context) (models.MailConfig, error)
	GetMailConfigByCompany(ctx context.Context, companyID string) (models.MailConfig, error)
	UpdateMailConfig(ctx context.Context, config models.MailConfig) (models.MailConfig, error)
	ListMailConfigs(ctx context.Context) ([]models.MailConfig, error)
	DeleteMailConfig(ctx context.Context, id string) error
}
