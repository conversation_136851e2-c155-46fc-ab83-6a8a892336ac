// internal/mailer/service/sender.go
package service

import (
	"crypto/tls"
	"fmt"
	"net/smtp"
	"strings"
	"sync"
	"time"

	"github.com/psynarios/admin_back/internal/mailer/models"
	"github.com/psynarios/admin_back/pkg/utils"
	"github.com/rs/zerolog"
)

// Connection pooling structures
type smtpClientInfo struct {
	client   *smtp.Client
	lastUsed time.Time
	authDone bool
}

var (
	smtpClientPool = make(map[string]*smtpClientInfo)
	smtpPoolMutex  sync.Mutex
	// Connection will be reset if idle for more than maxIdleTime
	maxIdleTime = 5 * time.Minute
	// Maximum number of connections in the pool
	maxPoolSize = 10
)

// EmailSender defines the interface for sending emails
type EmailSender interface {
	SendMail(config models.MailConfig, to, cc, bcc []string, subject, body string) error
	CleanupConnections()
}

// emailSender implements EmailSender
type emailSender struct {
	logger zerolog.Logger
}

// NewEmailSender creates a new email sender
func NewEmailSender(logger zerolog.Logger) EmailSender {
	sender := &emailSender{
		logger: logger.With().Str("component", "email_sender").Logger(),
	}

	// Start a goroutine to clean up stale connections
	go sender.cleanupStaleConnections()

	return sender
}

// cleanupStaleConnections periodically removes stale SMTP connections
func (s *emailSender) cleanupStaleConnections() {
	ticker := time.NewTicker(maxIdleTime / 2)
	defer ticker.Stop()

	for range ticker.C {
		s.CleanupConnections()
	}
}

// CleanupConnections removes idle connections
func (s *emailSender) CleanupConnections() {
	cleanupTime := time.Now().Add(-maxIdleTime)

	smtpPoolMutex.Lock()
	defer smtpPoolMutex.Unlock()

	for key, info := range smtpClientPool {
		if info.lastUsed.Before(cleanupTime) {
			s.logger.Debug().Str("connection", key).Msg("Closing stale SMTP connection")
			info.client.Quit()
			delete(smtpClientPool, key)
		}
	}
}

// getConnectionPoolKey generates a unique key for the connection pool
func getConnectionPoolKey(config models.MailConfig) string {
	return fmt.Sprintf("%s:%d:%s:%t", config.SMTPHost, config.SMTPPort, config.SMTPUsername, config.UseSSL)
}

// createNewSMTPConnection creates a new SMTP connection based on the configuration
func (s *emailSender) createNewSMTPConnection(config models.MailConfig) (*smtp.Client, error) {
	// Server address
	smtpAddr := fmt.Sprintf("%s:%d", config.SMTPHost, config.SMTPPort)
	var c *smtp.Client
	var err error

	// Based on port and SSL setting, create the appropriate connection
	if config.SMTPPort == 465 && config.UseSSL {
		// For SSL (port 465), connect with TLS first
		s.logger.Debug().Msg("Creating new direct SSL connection")

		// Connect using TLS
		tlsConfig := &tls.Config{
			ServerName:         config.SMTPHost,
			InsecureSkipVerify: false,
		}

		conn, err := tls.Dial("tcp", smtpAddr, tlsConfig)
		if err != nil {
			return nil, fmt.Errorf("failed to connect with TLS: %w", err)
		}

		c, err = smtp.NewClient(conn, config.SMTPHost)
		if err != nil {
			conn.Close()
			return nil, fmt.Errorf("failed to create SMTP client: %w", err)
		}
	} else if config.SMTPPort == 587 {
		// For STARTTLS (port 587), connect normally and then upgrade
		s.logger.Debug().Msg("Creating new STARTTLS connection")

		// Connect to server
		c, err = smtp.Dial(smtpAddr)
		if err != nil {
			return nil, fmt.Errorf("failed to connect to SMTP server: %w", err)
		}

		// Send EHLO
		if err = c.Hello("psynarios.com"); err != nil {
			c.Close()
			return nil, fmt.Errorf("EHLO failed: %w", err)
		}

		// Start TLS
		if err = c.StartTLS(&tls.Config{ServerName: config.SMTPHost}); err != nil {
			c.Close()
			return nil, fmt.Errorf("StartTLS failed: %w", err)
		}
	} else {
		// For plain SMTP without encryption
		s.logger.Debug().Msg("Creating new standard SMTP connection")
		c, err = smtp.Dial(smtpAddr)
		if err != nil {
			return nil, fmt.Errorf("failed to connect to SMTP server: %w", err)
		}
	}

	// Only authenticate if username and password are provided
	if config.SMTPUsername != "" && config.SMTPPassword != "" {
		// Try to decrypt the password
		smtpPassword := config.SMTPPassword

		// First try to decrypt with proper encryption
		if utils.IsEncrypted(smtpPassword) {
			decrypted, err := utils.DecryptString(smtpPassword)
			if err != nil {
				s.logger.Warn().Err(err).Msg("Failed to decrypt password, trying Base64 decode as fallback")
				// Try Base64 as fallback for backward compatibility
				decoded, err := utils.DecodeBase64(smtpPassword)
				if err != nil {
					s.logger.Warn().Err(err).Msg("Failed to decode Base64 password, using as-is")
				} else {
					smtpPassword = decoded
					s.logger.Debug().Msg("Successfully decoded Base64 password")
				}
			} else {
				smtpPassword = decrypted
				s.logger.Debug().Msg("Successfully decrypted password")
			}
		} else if utils.IsBase64(smtpPassword) {
			// For backward compatibility with Base64 encoded passwords
			decoded, err := utils.DecodeBase64(smtpPassword)
			if err != nil {
				s.logger.Warn().Err(err).Msg("Failed to decode Base64 password, using as-is")
			} else {
				smtpPassword = strings.TrimSpace(decoded)
				s.logger.Debug().Msg("Successfully decoded Base64 password")
			}
		} else {
			s.logger.Debug().Msg("Password is in plain text, using as-is")
		}

		s.logger.Debug().Int("password_length", len(smtpPassword)).Msg("Password length check")

		// Create auth with decrypted password
		auth := smtp.PlainAuth("", config.SMTPUsername, smtpPassword, config.SMTPHost)

		// Authenticate
		if err = c.Auth(auth); err != nil {
			c.Close()
			return nil, fmt.Errorf("authentication failed: %w", err)
		}
	}

	return c, nil
}

// SendMail sends an email using the SMTP configuration with connection pooling
func (s *emailSender) SendMail(config models.MailConfig, to, cc, bcc []string, subject, body string) error {
	startTime := time.Now()
	defer func() {
		s.logger.Debug().Dur("duration", time.Since(startTime)).Msg("sendMail completed")
	}()

	// Format message content
	var msg strings.Builder
	msg.WriteString(fmt.Sprintf("From: %s <%s>\r\n", config.SenderName, config.SenderEmail))
	msg.WriteString(fmt.Sprintf("To: %s\r\n", strings.Join(to, ", ")))
	if len(cc) > 0 {
		msg.WriteString(fmt.Sprintf("Cc: %s\r\n", strings.Join(cc, ", ")))
	}
	msg.WriteString(fmt.Sprintf("Subject: %s\r\n", subject))
	msg.WriteString("MIME-Version: 1.0\r\n")
	msg.WriteString("Content-Type: multipart/alternative; boundary=boundary123456\r\n\r\n")

	// Add text part (simplified version of HTML)
	msg.WriteString("--boundary123456\r\n")
	msg.WriteString("Content-Type: text/plain; charset=UTF-8\r\n\r\n")
	msg.WriteString("Please view this email in an HTML-capable email client.")
	msg.WriteString("\r\n\r\n")

	// Add HTML part
	msg.WriteString("--boundary123456\r\n")
	msg.WriteString("Content-Type: text/html; charset=UTF-8\r\n\r\n")
	msg.WriteString(body)
	msg.WriteString("\r\n\r\n")
	msg.WriteString("--boundary123456--")

	// Combine all recipients
	var allRecipients []string
	allRecipients = append(allRecipients, to...)
	allRecipients = append(allRecipients, cc...)
	allRecipients = append(allRecipients, bcc...)

	// Pool key for connection lookup
	poolKey := getConnectionPoolKey(config)

	// Try to get a connection from the pool
	smtpPoolMutex.Lock()
	clientInfo, exists := smtpClientPool[poolKey]

	// Check if connection exists and is usable
	validConnection := exists && clientInfo != nil
	if validConnection {
		// Test the connection with a NOOP command
		if err := clientInfo.client.Noop(); err != nil {
			s.logger.Debug().Err(err).Msg("Pooled connection is no longer valid")
			// Close and remove the invalid connection
			clientInfo.client.Close()
			delete(smtpClientPool, poolKey)
			validConnection = false
		} else {
			// Update last used time
			clientInfo.lastUsed = time.Now()
		}
	}
	smtpPoolMutex.Unlock()

	var c *smtp.Client
	var err error

	// If connection doesn't exist or is invalid, create a new one
	if !validConnection {
		connectionStart := time.Now()

		// Create new connection
		c, err = s.createNewSMTPConnection(config)
		if err != nil {
			return fmt.Errorf("failed to create SMTP connection: %w", err)
		}

		// Manage pool size
		smtpPoolMutex.Lock()

		// Check if we need to remove old connections to stay under the limit
		if len(smtpClientPool) >= maxPoolSize {
			// Find the oldest connection
			var oldestKey string
			var oldestTime time.Time
			first := true

			for k, info := range smtpClientPool {
				if first || info.lastUsed.Before(oldestTime) {
					oldestKey = k
					oldestTime = info.lastUsed
					first = false
				}
			}

			// Remove the oldest connection
			if oldestKey != "" {
				oldClient := smtpClientPool[oldestKey]
				if oldClient != nil && oldClient.client != nil {
					oldClient.client.Quit()
				}
				delete(smtpClientPool, oldestKey)
				s.logger.Debug().Str("connection", oldestKey).Msg("Removed oldest connection from pool due to size limit")
			}
		}

		// Store the new client in the pool
		smtpClientPool[poolKey] = &smtpClientInfo{
			client:   c,
			lastUsed: time.Now(),
			authDone: config.SMTPUsername != "" && config.SMTPPassword != "",
		}
		smtpPoolMutex.Unlock()

		s.logger.Debug().Dur("duration", time.Since(connectionStart)).Msg("SMTP connection created")
	} else {
		s.logger.Debug().Msg("Using existing SMTP connection from pool")
		c = clientInfo.client
	}

	// Use the connection (existing or new)
	sendingStart := time.Now()

	// Reset the connection to start a new mail
	if err = c.Reset(); err != nil {
		s.logger.Warn().Err(err).Msg("Failed to reset SMTP connection, creating new one")

		// Close the existing connection
		c.Close()

		// Remove the invalid connection from the pool
		smtpPoolMutex.Lock()
		delete(smtpClientPool, poolKey)
		smtpPoolMutex.Unlock()

		// Create a new connection
		c, err = s.createNewSMTPConnection(config)
		if err != nil {
			return fmt.Errorf("failed to recreate SMTP connection: %w", err)
		}

		// Update the pool with the new connection
		smtpPoolMutex.Lock()
		smtpClientPool[poolKey] = &smtpClientInfo{
			client:   c,
			lastUsed: time.Now(),
			authDone: config.SMTPUsername != "" && config.SMTPPassword != "",
		}
		smtpPoolMutex.Unlock()
	}

	// Set sender
	if err = c.Mail(config.SenderEmail); err != nil {
		// If this fails, something is wrong with the connection
		c.Close()
		smtpPoolMutex.Lock()
		delete(smtpClientPool, poolKey)
		smtpPoolMutex.Unlock()
		return fmt.Errorf("MAIL FROM failed: %w", err)
	}

	// Set recipients
	for _, recipient := range allRecipients {
		if err = c.Rcpt(recipient); err != nil {
			return fmt.Errorf("RCPT TO failed for %s: %w", recipient, err)
		}
	}

	// Send data
	w, err := c.Data()
	if err != nil {
		return fmt.Errorf("DATA failed: %w", err)
	}

	_, err = w.Write([]byte(msg.String()))
	if err != nil {
		return fmt.Errorf("failed to write message: %w", err)
	}

	err = w.Close()
	if err != nil {
		return fmt.Errorf("failed to close message: %w", err)
	}

	s.logger.Debug().Dur("duration", time.Since(sendingStart)).Msg("Email message transmission completed")

	return nil
}
