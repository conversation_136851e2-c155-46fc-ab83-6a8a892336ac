// internal/mailer/service/mail_service.go
package service

import (
	"context"
	"fmt"
	"time"

	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/mailer/models"
	"github.com/psynarios/admin_back/internal/mailer/repository"
	"github.com/psynarios/admin_back/pkg/utils"
)

// MailService defines the mail service interface
type MailService interface {
	// Mail config
	CreateMailConfig(ctx context.Context, config models.MailConfig) (models.MailConfig, error)
	GetMailConfig(ctx context.Context) (models.MailConfig, error)
	GetMailConfigByCompany(ctx context.Context, companyID string) (models.MailConfig, error)
	UpdateMailConfig(ctx context.Context, config models.MailConfig) (models.MailConfig, error)
	ListMailConfigs(ctx context.Context) ([]models.MailConfig, error)
	DeleteMailConfig(ctx context.Context, id string) error

	// Sending emails
	SendEmail(ctx context.Context, to, cc, bcc []string, subject, Body string, companyID string) error

	// Template-based email sending (new)
	SendTemplateEmail(ctx context.Context, to, cc, bcc []string, templateID string, data map[string]interface{}, companyID string) error
}

// mailService implements MailService
type mailService struct {
	repo        repository.Repository
	emailSender EmailSender
	logger      zerolog.Logger
}

// NewMailService creates a new mail service
func NewMailService(repo repository.Repository, logger zerolog.Logger) MailService {
	return &mailService{
		repo:        repo,
		emailSender: NewEmailSender(logger),
		logger:      logger.With().Str("component", "mail_service").Logger(),
	}
}

// CreateMailConfig creates a new mail configuration
func (s *mailService) CreateMailConfig(ctx context.Context, config models.MailConfig) (models.MailConfig, error) {
	// If the password isn't already encrypted, encrypt it
	if config.SMTPPassword != "" && !utils.IsEncrypted(config.SMTPPassword) {
		encrypted, err := utils.EncryptString(config.SMTPPassword)
		if err != nil {
			return models.MailConfig{}, fmt.Errorf("failed to encrypt password: %w", err)
		}
		config.SMTPPassword = encrypted
		s.logger.Debug().Msg("Encrypted password for storage")
	}

	return s.repo.CreateMailConfig(ctx, config)
}

// GetMailConfig retrieves the active mail configuration
func (s *mailService) GetMailConfig(ctx context.Context) (models.MailConfig, error) {
	return s.repo.GetMailConfig(ctx)
}

// GetMailConfigByCompany retrieves the active mail configuration for a company
func (s *mailService) GetMailConfigByCompany(ctx context.Context, companyID string) (models.MailConfig, error) {
	return s.repo.GetMailConfigByCompany(ctx, companyID)
}

// UpdateMailConfig updates a mail configuration
func (s *mailService) UpdateMailConfig(ctx context.Context, config models.MailConfig) (models.MailConfig, error) {
	if config.SMTPPassword != "" && !utils.IsEncrypted(config.SMTPPassword) {
		encrypted, err := utils.EncryptString(config.SMTPPassword)
		if err != nil {
			return models.MailConfig{}, fmt.Errorf("failed to encrypt password: %w", err)
		}
		config.SMTPPassword = encrypted
		s.logger.Debug().Msg("Encrypted password for storage")
	}

	// If password is empty, get the existing password
	if config.SMTPPassword == "" {
		var existingConfig models.MailConfig
		var err error

		if config.CompanyID != "" {
			existingConfig, err = s.repo.GetMailConfigByCompany(ctx, config.CompanyID)
		} else {
			existingConfig, err = s.repo.GetMailConfig(ctx)
		}

		if err != nil {
			return models.MailConfig{}, fmt.Errorf("failed to get existing config: %w", err)
		}
		config.SMTPPassword = existingConfig.SMTPPassword
	}

	return s.repo.UpdateMailConfig(ctx, config)
}

// SendEmail sends an email
func (s *mailService) SendEmail(ctx context.Context, to, cc, bcc []string, subject, body, companyID string) error {
	startTime := time.Now()
	defer func() {
		s.logger.Debug().Dur("duration", time.Since(startTime)).Msg("SendEmail completed")
	}()

	// Get mail configuration for the company
	config, err := s.repo.GetMailConfigByCompany(ctx, companyID)
	if err != nil {
		return fmt.Errorf("failed to get mail configuration for company %s: %w", companyID, err)
	}

	// Send the email
	sendStart := time.Now()
	err = s.emailSender.SendMail(config, to, cc, bcc, subject, body)
	if err != nil {
		return fmt.Errorf("failed to send email: %w", err)
	}
	s.logger.Debug().Dur("duration", time.Since(sendStart)).Msg("Email sending completed")

	s.logger.Info().
		Strs("recipients", to).
		Msg("Email sent successfully")

	return nil
}

// ListMailConfigs lists all mail configurations
func (s *mailService) ListMailConfigs(ctx context.Context) ([]models.MailConfig, error) {
	return s.repo.ListMailConfigs(ctx)
}

func (s *mailService) DeleteMailConfig(ctx context.Context, id string) error {
	return s.repo.DeleteMailConfig(ctx, id)
}

// SendTemplateEmail sends an email using a template
func (s *mailService) SendTemplateEmail(ctx context.Context, to, cc, bcc []string, templateID string, data map[string]interface{}, companyID string) error {
	startTime := time.Now()
	defer func() {
		s.logger.Debug().Dur("duration", time.Since(startTime)).Msg("SendTemplateEmail completed")
	}()

	// Log the request
	s.logger.Info().
		Str("templateID", templateID).
		Str("companyID", companyID).
		Interface("recipients", to).
		Msg("Sending template-based email")

	// Check if we have the required template data
	if data == nil {
		return fmt.Errorf("template data cannot be nil")
	}

	// Extract subject and body from the data
	var subject, body string
	var ok bool

	// Check if subject and body are directly provided in the data
	subject, ok = data["subject"].(string)
	if !ok {
		return fmt.Errorf("subject not provided in template data")
	}

	body, ok = data["body"].(string)
	if !ok {
		return fmt.Errorf("body not provided in template data")
	}

	// Replace template variables in subject and body
	subject = utils.ReplaceTemplateVariables(subject, data)
	body = utils.ReplaceTemplateVariables(body, data)

	// Prepare the HTML for email sending
	body = utils.PrepareEmailHTML(body)

	// Send the email with the processed subject and body
	return s.SendEmail(ctx, to, cc, bcc, subject, body, companyID)
}
