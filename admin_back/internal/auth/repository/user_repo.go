// internal/auth/repository/user_repo.go
package repository

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/psynarios/admin_back/internal/auth/models"
	"github.com/psynarios/admin_back/internal/common/database"
)

type UserRepository interface {
	FindAll(ctx context.Context, page, pageSize int) ([]models.User, int64, error)
	FindByID(ctx context.Context, id int64) (*models.User, error)
	FindByUsername(ctx context.Context, username string) (*models.User, error)
	FindByEmail(ctx context.Context, email string) (*models.User, error)
	Create(ctx context.Context, user *models.User) error
	Update(ctx context.Context, user *models.User) error
	Delete(ctx context.Context, id int64) error
	UpdatePassword(ctx context.Context, id int64, passwordHash string) error
	UpdateLastLogin(ctx context.Context, id int64) error
}

type postgresUserRepository struct {
	db *database.PostgresDB
}

func NewPostgresUserRepository(db *database.PostgresDB) UserRepository {
	return &postgresUserRepository{
		db: db,
	}
}

// FindAll retrieves all users from the database with pagination
func (r *postgresUserRepository) FindAll(ctx context.Context, page, pageSize int) ([]models.User, int64, error) {
	// Calculate offset
	offset := (page - 1) * pageSize
	if offset < 0 {
		offset = 0
	}

	// Get total count
	var totalCount int64
	countQuery := `SELECT COUNT(*) FROM users`
	err := r.db.QueryRow(ctx, countQuery).Scan(&totalCount)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count users: %w", err)
	}

	// Get paginated users
	query := `SELECT id, email, username, password_hash, role, first_name, last_name, 
              active, last_login, created_at, updated_at 
              FROM users 
              ORDER BY created_at DESC
              LIMIT $1 OFFSET $2`

	rows, err := r.db.Query(ctx, query, pageSize, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query users: %w", err)
	}
	defer rows.Close()

	var users []models.User
	for rows.Next() {
		var user models.User
		if err := rows.Scan(
			&user.ID,
			&user.Email,
			&user.Username,
			&user.Password,
			&user.Role,
			&user.FirstName,
			&user.LastName,
			&user.Active,
			&user.LastLogin,
			&user.CreatedAt,
			&user.UpdatedAt,
		); err != nil {
			return nil, 0, fmt.Errorf("failed to scan user row: %w", err)
		}
		users = append(users, user)
	}

	if err := rows.Err(); err != nil {
		return nil, 0, fmt.Errorf("error iterating user rows: %w", err)
	}

	return users, totalCount, nil
}

// FindByID retrieves a user by ID
func (r *postgresUserRepository) FindByID(ctx context.Context, id int64) (*models.User, error) {
	query := `SELECT id, email, username, password_hash, role, first_name, last_name, 
              active, last_login, created_at, updated_at 
              FROM users 
              WHERE id = $1`

	row := r.db.QueryRow(ctx, query, id)

	var user models.User
	err := row.Scan(
		&user.ID,
		&user.Email,
		&user.Username,
		&user.Password,
		&user.Role,
		&user.FirstName,
		&user.LastName,
		&user.Active,
		&user.LastLogin,
		&user.CreatedAt,
		&user.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("user not found: %w", err)
		}
		return nil, fmt.Errorf("failed to scan user: %w", err)
	}

	return &user, nil
}

// FindByUsername retrieves a user by username
func (r *postgresUserRepository) FindByUsername(ctx context.Context, username string) (*models.User, error) {
	query := `SELECT id, email, username, password_hash, role, first_name, last_name, 
              active, last_login, created_at, updated_at 
              FROM users 
              WHERE username = $1`

	row := r.db.QueryRow(ctx, query, username)

	var user models.User
	err := row.Scan(
		&user.ID,
		&user.Email,
		&user.Username,
		&user.Password,
		&user.Role,
		&user.FirstName,
		&user.LastName,
		&user.Active,
		&user.LastLogin,
		&user.CreatedAt,
		&user.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("user not found: %w", err)
		}
		return nil, fmt.Errorf("failed to scan user: %w", err)
	}

	return &user, nil
}

// FindByEmail retrieves a user by email
func (r *postgresUserRepository) FindByEmail(ctx context.Context, email string) (*models.User, error) {
	query := `SELECT id, email, username, password_hash, role, first_name, last_name, 
              active, last_login, created_at, updated_at 
              FROM users 
              WHERE email = $1`

	row := r.db.QueryRow(ctx, query, email)

	var user models.User
	err := row.Scan(
		&user.ID,
		&user.Email,
		&user.Username,
		&user.Password,
		&user.Role,
		&user.FirstName,
		&user.LastName,
		&user.Active,
		&user.LastLogin,
		&user.CreatedAt,
		&user.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, fmt.Errorf("user not found: %w", err)
		}
		return nil, fmt.Errorf("failed to scan user: %w", err)
	}

	return &user, nil
}

// Create inserts a new user into the database
func (r *postgresUserRepository) Create(ctx context.Context, user *models.User) error {
	// Set created_at and updated_at if they're not already set
	now := time.Now()
	if user.CreatedAt.IsZero() {
		user.CreatedAt = now
	}
	if user.UpdatedAt.IsZero() {
		user.UpdatedAt = now
	}

	query := `
        INSERT INTO users (email, username, password_hash, role, first_name, last_name, active, created_at, updated_at) 
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9) 
        RETURNING id, created_at, updated_at
    `

	err := r.db.QueryRow(
		ctx,
		query,
		user.Email,
		user.Username,
		user.Password,
		user.Role,
		user.FirstName,
		user.LastName,
		user.Active,
		user.CreatedAt,
		user.UpdatedAt,
	).Scan(&user.ID, &user.CreatedAt, &user.UpdatedAt)

	if err != nil {
		return fmt.Errorf("failed to create user: %w", err)
	}

	return nil
}

// Update updates an existing user in the database
func (r *postgresUserRepository) Update(ctx context.Context, user *models.User) error {
	query := `UPDATE users 
              SET email = $1, 
                  username = $2, 
                  role = $3, 
                  first_name = $4, 
                  last_name = $5, 
                  active = $6, 
                  updated_at = NOW() 
              WHERE id = $7 
              RETURNING updated_at`

	err := r.db.QueryRow(
		ctx,
		query,
		user.Email,
		user.Username,
		user.Role,
		user.FirstName,
		user.LastName,
		user.Active,
		user.ID,
	).Scan(&user.UpdatedAt)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return fmt.Errorf("user not found: %w", err)
		}
		return fmt.Errorf("failed to update user: %w", err)
	}

	return nil
}

// Delete removes a user from the database
func (r *postgresUserRepository) Delete(ctx context.Context, id int64) error {
	query := `DELETE FROM users WHERE id = $1`

	ct, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete user: %w", err)
	}

	if ct.RowsAffected() == 0 {
		return fmt.Errorf("user not found")
	}

	return nil
}

// UpdatePassword updates a user's password
func (r *postgresUserRepository) UpdatePassword(ctx context.Context, id int64, passwordHash string) error {
	query := `UPDATE users 
              SET password_hash = $1, 
                  updated_at = NOW() 
              WHERE id = $2`

	ct, err := r.db.Exec(ctx, query, passwordHash, id)
	if err != nil {
		return fmt.Errorf("failed to update password: %w", err)
	}

	if ct.RowsAffected() == 0 {
		return fmt.Errorf("user not found")
	}

	return nil
}

// UpdateLastLogin updates the last login timestamp for a user
func (r *postgresUserRepository) UpdateLastLogin(ctx context.Context, id int64) error {
	now := time.Now()
	query := `UPDATE users 
              SET last_login = $1 
              WHERE id = $2`

	ct, err := r.db.Exec(ctx, query, now, id)
	if err != nil {
		return fmt.Errorf("failed to update last login: %w", err)
	}

	if ct.RowsAffected() == 0 {
		return fmt.Errorf("user not found")
	}

	return nil
}
