package service

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/rs/zerolog"
	"golang.org/x/crypto/bcrypt"

	"github.com/psynarios/admin_back/internal/auth/models"
	"github.com/psynarios/admin_back/internal/auth/repository"
)

// AuthService defines the interface for authentication operations
type AuthService interface {
	Login(ctx context.Context, email, password string) (*TokenDetails, *models.UserResponse, error)
	SetAuthCookies(w http.ResponseWriter, tokens *TokenDetails)
	SetCsrfCookie(w http.ResponseWriter, csrfToken string)
	ClearAuthCookies(w http.ResponseWriter)
	GetTokenFromCookies(r *http.Request) (accessToken, refreshToken string)
	GetCsrfToken(r *http.Request) string
	Register(ctx context.Context, req *models.CreateUserRequest) (*models.UserResponse, error)
	Logout(ctx context.Context, w http.ResponseWriter, refreshToken string) error
	RefreshToken(ctx context.Context, refreshToken string) (*TokenDetails, error)
	ValidateToken(accessToken string) (*AccessTokenClaims, error)
	ChangePassword(ctx context.Context, userID int64, currentPassword, newPassword string) error
	GetUserByID(ctx context.Context, id int64) (*models.UserResponse, error)
	VerifyCsrfToken(r *http.Request, providedToken string) bool
	ListUsers(ctx context.Context, page, pageSize int) ([]*models.UserResponse, int64, error)
	UpdateUser(ctx context.Context, userID int64, req *models.UpdateUserRequest) (*models.UserResponse, error)
	DeleteUser(ctx context.Context, userID int64) error
	RefreshTokenForUser(ctx context.Context, userID int64) (*TokenDetails, error)
}

// TokenDetails contains auth token details
type TokenDetails struct {
	AccessToken  string
	RefreshToken string
	AccessUuid   string
	RefreshUuid  string
	AtExpires    int64
	RtExpires    int64
	CsrfToken    string // Added CSRF token
}

// CookieConfig holds configuration for auth cookies
type CookieConfig struct {
	Domain   string
	Path     string
	MaxAge   int
	Secure   bool
	HTTPOnly bool
	SameSite http.SameSite
}

// AccessTokenClaims defines JWT claims for access tokens
type AccessTokenClaims struct {
	UserID   int64       `json:"user_id"`
	Username string      `json:"username"`
	Role     models.Role `json:"role"`
	UUID     string      `json:"uuid"`
	jwt.RegisteredClaims
}

// RefreshTokenClaims defines JWT claims for refresh tokens
type RefreshTokenClaims struct {
	UserID int64  `json:"user_id"`
	UUID   string `json:"uuid"`
	jwt.RegisteredClaims
}

// authServiceImpl implements the AuthService interface
type authServiceImpl struct {
	userRepo        repository.UserRepository
	tokenRepo       repository.TokenRepository
	accessSecret    string
	refreshSecret   string
	accessLifetime  time.Duration
	refreshLifetime time.Duration
	cookieConfig    CookieConfig
	logger          zerolog.Logger
}

// NewAuthService creates a new auth service
func NewAuthService(
	userRepo repository.UserRepository,
	tokenRepo repository.TokenRepository,
	accessSecret string,
	refreshSecret string,
	accessLifetime time.Duration,
	refreshLifetime time.Duration,
	cookieConfig CookieConfig,
	logger zerolog.Logger,
) AuthService {
	return &authServiceImpl{
		userRepo:        userRepo,
		tokenRepo:       tokenRepo,
		accessSecret:    accessSecret,
		refreshSecret:   refreshSecret,
		accessLifetime:  accessLifetime,
		refreshLifetime: refreshLifetime,
		cookieConfig:    cookieConfig,
		logger:          logger.With().Str("component", "auth_service").Logger(),
	}
}

// Login authenticates a user and generates JWT tokens
func (s *authServiceImpl) Login(ctx context.Context, email, password string) (*TokenDetails, *models.UserResponse, error) {
	// Find user by email
	user, err := s.userRepo.FindByEmail(ctx, email)
	if err != nil {
		s.logger.Debug().
			Str("email", email).
			Err(err).
			Msg("Invalid login credentials")
		return nil, nil, fmt.Errorf("invalid credentials")
	}

	// Check if user is active
	if !user.Active {
		s.logger.Debug().
			Str("email", email).
			Int64("user_id", user.ID).
			Msg("Account is disabled")
		return nil, nil, fmt.Errorf("account is disabled")
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password)); err != nil {
		s.logger.Debug().
			Str("email", email).
			Int64("user_id", user.ID).
			Err(err).
			Msg("Invalid password")
		return nil, nil, fmt.Errorf("invalid credentials")
	}

	// Generate tokens
	tokens, err := s.createTokens(ctx, user)
	if err != nil {
		s.logger.Error().
			Str("email", email).
			Int64("user_id", user.ID).
			Err(err).
			Msg("Failed to generate tokens")
		return nil, nil, fmt.Errorf("failed to generate tokens: %w", err)
	}

	// Update last login time
	if err := s.userRepo.UpdateLastLogin(ctx, user.ID); err != nil {
		// Non-critical error, just log it
		s.logger.Warn().
			Int64("user_id", user.ID).
			Err(err).
			Msg("Failed to update last login")
	}

	// Convert to response object
	userResponse := user.ToResponse()

	s.logger.Info().
		Int64("user_id", user.ID).
		Str("email", user.Email).
		Msg("User logged in successfully")

	return tokens, &userResponse, nil
}

// SetAuthCookies sets the authentication cookies on the HTTP response
func (s *authServiceImpl) SetAuthCookies(w http.ResponseWriter, tokens *TokenDetails) {
	// Set access token cookie
	http.SetCookie(w, &http.Cookie{
		Name:     "access_token",
		Value:    tokens.AccessToken,
		Domain:   s.cookieConfig.Domain,
		Path:     s.cookieConfig.Path,
		MaxAge:   int(s.accessLifetime.Seconds()),
		Secure:   s.cookieConfig.Secure,
		HttpOnly: s.cookieConfig.HTTPOnly,
		SameSite: s.cookieConfig.SameSite,
	})

	// Set refresh token cookie
	http.SetCookie(w, &http.Cookie{
		Name:     "refresh_token",
		Value:    tokens.RefreshToken,
		Domain:   s.cookieConfig.Domain,
		Path:     s.cookieConfig.Path,
		MaxAge:   int(s.refreshLifetime.Seconds()),
		Secure:   s.cookieConfig.Secure,
		HttpOnly: s.cookieConfig.HTTPOnly,
		SameSite: s.cookieConfig.SameSite,
	})

	s.logger.Debug().Msg("Auth cookies set successfully")
}

// SetCsrfCookie sets the CSRF token cookie (non HTTP-only so JS can read it)
func (s *authServiceImpl) SetCsrfCookie(w http.ResponseWriter, csrfToken string) {
	http.SetCookie(w, &http.Cookie{
		Name:     "csrf_token",
		Value:    csrfToken,
		Domain:   s.cookieConfig.Domain,
		Path:     s.cookieConfig.Path,
		MaxAge:   int(s.accessLifetime.Seconds()),
		Secure:   s.cookieConfig.Secure,
		HttpOnly: false, // Important: NOT HttpOnly so JS can read it
		SameSite: s.cookieConfig.SameSite,
	})

	s.logger.Debug().Msg("CSRF cookie set successfully")
}

// ClearAuthCookies clears the authentication cookies
func (s *authServiceImpl) ClearAuthCookies(w http.ResponseWriter) {
	// Clear access token cookie
	http.SetCookie(w, &http.Cookie{
		Name:     "access_token",
		Value:    "",
		Domain:   s.cookieConfig.Domain,
		Path:     s.cookieConfig.Path,
		MaxAge:   -1,
		Secure:   s.cookieConfig.Secure,
		HttpOnly: s.cookieConfig.HTTPOnly,
		SameSite: s.cookieConfig.SameSite,
	})

	// Clear refresh token cookie
	http.SetCookie(w, &http.Cookie{
		Name:     "refresh_token",
		Value:    "",
		Domain:   s.cookieConfig.Domain,
		Path:     s.cookieConfig.Path,
		MaxAge:   -1,
		Secure:   s.cookieConfig.Secure,
		HttpOnly: s.cookieConfig.HTTPOnly,
		SameSite: s.cookieConfig.SameSite,
	})

	// Clear CSRF token cookie
	http.SetCookie(w, &http.Cookie{
		Name:     "csrf_token",
		Value:    "",
		Domain:   s.cookieConfig.Domain,
		Path:     s.cookieConfig.Path,
		MaxAge:   -1,
		Secure:   s.cookieConfig.Secure,
		HttpOnly: false,
		SameSite: s.cookieConfig.SameSite,
	})

	s.logger.Debug().Msg("Auth cookies cleared successfully")
}

// GetTokenFromCookies retrieves tokens from cookies
func (s *authServiceImpl) GetTokenFromCookies(r *http.Request) (accessToken, refreshToken string) {
	accessCookie, err := r.Cookie("access_token")
	if err == nil {
		accessToken = accessCookie.Value
	}

	refreshCookie, err := r.Cookie("refresh_token")
	if err == nil {
		refreshToken = refreshCookie.Value
	}

	return
}

// GetCsrfToken retrieves CSRF token from cookie
func (s *authServiceImpl) GetCsrfToken(r *http.Request) string {
	csrfCookie, err := r.Cookie("csrf_token")
	if err == nil {
		return csrfCookie.Value
	}
	return ""
}

// VerifyCsrfToken verifies that the provided CSRF token matches the one in the cookie
func (s *authServiceImpl) VerifyCsrfToken(r *http.Request, providedToken string) bool {
	cookieToken := s.GetCsrfToken(r)
	return cookieToken != "" && providedToken != "" && cookieToken == providedToken
}

// Register creates a new user account
func (s *authServiceImpl) Register(ctx context.Context, req *models.CreateUserRequest) (*models.UserResponse, error) {
	// Check if email already exists
	existingByEmail, err := s.userRepo.FindByEmail(ctx, req.Email)
	if err == nil && existingByEmail != nil {
		return nil, fmt.Errorf("email already in use")
	}

	// Check if username already exists
	existingByUsername, err := s.userRepo.FindByUsername(ctx, req.Username)
	if err == nil && existingByUsername != nil {
		return nil, fmt.Errorf("username already in use")
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Create new user
	user := &models.User{
		Email:     req.Email,
		Username:  req.Username,
		Password:  string(hashedPassword),
		Role:      req.Role,
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Active:    true, // New users are active by default
	}

	// Save user to database
	if err := s.userRepo.Create(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	// Convert to response object
	userResponse := user.ToResponse()

	return &userResponse, nil
}

// Logout invalidates the user's session and revokes refresh tokens
func (s *authServiceImpl) Logout(ctx context.Context, w http.ResponseWriter, refreshToken string) error {
	// If refresh token is provided, parse it to get the UUID
	if refreshToken != "" {
		claims := &RefreshTokenClaims{}
		token, err := jwt.ParseWithClaims(refreshToken, claims, func(token *jwt.Token) (interface{}, error) {
			return []byte(s.refreshSecret), nil
		})

		// Only proceed if token is valid
		if err == nil && token.Valid {
			// Revoke the refresh token
			if err := s.tokenRepo.RevokeToken(ctx, claims.UUID); err != nil {
				s.logger.Warn().
					Err(err).
					Str("uuid", claims.UUID).
					Msg("Failed to revoke refresh token during logout")
			}
		}
	}

	// Clear cookies
	s.ClearAuthCookies(w)

	s.logger.Info().Msg("User logged out successfully")
	return nil
}

// RefreshToken generates new tokens using a refresh token
func (s *authServiceImpl) RefreshToken(ctx context.Context, refreshToken string) (*TokenDetails, error) {
	// Parse refresh token
	claims := &RefreshTokenClaims{}
	token, err := jwt.ParseWithClaims(refreshToken, claims, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.refreshSecret), nil
	})

	// Handle token parsing errors with better error messages
	if err != nil {
		// Check for token expiration in a more compatible way
		if strings.Contains(err.Error(), "expired") || strings.Contains(err.Error(), "token is expired") {
			s.logger.Warn().
				Err(err).
				Msg("Refresh token expired")
			return nil, fmt.Errorf("refresh token expired")
		}

		s.logger.Warn().
			Err(err).
			Msg("Invalid refresh token")
		return nil, fmt.Errorf("invalid refresh token: %w", err)
	}

	if !token.Valid {
		s.logger.Warn().Msg("Invalid refresh token")
		return nil, fmt.Errorf("invalid refresh token")
	}

	// Validate token in database
	userID, err := s.tokenRepo.ValidateToken(ctx, claims.UUID)
	if err != nil {
		s.logger.Warn().
			Err(err).
			Str("uuid", claims.UUID).
			Msg("Refresh token not found or revoked")
		return nil, fmt.Errorf("refresh token not found or revoked")
	}

	// Get user from database
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		s.logger.Error().
			Err(err).
			Int64("user_id", userID).
			Msg("User not found during token refresh")
		return nil, fmt.Errorf("user not found")
	}

	// Revoke the current refresh token (token rotation for security)
	if err := s.tokenRepo.RevokeToken(ctx, claims.UUID); err != nil {
		s.logger.Warn().
			Err(err).
			Str("uuid", claims.UUID).
			Msg("Failed to revoke old refresh token")
	}

	// Generate new tokens
	tokens, err := s.createTokens(ctx, user)
	if err != nil {
		s.logger.Error().
			Err(err).
			Int64("user_id", user.ID).
			Msg("Failed to generate new tokens during refresh")
		return nil, fmt.Errorf("failed to generate tokens: %w", err)
	}

	s.logger.Info().
		Int64("user_id", user.ID).
		Msg("Tokens refreshed successfully")

	return tokens, nil
}

// ValidateToken validates an access token and returns the claims
func (s *authServiceImpl) ValidateToken(accessToken string) (*AccessTokenClaims, error) {
	claims := &AccessTokenClaims{}
	token, err := jwt.ParseWithClaims(accessToken, claims, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.accessSecret), nil
	})

	// Handle token parsing errors with better error messages
	if err != nil {
		// Check for token expiration in a more compatible way
		if strings.Contains(err.Error(), "expired") || strings.Contains(err.Error(), "token is expired") {
			return nil, fmt.Errorf("token is expired")
		}
		return nil, fmt.Errorf("invalid token: %w", err)
	}

	if !token.Valid {
		return nil, fmt.Errorf("invalid token")
	}

	return claims, nil
}

// ChangePassword changes a user's password
func (s *authServiceImpl) ChangePassword(ctx context.Context, userID int64, currentPassword, newPassword string) error {
	// Get user from database
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("user not found")
	}

	// Verify current password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(currentPassword)); err != nil {
		return fmt.Errorf("current password is incorrect")
	}

	// Hash new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}

	// Update password in database
	if err := s.userRepo.UpdatePassword(ctx, userID, string(hashedPassword)); err != nil {
		return fmt.Errorf("failed to update password: %w", err)
	}

	return nil
}

// GetUserByID retrieves a user by ID
func (s *authServiceImpl) GetUserByID(ctx context.Context, id int64) (*models.UserResponse, error) {
	user, err := s.userRepo.FindByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	userResponse := user.ToResponse()
	return &userResponse, nil
}

// ListUsers retrieves a paginated list of users
func (s *authServiceImpl) ListUsers(ctx context.Context, page, pageSize int) ([]*models.UserResponse, int64, error) {
	users, total, err := s.userRepo.FindAll(ctx, page, pageSize)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list users: %w", err)
	}

	// Convert to response objects
	userResponses := make([]*models.UserResponse, len(users))
	for i, user := range users {
		resp := user.ToResponse()
		userResponses[i] = &resp
	}

	return userResponses, total, nil
}

// UpdateUser updates a user's information
func (s *authServiceImpl) UpdateUser(ctx context.Context, userID int64, req *models.UpdateUserRequest) (*models.UserResponse, error) {
	// Check if user exists
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("user not found")
	}

	// Update fields
	if req.Email != "" && req.Email != user.Email {
		// Check if email is already used by another user
		existingUser, err := s.userRepo.FindByEmail(ctx, req.Email)
		if err == nil && existingUser != nil && existingUser.ID != userID {
			return nil, fmt.Errorf("email already in use")
		}
		user.Email = req.Email
	}

	if req.Username != "" && req.Username != user.Username {
		// Check if username is already used by another user
		existingUser, err := s.userRepo.FindByUsername(ctx, req.Username)
		if err == nil && existingUser != nil && existingUser.ID != userID {
			return nil, fmt.Errorf("username already in use")
		}
		user.Username = req.Username
	}

	if req.FirstName != "" {
		user.FirstName = req.FirstName
	}

	if req.LastName != "" {
		user.LastName = req.LastName
	}

	if req.Role != "" {
		user.Role = models.Role(req.Role)
	}

	if req.Active != nil {
		user.Active = *req.Active
	}

	// Update user in database
	if err := s.userRepo.Update(ctx, user); err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	// Convert to response object
	userResponse := user.ToResponse()
	return &userResponse, nil
}

// DeleteUser deletes a user
func (s *authServiceImpl) DeleteUser(ctx context.Context, userID int64) error {
	// Check if user exists
	_, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return fmt.Errorf("user not found")
	}

	// Revoke all user tokens
	if err := s.tokenRepo.RevokeAllUserTokens(ctx, userID); err != nil {
		s.logger.Warn().
			Err(err).
			Int64("user_id", userID).
			Msg("Failed to revoke all user tokens during user deletion")
	}

	// Delete user from database
	if err := s.userRepo.Delete(ctx, userID); err != nil {
		return fmt.Errorf("failed to delete user: %w", err)
	}

	return nil
}

// createTokens generates access and refresh tokens for a user
func (s *authServiceImpl) createTokens(ctx context.Context, user *models.User) (*TokenDetails, error) {
	td := &TokenDetails{
		AtExpires:   time.Now().Add(s.accessLifetime).Unix(),
		RtExpires:   time.Now().Add(s.refreshLifetime).Unix(),
		AccessUuid:  uuid.NewString(),
		RefreshUuid: uuid.NewString(),
	}

	// Generate CSRF token
	csrfBytes := make([]byte, 32)
	if _, err := rand.Read(csrfBytes); err != nil {
		return nil, fmt.Errorf("failed to generate CSRF token: %w", err)
	}
	td.CsrfToken = base64.StdEncoding.EncodeToString(csrfBytes)

	// Create access token
	atClaims := AccessTokenClaims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     user.Role,
		UUID:     td.AccessUuid,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Unix(td.AtExpires, 0)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
		},
	}

	at := jwt.NewWithClaims(jwt.SigningMethodHS256, atClaims)
	accessToken, err := at.SignedString([]byte(s.accessSecret))
	if err != nil {
		return nil, err
	}
	td.AccessToken = accessToken

	// Create refresh token
	rtClaims := RefreshTokenClaims{
		UserID: user.ID,
		UUID:   td.RefreshUuid,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Unix(td.RtExpires, 0)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
		},
	}

	rt := jwt.NewWithClaims(jwt.SigningMethodHS256, rtClaims)
	refreshToken, err := rt.SignedString([]byte(s.refreshSecret))
	if err != nil {
		return nil, err
	}
	td.RefreshToken = refreshToken

	// Store refresh token in database
	err = s.tokenRepo.StoreToken(
		ctx,
		user.ID,
		td.RefreshUuid,
		time.Unix(td.RtExpires, 0),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to store refresh token: %w", err)
	}

	return td, nil
}

func (s *authServiceImpl) RefreshTokenForUser(ctx context.Context, userID int64) (*TokenDetails, error) {
	// Get the user from database
	user, err := s.userRepo.FindByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("user not found: %w", err)
	}

	// Generate new tokens
	tokens, err := s.createTokens(ctx, user)
	if err != nil {
		s.logger.Error().
			Err(err).
			Int64("user_id", user.ID).
			Msg("Failed to generate new tokens for CSRF fix")
		return nil, fmt.Errorf("failed to generate tokens: %w", err)
	}

	s.logger.Info().
		Int64("user_id", user.ID).
		Msg("Tokens generated for CSRF fix")

	return tokens, nil
}
