package service

import (
	"context"
	"fmt"

	"github.com/rs/zerolog"
	"golang.org/x/crypto/bcrypt"

	"github.com/psynarios/admin_back/internal/auth/models"
	"github.com/psynarios/admin_back/internal/auth/repository"
	"github.com/psynarios/admin_back/internal/common/errors"
)

// UserService defines the interface for user management
type UserService interface {
	GetUsers(ctx context.Context, page, pageSize int) ([]models.UserResponse, int64, error)
	GetUser(ctx context.Context, id int64) (*models.UserResponse, error)
	GetUserByUsername(ctx context.Context, username string) (*models.UserResponse, error)
	CreateUser(ctx context.Context, req *models.CreateUserRequest) (*models.UserResponse, error)
	UpdateUser(ctx context.Context, id int64, req *models.UpdateUserRequest) (*models.UserResponse, error)
	DeleteUser(ctx context.Context, id int64) error
}

// userService implements UserService
type userService struct {
	userRepo repository.UserRepository
	logger   zerolog.Logger
}

// NewUserService creates a new user service
func NewUserService(userRepo repository.UserRepository, logger zerolog.Logger) UserService {
	return &userService{
		userRepo: userRepo,
		logger:   logger.With().Str("component", "user_service").Logger(),
	}
}

// GetUsers returns all users with pagination
func (s *userService) GetUsers(ctx context.Context, page, pageSize int) ([]models.UserResponse, int64, error) {
	s.logger.Debug().Int("page", page).Int("pageSize", pageSize).Msg("Getting users with pagination")

	// Ensure valid pagination parameters
	if page < 1 {
		page = 1
	}
	if pageSize < 1 {
		pageSize = 10 // Default page size
	}

	users, total, err := s.userRepo.FindAll(ctx, page, pageSize)
	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to get users")
		return nil, 0, fmt.Errorf("failed to get users: %w", err)
	}

	// Convert to response objects
	userResponses := make([]models.UserResponse, len(users))
	for i, user := range users {
		userResponses[i] = user.ToResponse()
	}

	return userResponses, total, nil
}

// GetUser returns a user by ID
func (s *userService) GetUser(ctx context.Context, id int64) (*models.UserResponse, error) {
	s.logger.Debug().Int64("id", id).Msg("Getting user by ID")

	user, err := s.userRepo.FindByID(ctx, id)
	if err != nil {
		s.logger.Error().Err(err).Int64("id", id).Msg("Failed to get user")
		return nil, fmt.Errorf("user not found: %w", err)
	}

	userResponse := user.ToResponse()
	return &userResponse, nil
}

// GetUserByUsername returns a user by username
func (s *userService) GetUserByUsername(ctx context.Context, username string) (*models.UserResponse, error) {
	s.logger.Debug().Str("username", username).Msg("Getting user by username")

	user, err := s.userRepo.FindByUsername(ctx, username)
	if err != nil {
		s.logger.Error().Err(err).Str("username", username).Msg("Failed to get user")
		return nil, fmt.Errorf("user not found: %w", err)
	}

	userResponse := user.ToResponse()
	return &userResponse, nil
}

// CreateUser creates a new user
func (s *userService) CreateUser(ctx context.Context, req *models.CreateUserRequest) (*models.UserResponse, error) {
	s.logger.Debug().Str("email", req.Email).Str("username", req.Username).Msg("Creating user")

	// Check if email already exists
	existingByEmail, err := s.userRepo.FindByEmail(ctx, req.Email)
	if err == nil && existingByEmail != nil {
		s.logger.Warn().Str("email", req.Email).Msg("Email already in use")
		return nil, errors.NewConflictError("email already in use", nil)
	}

	// Check if username already exists
	existingByUsername, err := s.userRepo.FindByUsername(ctx, req.Username)
	if err == nil && existingByUsername != nil {
		s.logger.Warn().Str("username", req.Username).Msg("Username already in use")
		return nil, errors.NewConflictError("username already in use", nil)
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		s.logger.Error().Err(err).Msg("Failed to hash password")
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Create new user
	user := &models.User{
		Email:     req.Email,
		Username:  req.Username,
		Password:  string(hashedPassword),
		Role:      req.Role,
		FirstName: req.FirstName,
		LastName:  req.LastName,
		Active:    true, // New users are active by default
	}

	// Save user to database
	if err := s.userRepo.Create(ctx, user); err != nil {
		s.logger.Error().Err(err).Msg("Failed to create user")
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	s.logger.Info().Int64("id", user.ID).Str("email", user.Email).Msg("User created successfully")

	// Convert to response object
	userResponse := user.ToResponse()

	return &userResponse, nil
}

// UpdateUser updates an existing user
func (s *userService) UpdateUser(ctx context.Context, id int64, req *models.UpdateUserRequest) (*models.UserResponse, error) {
	s.logger.Debug().Int64("id", id).Msg("Updating user")

	// Get user from database
	user, err := s.userRepo.FindByID(ctx, id)
	if err != nil {
		s.logger.Error().Err(err).Int64("id", id).Msg("User not found for update")
		return nil, fmt.Errorf("user not found: %w", err)
	}

	// Check if email is being changed and if it's already in use
	if req.Email != "" && req.Email != user.Email {
		existingByEmail, err := s.userRepo.FindByEmail(ctx, req.Email)
		if err == nil && existingByEmail != nil && existingByEmail.ID != id {
			s.logger.Warn().Str("email", req.Email).Msg("Email already in use")
			return nil, errors.NewConflictError("email already in use", nil)
		}
		user.Email = req.Email
	}

	// Check if username is being changed and if it's already in use
	if req.Username != "" && req.Username != user.Username {
		existingByUsername, err := s.userRepo.FindByUsername(ctx, req.Username)
		if err == nil && existingByUsername != nil && existingByUsername.ID != id {
			s.logger.Warn().Str("username", req.Username).Msg("Username already in use")
			return nil, errors.NewConflictError("username already in use", nil)
		}
		user.Username = req.Username
	}

	// Update other fields
	if req.FirstName != "" {
		user.FirstName = req.FirstName
	}

	if req.LastName != "" {
		user.LastName = req.LastName
	}

	if string(req.Role) != "" {
		user.Role = req.Role
	}

	if req.Active != nil {
		user.Active = *req.Active
	}

	// Save updated user to database
	if err := s.userRepo.Update(ctx, user); err != nil {
		s.logger.Error().Err(err).Int64("id", id).Msg("Failed to update user")
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	s.logger.Info().Int64("id", user.ID).Msg("User updated successfully")

	// Convert to response object
	userResponse := user.ToResponse()

	return &userResponse, nil
}

// DeleteUser deletes a user
func (s *userService) DeleteUser(ctx context.Context, id int64) error {
	s.logger.Debug().Int64("id", id).Msg("Deleting user")

	// Check if user exists
	_, err := s.userRepo.FindByID(ctx, id)
	if err != nil {
		s.logger.Error().Err(err).Int64("id", id).Msg("User not found for deletion")
		return fmt.Errorf("user not found: %w", err)
	}

	// Delete user
	if err := s.userRepo.Delete(ctx, id); err != nil {
		s.logger.Error().Err(err).Int64("id", id).Msg("Failed to delete user")
		return fmt.Errorf("failed to delete user: %w", err)
	}

	s.logger.Info().Int64("id", id).Msg("User deleted successfully")

	return nil
}
