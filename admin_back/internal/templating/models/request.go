package models

// TemplateGroupRequest represents the request to create or update a template group
type TemplateGroupRequest struct {
	Name        string   `json:"name" binding:"required"`
	DayNumber   int      `json:"day_number" binding:"required,min=1"`
	CompanyIDs  []string `json:"company_ids" binding:"required"`
	Description string   `json:"description"`
	IsActive    bool     `json:"is_active"`
}

// TemplateRequest represents the request to create or update a template
type TemplateRequest struct {
	Name           string                 `json:"name" binding:"required"`
	Subject        string                 `json:"subject" binding:"required"`
	Body           string                 `json:"body" binding:"required"` // Changed HTMLBody to Body
	GroupID        string                 `json:"group_id" binding:"required"`
	WeekNumber     int                    `json:"week_number"` // Remove the binding tag for testing
	Type           string                 `json:"type" binding:"required"`
	DefaultData    map[string]interface{} `json:"default_data"`
	RequiredFields []string               `json:"required_fields"`
	IsActive       bool                   `json:"is_active"`
}

// RenderedTemplate represents a rendered template
type RenderedTemplate struct {
	Subject string `json:"subject"`
	Body    string `json:"body"` // Changed HTMLBody to Body
}
