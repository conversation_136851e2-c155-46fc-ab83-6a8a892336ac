package models

import (
	"time"
)

// TemplateData is a JSON object containing data for template variables
type TemplateData map[string]interface{}

// Template represents an email template
type Template struct {
	ID             string                 `json:"id" db:"id"`
	GroupID        string                 `json:"group_id" db:"group_id"`
	Name           string                 `json:"name" db:"name"`
	Subject        string                 `json:"subject" db:"subject"`
	Body           string                 `json:"body" db:"body"` // Changed HTMLBody to Body
	WeekNumber     int                    `json:"week_number" db:"week_number"`
	Type           string                 `json:"type" db:"type"`
	DefaultData    map[string]interface{} `json:"default_data" db:"default_data"`
	RequiredFields []string               `json:"required_fields" db:"required_fields"`
	IsActive       bool                   `json:"is_active" db:"is_active"`
	CreatedAt      time.Time              `json:"created_at" db:"created_at"`
	UpdatedAt      time.Time              `json:"updated_at" db:"updated_at"`
}
