package models

import (
	"time"
)

// TemplateGroup represents a group of templates for specific days and companies
type TemplateGroup struct {
	ID          string    `json:"id" db:"id"`
	Name        string    `json:"name" db:"name"`
	DayNumber   int       `json:"day_number" db:"day_number"`
	CompanyIDs  []string  `json:"company_ids"`
	Description string    `json:"description" db:"description"`
	IsActive    bool      `json:"is_active" db:"is_active"`
	CreatedAt   time.Time `json:"created_at" db:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" db:"updated_at"`
}
