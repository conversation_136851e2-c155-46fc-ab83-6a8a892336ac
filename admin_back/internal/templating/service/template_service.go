package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/templating/models"
	"github.com/psynarios/admin_back/internal/templating/repository"
)

// TemplateService defines the interface for template business logic
type TemplateService interface {
	// Template group methods
	CreateTemplateGroup(ctx context.Context, req models.TemplateGroupRequest) (models.TemplateGroup, error)
	GetTemplateGroup(ctx context.Context, id string) (models.TemplateGroup, error)
	GetTemplateGroupByDay(ctx context.Context, dayNumber int, companyID string) (models.TemplateGroup, error)
	ListTemplateGroups(ctx context.Context) ([]models.TemplateGroup, error)
	ListTemplateGroupsByCompany(ctx context.Context, companyID string) ([]models.TemplateGroup, error)
	UpdateTemplateGroup(ctx context.Context, id string, req models.TemplateGroupRequest) (models.TemplateGroup, error)
	DeleteTemplateGroup(ctx context.Context, id string) error

	// Template methods
	CreateTemplate(ctx context.Context, req models.TemplateRequest) (models.Template, error)
	GetTemplate(ctx context.Context, id string) (models.Template, error)
	ListTemplatesByGroup(ctx context.Context, groupID string) ([]models.Template, error)
	UpdateTemplate(ctx context.Context, id string, req models.TemplateRequest) (models.Template, error)
	DeleteTemplate(ctx context.Context, id string) error
	RenderTemplate(ctx context.Context, id string, data map[string]interface{}) (models.RenderedTemplate, error)

	// Selection methods
	SelectTemplate(ctx context.Context, dayNumber int, companyID string, userStartDate time.Time) (models.Template, error)
	SelectTemplateWithScenario(ctx context.Context, dayNumber int, companyID string, scenarioType string, userStartDate time.Time) (models.Template, error)

	// Alternative templates
	GetAlternativeTemplates(ctx context.Context, companyID string) ([]models.Template, error)
}

// templateService implements the TemplateService interface
type templateService struct {
	templateRepo      repository.TemplateRepository
	templateGroupRepo repository.TemplateGroupRepository
	selectorService   SelectorService
	engineService     EngineService
	logger            zerolog.Logger
}

// CompanyService defines the interface for company operations
type CompanyService interface {
	GetCompanyByID(ctx context.Context, id string) (Company, error)
}

// Company represents a company entity
type Company struct {
	ID  string
	URL string
}

// NewTemplateService creates a new template service
func NewTemplateService(
	templateRepo repository.TemplateRepository,
	templateGroupRepo repository.TemplateGroupRepository,
	logger zerolog.Logger,
) TemplateService {
	// Create selector and engine services
	selectorService := NewSelectorService(templateGroupRepo, templateRepo, logger)
	engineService := NewEngineService(logger)

	return &templateService{
		templateRepo:      templateRepo,
		templateGroupRepo: templateGroupRepo,
		selectorService:   selectorService,
		engineService:     engineService,
		logger:            logger.With().Str("component", "template_service").Logger(),
	}
}

// CreateTemplateGroup creates a new template group
func (s *templateService) CreateTemplateGroup(ctx context.Context, req models.TemplateGroupRequest) (models.TemplateGroup, error) {
	s.logger.Debug().
		Str("name", req.Name).
		Int("day_number", req.DayNumber).
		Strs("company_ids", req.CompanyIDs).
		Msg("Creating template group")

	group := models.TemplateGroup{
		Name:        req.Name,
		DayNumber:   req.DayNumber,
		CompanyIDs:  req.CompanyIDs,
		Description: req.Description,
		IsActive:    req.IsActive,
	}

	createdGroup, err := s.templateGroupRepo.Create(ctx, group)
	if err != nil {
		return models.TemplateGroup{}, fmt.Errorf("failed to create template group: %w", err)
	}

	return createdGroup, nil
}

// GetTemplateGroup gets a template group by ID
func (s *templateService) GetTemplateGroup(ctx context.Context, id string) (models.TemplateGroup, error) {
	s.logger.Debug().Str("id", id).Msg("Getting template group")

	group, err := s.templateGroupRepo.GetByID(ctx, id)
	if err != nil {
		return models.TemplateGroup{}, fmt.Errorf("failed to get template group: %w", err)
	}

	return group, nil
}

// GetTemplateGroupByDay gets a template group by day number and company ID
func (s *templateService) GetTemplateGroupByDay(ctx context.Context, dayNumber int, companyID string) (models.TemplateGroup, error) {
	s.logger.Debug().
		Int("day_number", dayNumber).
		Str("company_id", companyID).
		Msg("Getting template group by day and company")

	group, err := s.templateGroupRepo.GetByDayAndCompany(ctx, dayNumber, companyID)
	if err != nil {
		return models.TemplateGroup{}, fmt.Errorf("failed to get template group: %w", err)
	}

	return group, nil
}

// ListTemplateGroups lists all template groups
func (s *templateService) ListTemplateGroups(ctx context.Context) ([]models.TemplateGroup, error) {
	s.logger.Debug().Msg("Listing all template groups")

	groups, err := s.templateGroupRepo.ListAll(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to list template groups: %w", err)
	}

	return groups, nil
}

// ListTemplateGroupsByCompany lists all template groups for a company
func (s *templateService) ListTemplateGroupsByCompany(ctx context.Context, companyID string) ([]models.TemplateGroup, error) {
	s.logger.Debug().Str("company_id", companyID).Msg("Listing template groups by company")

	groups, err := s.templateGroupRepo.ListByCompany(ctx, companyID)
	if err != nil {
		return nil, fmt.Errorf("failed to list template groups: %w", err)
	}

	return groups, nil
}

// UpdateTemplateGroup updates a template group
func (s *templateService) UpdateTemplateGroup(ctx context.Context, id string, req models.TemplateGroupRequest) (models.TemplateGroup, error) {
	s.logger.Debug().
		Str("id", id).
		Str("name", req.Name).
		Int("day_number", req.DayNumber).
		Strs("company_ids", req.CompanyIDs).
		Msg("Updating template group")

	// Check if the group exists
	_, err := s.templateGroupRepo.GetByID(ctx, id)
	if err != nil {
		return models.TemplateGroup{}, fmt.Errorf("template group not found: %w", err)
	}

	// Update the group
	group := models.TemplateGroup{
		ID:          id,
		Name:        req.Name,
		DayNumber:   req.DayNumber,
		CompanyIDs:  req.CompanyIDs,
		Description: req.Description,
		IsActive:    req.IsActive,
	}

	updatedGroup, err := s.templateGroupRepo.Update(ctx, group)
	if err != nil {
		return models.TemplateGroup{}, fmt.Errorf("failed to update template group: %w", err)
	}

	return updatedGroup, nil
}

// DeleteTemplateGroup deletes a template group
func (s *templateService) DeleteTemplateGroup(ctx context.Context, id string) error {
	s.logger.Debug().Str("id", id).Msg("Deleting template group")

	// Check if the group exists
	_, err := s.templateGroupRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("template group not found: %w", err)
	}

	// Delete the group
	err = s.templateGroupRepo.Delete(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to delete template group: %w", err)
	}

	return nil
}

// CreateTemplate creates a new template
func (s *templateService) CreateTemplate(ctx context.Context, req models.TemplateRequest) (models.Template, error) {
	s.logger.Debug().
		Str("name", req.Name).
		Str("group_id", req.GroupID).
		Int("week_number", req.WeekNumber).
		Msg("Creating template")

	// Check if the group exists
	_, err := s.templateGroupRepo.GetByID(ctx, req.GroupID)
	if err != nil {
		return models.Template{}, fmt.Errorf("template group not found: %w", err)
	}

	// Ensure week_number is valid (0 or positive)
	if req.WeekNumber < 0 {
		req.WeekNumber = 0 // Default to 0 if negative
	}

	// Create the template
	template := models.Template{
		GroupID:    req.GroupID,
		Name:       req.Name,
		Subject:    req.Subject,
		Body:       req.Body,
		WeekNumber: req.WeekNumber,
		Type:       req.Type,
		IsActive:   req.IsActive,
	}

	createdTemplate, err := s.templateRepo.Create(ctx, template)
	if err != nil {
		return models.Template{}, fmt.Errorf("failed to create template: %w", err)
	}

	return createdTemplate, nil
}

// GetTemplate gets a template by ID
func (s *templateService) GetTemplate(ctx context.Context, id string) (models.Template, error) {
	s.logger.Debug().Str("id", id).Msg("Getting template")

	template, err := s.templateRepo.GetByID(ctx, id)
	if err != nil {
		return models.Template{}, fmt.Errorf("failed to get template: %w", err)
	}

	return template, nil
}

// ListTemplatesByGroup lists all templates in a group
func (s *templateService) ListTemplatesByGroup(ctx context.Context, groupID string) ([]models.Template, error) {
	s.logger.Debug().Str("group_id", groupID).Msg("Listing templates by group")

	// Check if the group exists
	_, err := s.templateGroupRepo.GetByID(ctx, groupID)
	if err != nil {
		return nil, fmt.Errorf("template group not found: %w", err)
	}

	templates, err := s.templateRepo.ListByGroup(ctx, groupID)
	if err != nil {
		return nil, fmt.Errorf("failed to list templates: %w", err)
	}

	return templates, nil
}

// UpdateTemplate updates a template
func (s *templateService) UpdateTemplate(ctx context.Context, id string, req models.TemplateRequest) (models.Template, error) {
	s.logger.Debug().
		Str("id", id).
		Str("name", req.Name).
		Int("week_number", req.WeekNumber).
		Msg("Updating template")

	// Ensure week_number is valid (0 or positive)
	if req.WeekNumber < 0 {
		return models.Template{}, errors.New("week_number must be 0 or positive")
	}

	// Get existing template
	_, err := s.templateRepo.GetByID(ctx, id)
	if err != nil {
		return models.Template{}, fmt.Errorf("failed to get existing template: %w", err)
	}

	// Update template fields
	template := models.Template{
		ID:             id,
		GroupID:        req.GroupID,
		Name:           req.Name,
		Subject:        req.Subject,
		Body:           req.Body,
		WeekNumber:     req.WeekNumber,
		Type:           req.Type,
		DefaultData:    req.DefaultData,
		RequiredFields: req.RequiredFields,
		IsActive:       req.IsActive,
	}

	updatedTemplate, err := s.templateRepo.Update(ctx, template)
	if err != nil {
		return models.Template{}, fmt.Errorf("failed to update template: %w", err)
	}

	return updatedTemplate, nil
}

// DeleteTemplate deletes a template
func (s *templateService) DeleteTemplate(ctx context.Context, id string) error {
	s.logger.Debug().Str("id", id).Msg("Deleting template")

	// Check if the template exists
	_, err := s.templateRepo.GetByID(ctx, id)
	if err != nil {
		return fmt.Errorf("template not found: %w", err)
	}

	// Delete the template
	err = s.templateRepo.Delete(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to delete template: %w", err)
	}

	return nil
}

// RenderTemplate renders a template with data
func (s *templateService) RenderTemplate(ctx context.Context, id string, data map[string]interface{}) (models.RenderedTemplate, error) {
	s.logger.Debug().Str("id", id).Msg("Rendering template")

	// Get the template
	template, err := s.templateRepo.GetByID(ctx, id)
	if err != nil {
		return models.RenderedTemplate{}, fmt.Errorf("template not found: %w", err)
	}

	// Render the template
	subject, body, err := s.engineService.RenderTemplate(template, data)
	if err != nil {
		return models.RenderedTemplate{}, fmt.Errorf("failed to render template: %w", err)
	}

	return models.RenderedTemplate{
		Subject: subject,
		Body:    body,
	}, nil
}

// SelectTemplate selects a template based on criteria
func (s *templateService) SelectTemplate(ctx context.Context, dayNumber int, companyID string, userStartDate time.Time) (models.Template, error) {
	s.logger.Debug().
		Int("day_number", dayNumber).
		Str("company_id", companyID).
		Time("user_start_date", userStartDate).
		Msg("Selecting template")

	template, err := s.selectorService.SelectTemplate(ctx, dayNumber, companyID, userStartDate)
	if err != nil {
		return models.Template{}, fmt.Errorf("failed to select template: %w", err)
	}

	return template, nil
}

// SelectTemplateWithScenario selects a template with a specific scenario
func (s *templateService) SelectTemplateWithScenario(ctx context.Context, dayNumber int, companyID string, scenarioType string, userStartDate time.Time) (models.Template, error) {
	s.logger.Debug().
		Int("day_number", dayNumber).
		Str("company_id", companyID).
		Str("scenario_type", scenarioType).
		Time("user_start_date", userStartDate).
		Msg("Selecting template with scenario")

	template, err := s.selectorService.SelectTemplateWithScenario(ctx, dayNumber, companyID, scenarioType, userStartDate)
	if err != nil {
		return models.Template{}, fmt.Errorf("failed to select template: %w", err)
	}

	return template, nil
}

// GetAlternativeTemplates returns alternative templates when no template is found for a specific day
func (s *templateService) GetAlternativeTemplates(ctx context.Context, companyID string) ([]models.Template, error) {
	s.logger.Debug().
		Str("company_id", companyID).
		Msg("Getting alternative templates")

	templates, err := s.selectorService.SelectTemplateAlternatives(ctx, companyID)
	if err != nil {
		return nil, fmt.Errorf("failed to get alternative templates: %w", err)
	}

	return templates, nil
}
