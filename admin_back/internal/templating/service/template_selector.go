package service

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"time"

	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/templating/models"
	"github.com/psynarios/admin_back/internal/templating/repository"
)

// SelectorService handles the selection of appropriate templates
type SelectorService interface {
	// SelectTemplate selects the appropriate template based on criteria
	SelectTemplate(ctx context.Context, dayNumber int, companyID string, userStartDate time.Time) (models.Template, error)

	// SelectTemplateWithScenario selects a template based on a specific scenario
	SelectTemplateWithScenario(ctx context.Context, dayNumber int, companyID string, scenarioType string, userStartDate time.Time) (models.Template, error)

	// SelectFridayTemplate is a specialized function for Friday templates
	SelectFridayTemplate(ctx context.Context, companyID string, userStartDate time.Time) (models.Template, error)

	// SelectTemplateAlternatives provides alternative templates when no template is found for a specific day
	SelectTemplateAlternatives(ctx context.Context, companyID string) ([]models.Template, error)
}

// templateSelectorService implements SelectorService
type templateSelectorService struct {
	templateGroupRepo repository.TemplateGroupRepository
	templateRepo      repository.TemplateRepository
	logger            zerolog.Logger
}

// NewSelectorService creates a new template selector service
func NewSelectorService(
	templateGroupRepo repository.TemplateGroupRepository,
	templateRepo repository.TemplateRepository,
	logger zerolog.Logger,
) SelectorService {
	return &templateSelectorService{
		templateGroupRepo: templateGroupRepo,
		templateRepo:      templateRepo,
		logger:            logger.With().Str("component", "template_selector").Logger(),
	}
}

// SelectTemplate selects the appropriate template based on criteria
func (s *templateSelectorService) SelectTemplate(
	ctx context.Context,
	dayNumber int,
	companyID string,
	userStartDate time.Time,
) (models.Template, error) {
	// Step 1: Find the template group for this day and company
	group, err := s.templateGroupRepo.GetByDayAndCompany(ctx, dayNumber, companyID)
	if err != nil {
		return models.Template{}, fmt.Errorf("no template group found for day %d and company %s: %w", dayNumber, companyID, err)
	}

	// Skip if group is not active
	if !group.IsActive {
		return models.Template{}, errors.New("template group is not active")
	}

	// Step 2: Calculate the week number based on user's start date
	weekNumber := calculateWeekNumber(userStartDate)

	// Step 3: Try to find a template specific to this week
	tmpl, err := s.templateRepo.GetByGroupWeekAndScenario(ctx, group.ID, weekNumber, "default")
	if err == nil {
		// Found a specific template for this week
		return tmpl, nil
	}

	// Step 4: If no specific week template, try to find a default template (week_number = 0)
	tmpl, err = s.templateRepo.GetByGroupWeekAndScenario(ctx, group.ID, 0, "default")
	if err == nil {
		// Found a default template
		return tmpl, nil
	}

	// Step 5: If still no template, get all templates for this group with week_number = 0 and select one randomly
	templates, err := s.templateRepo.ListByGroup(ctx, group.ID)
	if err != nil || len(templates) == 0 {
		// If we reach here, no template was found
		return models.Template{}, errors.New("no suitable template found")
	}

	// Filter templates with week_number = 0
	var defaultTemplates []models.Template
	for _, t := range templates {
		if t.WeekNumber == 0 && t.IsActive {
			defaultTemplates = append(defaultTemplates, t)
		}
	}

	if len(defaultTemplates) == 0 {
		return models.Template{}, errors.New("no default templates found")
	}

	// Select a random template from the default templates
	rand.Seed(time.Now().UnixNano())
	randomIndex := rand.Intn(len(defaultTemplates))
	return defaultTemplates[randomIndex], nil
}

// SelectTemplateWithScenario selects a template based on a specific scenario
func (s *templateSelectorService) SelectTemplateWithScenario(
	ctx context.Context,
	dayNumber int,
	companyID string,
	scenarioType string,
	userStartDate time.Time,
) (models.Template, error) {
	// Step 1: Find the template group for this day and company
	group, err := s.templateGroupRepo.GetByDayAndCompany(ctx, dayNumber, companyID)
	if err != nil {
		return models.Template{}, fmt.Errorf("no template group found for day %d and company %s: %w", dayNumber, companyID, err)
	}

	// Skip if group is not active
	if !group.IsActive {
		return models.Template{}, errors.New("template group is not active")
	}

	// Step 2: Calculate the week number based on user's start date
	weekNumber := calculateWeekNumber(userStartDate)

	// Step 3: Try to find a template for this specific scenario and week
	tmpl, err := s.templateRepo.GetByGroupWeekAndScenario(ctx, group.ID, weekNumber, scenarioType)
	if err == nil {
		// Found a specific template for this scenario and week
		return tmpl, nil
	}

	// Step 4: Try to find a template for this scenario but any week (week_number = 0)
	tmpl, err = s.templateRepo.GetByGroupWeekAndScenario(ctx, group.ID, 0, scenarioType)
	if err == nil {
		// Found a template for this scenario
		return tmpl, nil
	}

	// Step 5: If no scenario-specific template found, try the default scenario for this week
	if scenarioType != "default" {
		tmpl, err = s.templateRepo.GetByGroupWeekAndScenario(ctx, group.ID, weekNumber, "default")
		if err == nil {
			// Found a default template for this week
			return tmpl, nil
		}
	}

	// Step 6: As a last resort, try the default scenario and any week
	if scenarioType != "default" {
		tmpl, err = s.templateRepo.GetByGroupWeekAndScenario(ctx, group.ID, 0, "default")
		if err == nil {
			return tmpl, nil
		}
	}

	// Step 7: If still no template, get all templates for this group with week_number = 0 and select one randomly
	templates, err := s.templateRepo.ListByGroup(ctx, group.ID)
	if err != nil || len(templates) == 0 {
		// If we reach here, no template was found
		return models.Template{}, errors.New("no suitable template found")
	}

	// Filter templates with week_number = 0
	var defaultTemplates []models.Template
	for _, t := range templates {
		if t.WeekNumber == 0 && t.IsActive {
			defaultTemplates = append(defaultTemplates, t)
		}
	}

	if len(defaultTemplates) == 0 {
		return models.Template{}, errors.New("no default templates found")
	}

	// Select a random template from the default templates
	rand.Seed(time.Now().UnixNano())
	randomIndex := rand.Intn(len(defaultTemplates))
	return defaultTemplates[randomIndex], nil
}

// SelectFridayTemplate is a specialized function for Friday templates
func (s *templateSelectorService) SelectFridayTemplate(
	ctx context.Context,
	companyID string,
	userStartDate time.Time,
) (models.Template, error) {
	// For Friday, the day number is standardized as 5
	const fridayDayNumber = 5

	// Calculate the week number since user's start date
	weekNumber := calculateWeekNumber(userStartDate)

	// Find the Friday template group
	group, err := s.templateGroupRepo.GetByDayAndCompany(ctx, fridayDayNumber, companyID)
	if err != nil {
		return models.Template{}, fmt.Errorf("no Friday template group found: %w", err)
	}

	// Skip if group is not active
	if !group.IsActive {
		return models.Template{}, errors.New("Friday template group is not active")
	}

	// Try to find a template specific to this Friday week
	tmpl, err := s.templateRepo.GetByGroupWeekAndScenario(ctx, group.ID, weekNumber, "default")
	if err == nil {
		// Found a specific template for this Friday week
		return tmpl, nil
	}

	// If no specific week template, try to find a default Friday template
	tmpl, err = s.templateRepo.GetByGroupWeekAndScenario(ctx, group.ID, 0, "default")
	if err == nil {
		// Found a default template for Friday
		return tmpl, nil
	}

	// If still no template, get all templates for this group with week_number = 0 and select one randomly
	templates, err := s.templateRepo.ListByGroup(ctx, group.ID)
	if err != nil || len(templates) == 0 {
		// If we reach here, no template was found
		return models.Template{}, fmt.Errorf("no Friday template found for week %d", weekNumber)
	}

	// Filter templates with week_number = 0
	var defaultTemplates []models.Template
	for _, t := range templates {
		if t.WeekNumber == 0 && t.IsActive {
			defaultTemplates = append(defaultTemplates, t)
		}
	}

	if len(defaultTemplates) == 0 {
		return models.Template{}, fmt.Errorf("no default Friday templates found")
	}

	// Select a random template from the default templates
	rand.Seed(time.Now().UnixNano())
	randomIndex := rand.Intn(len(defaultTemplates))
	return defaultTemplates[randomIndex], nil
}

// calculateWeekNumber calculates the number of weeks since the start date
func calculateWeekNumber(startDate time.Time) int {
	now := time.Now()

	// Calculate the difference in days
	diffDays := int(now.Sub(startDate).Hours() / 24)

	// Convert to weeks (integer division)
	return diffDays/7 + 1 // Add 1 so first week is week 1, not week 0
}

// SelectTemplateAlternatives provides alternative templates when no template is found for a specific day
func (s *templateSelectorService) SelectTemplateAlternatives(
	ctx context.Context,
	companyID string,
) ([]models.Template, error) {
	// Get all active template groups for this company
	groups, err := s.templateGroupRepo.ListByCompany(ctx, companyID)
	if err != nil {
		return nil, fmt.Errorf("failed to get template groups: %w", err)
	}

	if len(groups) == 0 {
		return nil, errors.New("no active template groups found for company")
	}

	// Collect templates from all groups
	var allTemplates []models.Template
	for _, group := range groups {
		templates, err := s.templateRepo.ListByGroup(ctx, group.ID)
		if err == nil && len(templates) > 0 {
			allTemplates = append(allTemplates, templates...)
		}
	}

	if len(allTemplates) == 0 {
		return nil, errors.New("no templates found for company")
	}

	return allTemplates, nil
}
