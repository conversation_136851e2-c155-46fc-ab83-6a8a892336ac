package service

import (
	"bytes"
	"fmt"
	htmltemplate "html/template"
	"regexp"
	"strings"
	texttemplate "text/template"
	"time"

	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/templating/models"
)

// EngineService defines the interface for template rendering operations
type EngineService interface {
	// RenderTemplate renders a template with the provided data
	RenderTemplate(template models.Template, data map[string]interface{}) (subject, body string, err error)

	// ValidateTemplateData validates that all required fields are present in the template data
	ValidateTemplateData(template models.Template, data map[string]interface{}) error
}

// templateEngineService implements EngineService
type templateEngineService struct {
	logger zerolog.Logger
}

// NewEngineService creates a new template engine service
func NewEngineService(logger zerolog.Logger) EngineService {
	return &templateEngineService{
		logger: logger.With().Str("component", "template_engine").Logger(),
	}
}

// RenderTemplate renders a template with the provided data
func (s *templateEngineService) RenderTemplate(
	template models.Template,
	data map[string]interface{},
) (string, string, error) {
	// Start timing
	startTime := time.Now()
	defer func() {
		s.logger.Debug().
			Dur("duration", time.Since(startTime)).
			Str("template", template.Name).
			Msg("Template rendering completed")
	}()

	// Validate template data
	if err := s.ValidateTemplateData(template, data); err != nil {
		return "", "", err
	}

	// Merge template data
	templateData := make(map[string]interface{})

	// Start with default data from template
	for k, v := range template.DefaultData {
		templateData[k] = v
	}

	// Override with provided data
	for k, v := range data {
		templateData[k] = v
	}

	// Render the subject and body
	subject, err := s.renderText(template.Subject, templateData)
	if err != nil {
		return "", "", fmt.Errorf("failed to render subject: %w", err)
	}

	body, err := s.renderHTML(template.Body, templateData)
	if err != nil {
		return "", "", fmt.Errorf("failed to render body: %w", err)
	}

	// Process company URL if available
	if companyURL, ok := templateData["company_url"].(string); ok && companyURL != "" {
		s.logger.Debug().Str("companyURL", companyURL).Msg("Replacing href='#' with company URL")

		// Replace href="#" with the company URL
		body = strings.ReplaceAll(body, `href="#"`, fmt.Sprintf(`href="%s"`, companyURL))
		body = strings.ReplaceAll(body, `href='#'`, fmt.Sprintf(`href='%s'`, companyURL))

		// Also handle cases where there might be spaces
		body = strings.ReplaceAll(body, `href= "#"`, fmt.Sprintf(`href= "%s"`, companyURL))
		body = strings.ReplaceAll(body, `href = "#"`, fmt.Sprintf(`href = "%s"`, companyURL))
	}

	return subject, body, nil
}

// ValidateTemplateData validates that all required fields are present in the template data
func (s *templateEngineService) ValidateTemplateData(template models.Template, data map[string]interface{}) error {
	// Combine default data with provided data
	combinedData := make(map[string]interface{})

	// Start with default data
	for k, v := range template.DefaultData {
		combinedData[k] = v
	}

	// Override with provided data
	for k, v := range data {
		combinedData[k] = v
	}

	// Check for required fields
	for _, field := range template.RequiredFields {
		if _, exists := combinedData[field]; !exists {
			return fmt.Errorf("required template field missing: %s", field)
		}
	}

	return nil
}

// renderHTML renders an HTML template with the provided data
// This version preserves image tags and other HTML content
func (s *templateEngineService) renderHTML(tmplStr string, data map[string]interface{}) (string, error) {
	// First, preserve <img> tags, base64 encoded images, and other complex HTML elements
	// by temporarily replacing them with placeholders
	imgTagPattern := regexp.MustCompile(`<img[^>]*>`)
	imgTags := imgTagPattern.FindAllString(tmplStr, -1)

	for i, img := range imgTags {
		placeholder := fmt.Sprintf("IMG_PLACEHOLDER_%d", i)
		tmplStr = strings.Replace(tmplStr, img, placeholder, 1)
	}

	// Do the same for base64 images
	base64Pattern := regexp.MustCompile(`data:image\/[^;]+;base64,[a-zA-Z0-9+/=]+`)
	base64Images := base64Pattern.FindAllString(tmplStr, -1)

	for i, img := range base64Images {
		placeholder := fmt.Sprintf("BASE64_PLACEHOLDER_%d", i)
		tmplStr = strings.Replace(tmplStr, img, placeholder, 1)
	}

	// Now render the template
	tmpl, err := htmltemplate.New("email").Parse(tmplStr)
	if err != nil {
		return "", fmt.Errorf("failed to parse HTML template: %w", err)
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", fmt.Errorf("failed to execute HTML template: %w", err)
	}

	result := buf.String()

	// Restore the images and other complex HTML elements
	for i, img := range imgTags {
		placeholder := fmt.Sprintf("IMG_PLACEHOLDER_%d", i)
		result = strings.Replace(result, placeholder, img, 1)
	}

	for i, img := range base64Images {
		placeholder := fmt.Sprintf("BASE64_PLACEHOLDER_%d", i)
		result = strings.Replace(result, placeholder, img, 1)
	}

	return result, nil
}

// renderText renders a text template with the provided data
func (s *templateEngineService) renderText(tmplStr string, data map[string]interface{}) (string, error) {
	tmpl, err := texttemplate.New("email").Parse(tmplStr)
	if err != nil {
		return "", fmt.Errorf("failed to parse text template: %w", err)
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, data); err != nil {
		return "", fmt.Errorf("failed to execute text template: %w", err)
	}

	return buf.String(), nil
}
