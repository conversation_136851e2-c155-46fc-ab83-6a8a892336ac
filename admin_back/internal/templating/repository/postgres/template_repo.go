package postgres

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/templating/models"
	"github.com/psynarios/admin_back/internal/templating/repository"
)

// templateRepository implements the TemplateRepository interface
type templateRepository struct {
	db     *pgxpool.Pool
	logger zerolog.Logger
}

// NewTemplateRepository creates a new template repository
func NewTemplateRepository(db *pgxpool.Pool, logger zerolog.Logger) repository.TemplateRepository {
	return &templateRepository{
		db:     db,
		logger: logger.With().Str("component", "template_repository").Logger(),
	}
}

// Create creates a new template
func (r *templateRepository) Create(ctx context.Context, template models.Template) (models.Template, error) {
	query := `
		INSERT INTO templates (
			group_id, name, subject, body, 
			week_number, type, is_active,
			created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9
		) RETURNING id, created_at, updated_at
	`

	now := time.Now()
	template.CreatedAt = now
	template.UpdatedAt = now

	err := r.db.QueryRow(
		ctx,
		query,
		template.GroupID,
		template.Name,
		template.Subject,
		template.Body,
		template.WeekNumber,
		template.Type,
		template.IsActive,
		template.CreatedAt,
		template.UpdatedAt,
	).Scan(&template.ID, &template.CreatedAt, &template.UpdatedAt)

	if err != nil {
		return models.Template{}, fmt.Errorf("failed to create template: %w", err)
	}

	return template, nil
}

// GetByID gets a template by ID
func (r *templateRepository) GetByID(ctx context.Context, id string) (models.Template, error) {
	query := `
		SELECT id, group_id, name, subject, body,
		       week_number, type, is_active,
		       created_at, updated_at
		FROM templates
		WHERE id = $1
	`

	var template models.Template

	err := r.db.QueryRow(ctx, query, id).Scan(
		&template.ID,
		&template.GroupID,
		&template.Name,
		&template.Subject,
		&template.Body,
		&template.WeekNumber,
		&template.Type,
		&template.IsActive,
		&template.CreatedAt,
		&template.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return models.Template{}, fmt.Errorf("template not found: %s", id)
		}
		return models.Template{}, fmt.Errorf("failed to get template: %w", err)
	}

	return template, nil
}

// ListByGroup lists all templates in a group
func (r *templateRepository) ListByGroup(ctx context.Context, groupID string) ([]models.Template, error) {
	query := `
		SELECT id, group_id, name, subject, body, 
		       week_number, type, is_active,
		       created_at, updated_at
		FROM templates
		WHERE group_id = $1
		ORDER BY week_number, type, name
	`

	rows, err := r.db.Query(ctx, query, groupID)
	if err != nil {
		return nil, fmt.Errorf("failed to list templates: %w", err)
	}
	defer rows.Close()

	templates := []models.Template{}

	for rows.Next() {
		var template models.Template

		err := rows.Scan(
			&template.ID,
			&template.GroupID,
			&template.Name,
			&template.Subject,
			&template.Body,
			&template.WeekNumber,
			&template.Type,
			&template.IsActive,
			&template.CreatedAt,
			&template.UpdatedAt,
		)

		if err != nil {
			return nil, fmt.Errorf("failed to scan template: %w", err)
		}

		templates = append(templates, template)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating rows: %w", err)
	}

	return templates, nil
}

// GetByGroupWeekAndScenario gets a template by group ID, week number, and type
func (r *templateRepository) GetByGroupWeekAndScenario(ctx context.Context, groupID string, weekNumber int, Type string) (models.Template, error) {
	query := `
		SELECT id, group_id, name, subject, body,
		       week_number, type, is_active,
		       created_at, updated_at
		FROM templates
		WHERE group_id = $1 AND week_number = $2 AND type = $3 AND is_active = TRUE
		LIMIT 1
	`

	var template models.Template

	err := r.db.QueryRow(ctx, query, groupID, weekNumber, Type).Scan(
		&template.ID,
		&template.GroupID,
		&template.Name,
		&template.Subject,
		&template.Body,
		&template.WeekNumber,
		&template.Type,
		&template.IsActive,
		&template.CreatedAt,
		&template.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return models.Template{}, fmt.Errorf("no active template found for group %s, week %d, and scenario %s", groupID, weekNumber, Type)
		}
		return models.Template{}, fmt.Errorf("failed to get template: %w", err)
	}

	return template, nil
}

// Update updates a template
func (r *templateRepository) Update(ctx context.Context, template models.Template) (models.Template, error) {
	query := `
		UPDATE templates
		SET
			group_id = $1,
			name = $2,
			subject = $3,
			body = $4,
			week_number = $5,
			type = $6,
			is_active = $7,
			updated_at = $8
		WHERE id = $9
		RETURNING id, group_id, name, subject, body, 
			week_number, type, is_active,
			created_at, updated_at
	`

	template.UpdatedAt = time.Now()

	var updatedTemplate models.Template

	err := r.db.QueryRow(
		ctx,
		query,
		template.GroupID,
		template.Name,
		template.Subject,
		template.Body,
		template.WeekNumber,
		template.Type,
		template.IsActive,
		template.UpdatedAt,
		template.ID,
	).Scan(
		&updatedTemplate.ID,
		&updatedTemplate.GroupID,
		&updatedTemplate.Name,
		&updatedTemplate.Subject,
		&updatedTemplate.Body,
		&updatedTemplate.WeekNumber,
		&updatedTemplate.Type,
		&updatedTemplate.IsActive,
		&updatedTemplate.CreatedAt,
		&updatedTemplate.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return models.Template{}, fmt.Errorf("template not found: %d", template.ID)
		}
		return models.Template{}, fmt.Errorf("failed to update template: %w", err)
	}

	return updatedTemplate, nil
}

// Delete deletes a template
func (r *templateRepository) Delete(ctx context.Context, id string) error {
	query := `DELETE FROM templates WHERE id = $1`

	tag, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete template: %w", err)
	}

	if tag.RowsAffected() == 0 {
		return fmt.Errorf("template not found: %s", id)
	}

	return nil
}
