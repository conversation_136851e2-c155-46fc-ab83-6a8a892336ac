package postgres

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/templating/models"
	"github.com/psynarios/admin_back/internal/templating/repository"
)

// templateGroupRepository implements the TemplateGroupRepository interface
type templateGroupRepository struct {
	db     *pgxpool.Pool
	logger zerolog.Logger
}

// NewTemplateGroupRepository creates a new template group repository
func NewTemplateGroupRepository(db *pgxpool.Pool, logger zerolog.Logger) repository.TemplateGroupRepository {
	return &templateGroupRepository{
		db:     db,
		logger: logger.With().Str("component", "template_group_repository").Logger(),
	}
}

// <PERSON><PERSON> creates a new template group with company associations
func (r *templateGroupRepository) Create(ctx context.Context, group models.TemplateGroup) (models.TemplateGroup, error) {
	tx, err := r.db.Begin(ctx)
	if err != nil {
		return models.TemplateGroup{}, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// Insert into template_groups table
	query := `
		INSERT INTO template_groups (
			name, day_number, description, is_active, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6
		) RETURNING id, created_at, updated_at
	`

	now := time.Now()
	group.CreatedAt = now
	group.UpdatedAt = now

	err = tx.QueryRow(
		ctx,
		query,
		group.Name,
		group.DayNumber,
		group.Description,
		group.IsActive,
		group.CreatedAt,
		group.UpdatedAt,
	).Scan(&group.ID, &group.CreatedAt, &group.UpdatedAt)

	if err != nil {
		return models.TemplateGroup{}, fmt.Errorf("failed to create template group: %w", err)
	}

	// Insert company associations
	for _, companyID := range group.CompanyIDs {
		_, err = tx.Exec(ctx,
			"INSERT INTO template_group_companies (template_group_id, company_id) VALUES ($1, $2)",
			group.ID, companyID)
		if err != nil {
			return models.TemplateGroup{}, fmt.Errorf("failed to associate company %s with group: %w", companyID, err)
		}
	}

	if err = tx.Commit(ctx); err != nil {
		return models.TemplateGroup{}, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return group, nil
}

// GetByID gets a template group by ID with company associations
func (r *templateGroupRepository) GetByID(ctx context.Context, id string) (models.TemplateGroup, error) {
	// Get the template group
	query := `
		SELECT id, name, day_number, description, is_active, created_at, updated_at
		FROM template_groups
		WHERE id = $1
	`

	var group models.TemplateGroup
	err := r.db.QueryRow(ctx, query, id).Scan(
		&group.ID,
		&group.Name,
		&group.DayNumber,
		&group.Description,
		&group.IsActive,
		&group.CreatedAt,
		&group.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return models.TemplateGroup{}, fmt.Errorf("template group not found: %s", id)
		}
		return models.TemplateGroup{}, fmt.Errorf("failed to get template group: %w", err)
	}

	// Get associated companies
	companiesQuery := `
		SELECT company_id FROM template_group_companies
		WHERE template_group_id = $1
	`

	rows, err := r.db.Query(ctx, companiesQuery, id)
	if err != nil {
		return models.TemplateGroup{}, fmt.Errorf("failed to get associated companies: %w", err)
	}
	defer rows.Close()

	var companyIDs []string
	for rows.Next() {
		var companyID string
		if err := rows.Scan(&companyID); err != nil {
			return models.TemplateGroup{}, fmt.Errorf("failed to scan company ID: %w", err)
		}
		companyIDs = append(companyIDs, companyID)
	}

	group.CompanyIDs = companyIDs
	return group, nil
}

// GetByDayAndCompany gets a template group by day number and company ID
func (r *templateGroupRepository) GetByDayAndCompany(ctx context.Context, dayNumber int, companyID string) (models.TemplateGroup, error) {
	query := `
		SELECT tg.id, tg.name, tg.day_number, tg.description, tg.is_active, tg.created_at, tg.updated_at
		FROM template_groups tg
		JOIN template_group_companies tgc ON tg.id = tgc.template_group_id
		WHERE tg.day_number = $1 AND tgc.company_id = $2 AND tg.is_active = TRUE
		LIMIT 1
	`

	var group models.TemplateGroup
	err := r.db.QueryRow(ctx, query, dayNumber, companyID).Scan(
		&group.ID,
		&group.Name,
		&group.DayNumber,
		&group.Description,
		&group.IsActive,
		&group.CreatedAt,
		&group.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return models.TemplateGroup{}, fmt.Errorf("no active template group found for day %d and company %s", dayNumber, companyID)
		}
		return models.TemplateGroup{}, fmt.Errorf("failed to get template group: %w", err)
	}

	// Get associated companies
	companiesQuery := `
		SELECT company_id FROM template_group_companies
		WHERE template_group_id = $1
	`

	rows, err := r.db.Query(ctx, companiesQuery, group.ID)
	if err != nil {
		return models.TemplateGroup{}, fmt.Errorf("failed to get associated companies: %w", err)
	}
	defer rows.Close()

	var companyIDs []string
	for rows.Next() {
		var companyID string
		if err := rows.Scan(&companyID); err != nil {
			return models.TemplateGroup{}, fmt.Errorf("failed to scan company ID: %w", err)
		}
		companyIDs = append(companyIDs, companyID)
	}

	if err := rows.Err(); err != nil {
		return models.TemplateGroup{}, fmt.Errorf("error iterating company IDs: %w", err)
	}

	group.CompanyIDs = companyIDs
	return group, nil
}

// ListAll lists all template groups
func (r *templateGroupRepository) ListAll(ctx context.Context) ([]models.TemplateGroup, error) {
	query := `
		SELECT id, name, day_number, description, is_active, created_at, updated_at
		FROM template_groups
		ORDER BY day_number, name
	`

	rows, err := r.db.Query(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to list template groups: %w", err)
	}
	defer rows.Close()

	var groups []models.TemplateGroup
	for rows.Next() {
		var group models.TemplateGroup
		err := rows.Scan(
			&group.ID,
			&group.Name,
			&group.DayNumber,
			&group.Description,
			&group.IsActive,
			&group.CreatedAt,
			&group.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan template group: %w", err)
		}

		// Get associated companies for this group
		companiesQuery := `
			SELECT company_id FROM template_group_companies
			WHERE template_group_id = $1
		`

		companyRows, err := r.db.Query(ctx, companiesQuery, group.ID)
		if err != nil {
			return nil, fmt.Errorf("failed to get associated companies: %w", err)
		}

		var companyIDs []string
		for companyRows.Next() {
			var companyID string
			if err := companyRows.Scan(&companyID); err != nil {
				companyRows.Close()
				return nil, fmt.Errorf("failed to scan company ID: %w", err)
			}
			companyIDs = append(companyIDs, companyID)
		}
		companyRows.Close()

		if err := companyRows.Err(); err != nil {
			return nil, fmt.Errorf("error iterating company IDs: %w", err)
		}

		group.CompanyIDs = companyIDs
		groups = append(groups, group)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating template groups: %w", err)
	}

	return groups, nil
}

// ListByCompany lists all template groups for a company
func (r *templateGroupRepository) ListByCompany(ctx context.Context, companyID string) ([]models.TemplateGroup, error) {
	query := `
		SELECT tg.id, tg.name, tg.day_number, tg.description, tg.is_active, tg.created_at, tg.updated_at
		FROM template_groups tg
		JOIN template_group_companies tgc ON tg.id = tgc.template_group_id
		WHERE tgc.company_id = $1
		ORDER BY tg.day_number, tg.name
	`

	rows, err := r.db.Query(ctx, query, companyID)
	if err != nil {
		return nil, fmt.Errorf("failed to list template groups: %w", err)
	}
	defer rows.Close()

	var groups []models.TemplateGroup
	for rows.Next() {
		var group models.TemplateGroup
		err := rows.Scan(
			&group.ID,
			&group.Name,
			&group.DayNumber,
			&group.Description,
			&group.IsActive,
			&group.CreatedAt,
			&group.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan template group: %w", err)
		}

		// Get associated companies for this group
		companiesQuery := `
			SELECT company_id FROM template_group_companies
			WHERE template_group_id = $1
		`

		companyRows, err := r.db.Query(ctx, companiesQuery, group.ID)
		if err != nil {
			return nil, fmt.Errorf("failed to get associated companies: %w", err)
		}

		var companyIDs []string
		for companyRows.Next() {
			var companyID string
			if err := companyRows.Scan(&companyID); err != nil {
				companyRows.Close()
				return nil, fmt.Errorf("failed to scan company ID: %w", err)
			}
			companyIDs = append(companyIDs, companyID)
		}
		companyRows.Close()

		if err := companyRows.Err(); err != nil {
			return nil, fmt.Errorf("error iterating company IDs: %w", err)
		}

		group.CompanyIDs = companyIDs
		groups = append(groups, group)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating template groups: %w", err)
	}

	return groups, nil
}

// Update updates a template group and its company associations
func (r *templateGroupRepository) Update(ctx context.Context, group models.TemplateGroup) (models.TemplateGroup, error) {
	tx, err := r.db.Begin(ctx)
	if err != nil {
		return models.TemplateGroup{}, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback(ctx)

	// Update template group
	query := `
		UPDATE template_groups
		SET
			name = $1,
			day_number = $2,
			description = $3,
			is_active = $4,
			updated_at = $5
		WHERE id = $6
		RETURNING id, name, day_number, description, is_active, created_at, updated_at
	`

	group.UpdatedAt = time.Now()

	var updatedGroup models.TemplateGroup
	err = tx.QueryRow(
		ctx,
		query,
		group.Name,
		group.DayNumber,
		group.Description,
		group.IsActive,
		group.UpdatedAt,
		group.ID,
	).Scan(
		&updatedGroup.ID,
		&updatedGroup.Name,
		&updatedGroup.DayNumber,
		&updatedGroup.Description,
		&updatedGroup.IsActive,
		&updatedGroup.CreatedAt,
		&updatedGroup.UpdatedAt,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return models.TemplateGroup{}, fmt.Errorf("template group not found: %s", group.ID)
		}
		return models.TemplateGroup{}, fmt.Errorf("failed to update template group: %w", err)
	}

	// Delete existing company associations
	_, err = tx.Exec(ctx, "DELETE FROM template_group_companies WHERE template_group_id = $1", group.ID)
	if err != nil {
		return models.TemplateGroup{}, fmt.Errorf("failed to remove existing company associations: %w", err)
	}

	// Insert new company associations
	for _, companyID := range group.CompanyIDs {
		_, err = tx.Exec(ctx,
			"INSERT INTO template_group_companies (template_group_id, company_id) VALUES ($1, $2)",
			group.ID, companyID)
		if err != nil {
			return models.TemplateGroup{}, fmt.Errorf("failed to associate company %s with group: %w", companyID, err)
		}
	}

	if err = tx.Commit(ctx); err != nil {
		return models.TemplateGroup{}, fmt.Errorf("failed to commit transaction: %w", err)
	}

	updatedGroup.CompanyIDs = group.CompanyIDs
	return updatedGroup, nil
}

// Delete deletes a template group
func (r *templateGroupRepository) Delete(ctx context.Context, id string) error {
	query := `DELETE FROM template_groups WHERE id = $1`

	tag, err := r.db.Exec(ctx, query, id)
	if err != nil {
		return fmt.Errorf("failed to delete template group: %w", err)
	}

	if tag.RowsAffected() == 0 {
		return fmt.Errorf("template group not found: %s", id)
	}

	return nil
}

// AddCompanyToGroup adds a company to a template group
func (r *templateGroupRepository) AddCompanyToGroup(ctx context.Context, groupID string, companyID string) error {
	query := `
		INSERT INTO template_group_companies (template_group_id, company_id)
		VALUES ($1, $2)
		ON CONFLICT (template_group_id, company_id) DO NOTHING
	`

	_, err := r.db.Exec(ctx, query, groupID, companyID)
	if err != nil {
		return fmt.Errorf("failed to add company %s to group %s: %w", companyID, groupID, err)
	}

	return nil
}

// RemoveCompanyFromGroup removes a company from a template group
func (r *templateGroupRepository) RemoveCompanyFromGroup(ctx context.Context, groupID string, companyID string) error {
	query := `
		DELETE FROM template_group_companies
		WHERE template_group_id = $1 AND company_id = $2
	`

	tag, err := r.db.Exec(ctx, query, groupID, companyID)
	if err != nil {
		return fmt.Errorf("failed to remove company %s from group %s: %w", companyID, groupID, err)
	}

	if tag.RowsAffected() == 0 {
		return fmt.Errorf("company %s not found in group %s", companyID, groupID)
	}

	return nil
}
