package repository

import (
	"context"

	"github.com/psynarios/admin_back/internal/templating/models"
)

// TemplateRepository defines the interface for template operations
type TemplateRepository interface {
	Create(ctx context.Context, template models.Template) (models.Template, error)
	GetByID(ctx context.Context, id string) (models.Template, error)
	ListByGroup(ctx context.Context, groupID string) ([]models.Template, error)
	GetByGroupWeekAndScenario(ctx context.Context, groupID string, weekNumber int, Type string) (models.Template, error)
	Update(ctx context.Context, template models.Template) (models.Template, error)
	Delete(ctx context.Context, id string) error
}
