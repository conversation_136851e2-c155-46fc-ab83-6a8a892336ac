package repository

import (
	"context"

	"github.com/psynarios/admin_back/internal/templating/models"
)

// TemplateGroupRepository defines the interface for template group operations
type TemplateGroupRepository interface {
	Create(ctx context.Context, group models.TemplateGroup) (models.TemplateGroup, error)
	GetByID(ctx context.Context, id string) (models.TemplateGroup, error)
	GetByDayAndCompany(ctx context.Context, dayNumber int, companyID string) (models.TemplateGroup, error)
	ListAll(ctx context.Context) ([]models.TemplateGroup, error)
	ListByCompany(ctx context.Context, companyID string) ([]models.TemplateGroup, error)
	Update(ctx context.Context, group models.TemplateGroup) (models.TemplateGroup, error)
	Delete(ctx context.Context, id string) error
	AddCompanyToGroup(ctx context.Context, groupID string, companyID string) error
	RemoveCompanyFromGroup(ctx context.Context, groupID string, companyID string) error
}
