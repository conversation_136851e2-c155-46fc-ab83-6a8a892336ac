package service

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"database/sql"
	"fmt"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/go-sql-driver/mysql"
	_ "github.com/go-sql-driver/mysql"
	"github.com/rs/zerolog"
	"github.com/xuri/excelize/v2"

	companyModels "github.com/psynarios/admin_back/internal/company/models"
	companyRepo "github.com/psynarios/admin_back/internal/company/repository"
	mailerService "github.com/psynarios/admin_back/internal/mailer/service"
	scraperModels "github.com/psynarios/admin_back/internal/scraper/models"
	templateService "github.com/psynarios/admin_back/internal/templating/service"
)

// ScraperService defines the interface for the scraper service
type ScraperService interface {
	ProcessUserFile(ctx context.Context, filePath string) (scraperModels.ProcessResult, error)
	ProcessUsers(ctx context.Context, users []scraperModels.UserData) (scraperModels.ProcessResult, error)
	ProcessUsersWithDetails(ctx context.Context, users []scraperModels.UserData) (scraperModels.BatchProcessResult, error)
	SelectTemplatesForUsers(ctx context.Context, users []scraperModels.UserData) (scraperModels.BatchTemplateResult, error)
	SendTemplateEmail(ctx context.Context, to, cc, bcc []string, templateID string, data map[string]interface{}, companyID string) error
}

// scraperService implements ScraperService
type scraperService struct {
	companyRepo     companyRepo.CompanyRepository
	mailService     mailerService.MailService
	templateService templateService.TemplateService
	logger          zerolog.Logger
}

// NewScraperService creates a new scraper service
func NewScraperService(
	companyRepo companyRepo.CompanyRepository,
	mailService mailerService.MailService,
	templateService templateService.TemplateService,
	logger zerolog.Logger,
) ScraperService {
	return &scraperService{
		companyRepo:     companyRepo,
		mailService:     mailService,
		templateService: templateService,
		logger:          logger.With().Str("component", "scraper_service").Logger(),
	}
}

// ProcessUsers processes an array of user data
func (s *scraperService) ProcessUsers(ctx context.Context, users []scraperModels.UserData) (scraperModels.ProcessResult, error) {
	result, err := s.processUsersInternal(ctx, users, false)
	if err != nil {
		return scraperModels.ProcessResult{}, err
	}
	return result.Summary, nil
}

// ProcessUsersWithDetails processes an array of user data and returns detailed results
func (s *scraperService) ProcessUsersWithDetails(ctx context.Context, users []scraperModels.UserData) (scraperModels.BatchProcessResult, error) {
	return s.processUsersInternal(ctx, users, true)
}

// processUsersInternal is the internal implementation of processing users
func (s *scraperService) processUsersInternal(ctx context.Context, users []scraperModels.UserData, includeDetails bool) (scraperModels.BatchProcessResult, error) {
	startTime := time.Now()
	result := scraperModels.BatchProcessResult{
		Summary: scraperModels.ProcessResult{
			TotalUsers: len(users),
		},
	}

	if includeDetails {
		result.UserResults = make([]scraperModels.UserDelayInfo, 0, len(users))
	}

	// Log the start of processing
	s.logger.Info().
		Int("totalUsers", len(users)).
		Bool("includeDetails", includeDetails).
		Msg("Starting to process users")

	// Process each user
	for i, user := range users {
		s.logger.Info().
			Int("userIndex", i+1).
			Int("totalUsers", len(users)).
			Str("username", user.Username).
			Str("email", user.Email).
			Str("company", user.CompanyName).
			Msg("Processing user")

		// Find the company in our platform
		company, err := s.companyRepo.GetByName(ctx, user.CompanyName) // Using GetByName method
		if err != nil {
			s.logger.Error().
				Err(err).
				Str("company", user.CompanyName).
				Msg("Failed to find company")
			result.Summary.ErrorCount++
			continue
		}

		// Log company details
		s.logger.Debug().
			Str("companyID", company.ID).
			Str("companyName", company.Name).
			Time("companyStartDate", company.StartedAt).
			Bool("useSSL", company.UseSSL).
			Msg("Company details")

		// Check if user has a delay
		hasDelay, weekNumber, scoreCount, expected, err := s.checkUserDelay(ctx, user.Username, company)
		if err != nil {
			s.logger.Error().
				Err(err).
				Str("username", user.Username).
				Str("company", user.CompanyName).
				Msg("Failed to check user delay")
			result.Summary.ErrorCount++
			continue
		}

		result.Summary.ProcessedUsers++

		// If including details, add user result
		if includeDetails {
			result.UserResults = append(result.UserResults, scraperModels.UserDelayInfo{
				Username:    user.Username,
				Email:       user.Email,
				CompanyName: user.CompanyName,
				HasDelay:    hasDelay,
				WeekNumber:  weekNumber,
				ScoreCount:  scoreCount,
				Expected:    expected,
			})
		}

		// If user has a delay, send an email
		if hasDelay {
			result.Summary.DelayedUsers++

			// Get current day of week (1-7, where 1 is Monday)
			currentDay := int(time.Now().Weekday())
			if currentDay == 0 {
				currentDay = 7 // Sunday is 7
			}

			// Send email based on template
			err = s.sendDelayNotification(ctx, user.Email, user.Username, company.ID, currentDay, weekNumber)
			if err != nil {
				s.logger.Error().
					Err(err).
					Str("username", user.Username).
					Str("email", user.Email).
					Msg("Failed to send delay notification")
				result.Summary.ErrorCount++
				continue
			}

			result.Summary.EmailsSent++
		}
	}

	result.Summary.ProcessingTime = time.Since(startTime).Seconds()

	// Log the final results
	s.logger.Info().
		Int("totalUsers", result.Summary.TotalUsers).
		Int("processedUsers", result.Summary.ProcessedUsers).
		Int("delayedUsers", result.Summary.DelayedUsers).
		Int("emailsSent", result.Summary.EmailsSent).
		Int("errorCount", result.Summary.ErrorCount).
		Float64("processingTime", result.Summary.ProcessingTime).
		Msg("Processing completed")

	// If we have detailed results, log some statistics
	if includeDetails && len(result.UserResults) > 0 {
		delayCount := 0
		totalScoreCount := 0
		totalExpected := 0

		for _, ur := range result.UserResults {
			if ur.HasDelay {
				delayCount++
			}
			totalScoreCount += ur.ScoreCount
			totalExpected += ur.Expected
		}

		// Calculate completion rate, avoiding division by zero
		var completionRate float64
		if totalExpected > 0 {
			completionRate = float64(totalScoreCount) / float64(totalExpected) * 100
		}

		s.logger.Info().
			Int("totalUsers", len(result.UserResults)).
			Int("usersWithDelay", delayCount).
			Int("totalCompletedScenarios", totalScoreCount).
			Int("totalExpectedScenarios", totalExpected).
			Float64("completionRate", completionRate).
			Msg("Detailed processing statistics")
	}

	return result, nil
}

// ProcessUserFile processes an XLSX file containing user data
func (s *scraperService) ProcessUserFile(ctx context.Context, filePath string) (scraperModels.ProcessResult, error) {
	startTime := time.Now()
	result := scraperModels.ProcessResult{}

	// Open the Excel file
	f, err := excelize.OpenFile(filePath)
	if err != nil {
		return result, fmt.Errorf("failed to open file: %w", err)
	}
	defer f.Close()

	// Get the first sheet
	sheetName := f.GetSheetName(0)
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return result, fmt.Errorf("failed to get rows: %w", err)
	}

	// Skip header row
	if len(rows) > 0 {
		rows = rows[1:]
	}

	result.TotalUsers = len(rows)

	// Convert rows to UserData objects
	users := make([]scraperModels.UserData, 0, len(rows))
	for i, row := range rows {
		if len(row) < 3 {
			s.logger.Warn().Int("row", i+2).Msg("Row has less than 3 columns, skipping")
			result.ErrorCount++
			continue
		}

		users = append(users, scraperModels.UserData{
			Username:    row[0],
			Email:       row[1],
			CompanyName: row[2],
		})
	}

	// Process the users
	batchResult, err := s.processUsersInternal(ctx, users, false)
	if err != nil {
		return result, err
	}

	// Combine the results
	result = batchResult.Summary
	result.ProcessingTime = time.Since(startTime).Seconds()

	return result, nil
}

// parseConnectionString parses a MySQL connection string in the format:
// mysql://username:password@host:port/database
func parseConnectionString(connStr string) (username, password, host, port, database string, err error) {
	// Check if the connection string starts with mysql://
	if len(connStr) < 8 || connStr[:8] != "mysql://" {
		return "", "", "", "", "", fmt.Errorf("invalid connection string format, must start with mysql://")
	}

	// Remove the mysql:// prefix
	connStr = connStr[8:]

	// Find the @ symbol to separate credentials from host
	atIndex := -1
	for i, c := range connStr {
		if c == '@' {
			atIndex = i
			break
		}
	}
	if atIndex == -1 {
		return "", "", "", "", "", fmt.Errorf("invalid connection string format, missing @ symbol")
	}

	// Extract credentials
	credentials := connStr[:atIndex]
	// Find the : symbol to separate username from password
	colonIndex := -1
	for i, c := range credentials {
		if c == ':' {
			colonIndex = i
			break
		}
	}
	if colonIndex == -1 {
		return "", "", "", "", "", fmt.Errorf("invalid connection string format, missing : in credentials")
	}
	username = credentials[:colonIndex]
	password = credentials[colonIndex+1:]

	// Extract host, port, and database
	hostPortDB := connStr[atIndex+1:]
	// Find the / symbol to separate host:port from database
	slashIndex := -1
	for i, c := range hostPortDB {
		if c == '/' {
			slashIndex = i
			break
		}
	}
	if slashIndex == -1 {
		return "", "", "", "", "", fmt.Errorf("invalid connection string format, missing / to separate host:port from database")
	}
	hostPort := hostPortDB[:slashIndex]
	database = hostPortDB[slashIndex+1:]

	// Find the : symbol to separate host from port
	colonIndex = -1
	for i, c := range hostPort {
		if c == ':' {
			colonIndex = i
			break
		}
	}
	if colonIndex == -1 {
		return "", "", "", "", "", fmt.Errorf("invalid connection string format, missing : to separate host from port")
	}
	host = hostPort[:colonIndex]
	port = hostPort[colonIndex+1:]

	return username, password, host, port, database, nil
}

// checkUserDelay checks if a user has a delay in their completed scenarios
// It joins the users, user_scenarios, and completed_scenarios tables to find scenarios completed by the user
func (s *scraperService) checkUserDelay(ctx context.Context, username string, company companyModels.Company) (bool, int, int, int, error) {
	// Parse the connection string
	dbUser, dbPass, dbHost, dbPort, dbName, err := parseConnectionString(company.ConnectionString)
	if err != nil {
		return false, 0, 0, 0, fmt.Errorf("failed to parse connection string: %w", err)
	}

	// Prepare DSN for MySQL driver
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s", dbUser, dbPass, dbHost, dbPort, dbName)

	// Start building the DSN parameters
	dsnParams := []string{}

	// Add TLS configuration if SSL is enabled
	if company.UseSSL {
		// Register TLS config for this company
		err = registerTLSConfig(company.ID)
		if err != nil {
			s.logger.Error().
				Err(err).
				Str("companyID", company.ID).
				Msg("Failed to register TLS config")
			return false, 0, 0, 0, fmt.Errorf("failed to register TLS config: %w", err)
		}
		dsnParams = append(dsnParams, "tls="+company.ID)
	}

	// Add timeout parameters
	// Get timeout from environment variable or use default (10 seconds)
	timeoutStr := os.Getenv("DB_TIMEOUT")
	timeout := 10
	if timeoutStr != "" {
		if t, err := strconv.Atoi(timeoutStr); err == nil {
			timeout = t
		}
	}

	dsnParams = append(dsnParams, fmt.Sprintf("timeout=%ds", timeout))
	dsnParams = append(dsnParams, fmt.Sprintf("readTimeout=%ds", timeout))
	dsnParams = append(dsnParams, fmt.Sprintf("writeTimeout=%ds", timeout))

	// Add the parameters to the DSN
	if len(dsnParams) > 0 {
		dsn += "?" + strings.Join(dsnParams, "&")
	}

	// Log certificate information if SSL is enabled
	if company.UseSSL {
		// Log the certificate path being used
		tempDir := os.TempDir()
		certsDir := filepath.Join(tempDir, "psynarios_certs")
		certPath := filepath.Join(certsDir, company.ID+".crt")

		if _, err := os.Stat(certPath); os.IsNotExist(err) {
			s.logger.Warn().
				Str("certPath", certPath).
				Str("companyID", company.ID).
				Msg("Company-specific certificate not found, will try default")

			// Check if default certificate exists
			defaultCertPath := filepath.Join(certsDir, "cacertificate.crt")
			if _, err := os.Stat(defaultCertPath); os.IsNotExist(err) {
				s.logger.Error().
					Str("defaultCertPath", defaultCertPath).
					Msg("Default certificate not found")
			} else {
				s.logger.Info().
					Str("defaultCertPath", defaultCertPath).
					Msg("Using default certificate")
			}
		} else {
			s.logger.Info().
				Str("certPath", certPath).
				Str("companyID", company.ID).
				Msg("Using company-specific certificate")
		}
	} else {
		s.logger.Info().
			Str("companyID", company.ID).
			Msg("SSL is disabled for this company, not using TLS")
	}

	// Log the full DSN (with password masked)
	dsnForLog := fmt.Sprintf("%s:***@tcp(%s:%s)/%s", dbUser, dbHost, dbPort, dbName)
	if len(dsnParams) > 0 {
		dsnForLog += "?" + strings.Join(dsnParams, "&")
	}
	s.logger.Debug().
		Str("dsn", dsnForLog).
		Msg("Database connection string")

	// Set connection timeout
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		s.logger.Error().
			Err(err).
			Str("dsn", dsnForLog).
			Msg("Failed to open database connection")
		return false, 0, 0, 0, fmt.Errorf("failed to connect to database: %w", err)
	}
	defer db.Close()

	// Set connection timeout
	db.SetConnMaxLifetime(time.Second * 10)
	db.SetMaxOpenConns(10)
	db.SetMaxIdleConns(5)

	// Note: Database ping has been removed for security reasons

	s.logger.Info().
		Str("dsn", dsnForLog).
		Msg("Database connection initialized")

	// Log connection details (without sensitive info)
	s.logger.Debug().
		Str("username", username).
		Str("dbHost", dbHost).
		Str("dbPort", dbPort).
		Str("dbName", dbName).
		Bool("useSSL", company.UseSSL).
		Msg("Database connection details")

	// Query to get the count of completed scenarios for a user where duplicated is false
	query := `
		SELECT COUNT(*)
		FROM completed_senarios cs
		JOIN completed_senarios_user_links csul ON cs.id = csul.completed_senario_id
		JOIN up_users u ON u.id = csul.user_id
		WHERE u.username = ? AND cs.duplicated = 0
	`

	// Log the query being executed
	s.logger.Debug().
		Str("query", query).
		Str("username", username).
		Msg("Executing query")

	var scoreCount int
	err = db.QueryRow(query, username).Scan(&scoreCount)
	if err != nil {
		s.logger.Error().
			Err(err).
			Str("query", query).
			Str("username", username).
			Msg("Query execution failed")
		return false, 0, 0, 0, fmt.Errorf("failed to query completed scenarios: %w", err)
	}

	// Log the query result
	s.logger.Info().
		Str("username", username).
		Int("scoreCount", scoreCount).
		Msg("Query result - number of completed scenarios")

	// Calculate expected number of completed scenarios based on company start date
	companyStartDate := company.StartedAt
	currentDate := time.Now()

	// Log company start date and current date
	s.logger.Debug().
		Str("username", username).
		Time("companyStartDate", companyStartDate).
		Time("currentDate", currentDate).
		Msg("Date information for delay calculation")

	// Calculate weeks since company start
	weeksSinceStart := int(currentDate.Sub(companyStartDate).Hours() / (24 * 7))

	// Calculate current week number
	currentWeekNumber := weeksSinceStart + 1

	// Check if user has a delay (fewer completed scenarios than expected)
	hasDelay := scoreCount < weeksSinceStart

	// Log the delay calculation details
	s.logger.Info().
		Str("username", username).
		Int("scoreCount", scoreCount).
		Int("weeksSinceStart", weeksSinceStart).
		Int("currentWeekNumber", currentWeekNumber).
		Bool("hasDelay", hasDelay).
		Dur("timeSinceCompanyStart", currentDate.Sub(companyStartDate)).
		Msg("Delay calculation details")

	return hasDelay, currentWeekNumber, scoreCount, weeksSinceStart, nil
}

// registerTLSConfig registers a TLS config for the given company
func registerTLSConfig(companyID string) error {
	// Use the temp directory for certificates
	tempDir := os.TempDir()
	certsDir := filepath.Join(tempDir, "psynarios_certs")
	certPath := filepath.Join(certsDir, companyID+".crt")

	// Create the directory if it doesn't exist
	if _, err := os.Stat(certsDir); os.IsNotExist(err) {
		fmt.Printf("Creating certificates directory: %s\n", certsDir)
		if err := os.MkdirAll(certsDir, 0777); err != nil {
			return fmt.Errorf("failed to create certificates directory: %w", err)
		}
	}

	// Check if company-specific certificate exists
	if _, err := os.Stat(certPath); os.IsNotExist(err) {
		fmt.Printf("Company certificate not found: %s\n", certPath)
		// If not found, try the default certificate
		defaultCertPath := filepath.Join(certsDir, "cacertificate.crt")
		if _, err := os.Stat(defaultCertPath); os.IsNotExist(err) {
			fmt.Printf("Default certificate not found: %s\n", defaultCertPath)
			return fmt.Errorf("CA certificate file not found for company %s", companyID)
		}
		fmt.Printf("Using default certificate: %s\n", defaultCertPath)
		certPath = defaultCertPath
	} else {
		fmt.Printf("Using company certificate: %s\n", certPath)
	}

	// Read the certificate file
	rootCertPool := x509.NewCertPool()
	pem, err := os.ReadFile(certPath)
	if err != nil {
		return fmt.Errorf("failed to read CA certificate: %w", err)
	}
	fmt.Printf("Certificate file size: %d bytes\n", len(pem))

	if ok := rootCertPool.AppendCertsFromPEM(pem); !ok {
		return fmt.Errorf("failed to append CA certificate to pool")
	}
	fmt.Printf("Successfully added certificate to pool\n")

	// Create TLS configuration
	// For testing purposes, you can set InsecureSkipVerify to true to bypass certificate verification
	// WARNING: This is insecure and should only be used for testing
	insecureSkipVerify := false

	// Check if INSECURE_SSL environment variable is set to true
	if os.Getenv("INSECURE_SSL") == "true" {
		insecureSkipVerify = true
		fmt.Printf("WARNING: InsecureSkipVerify is set to true. This is insecure and should only be used for testing.\n")
	}

	tlsConfig := &tls.Config{
		RootCAs:            rootCertPool,
		InsecureSkipVerify: insecureSkipVerify,
		ServerName:         "", // This will use the host from the connection string
	}

	// Register the TLS config with the MySQL driver using the company ID as the config name
	mysql.RegisterTLSConfig(companyID, tlsConfig)
	fmt.Printf("Registered TLS config for company: %s\n", companyID)

	return nil
}

// sendDelayNotification sends a delay notification email to the user
func (s *scraperService) sendDelayNotification(
	ctx context.Context,
	email string,
	username string,
	companyID string,
	dayNumber int,
	weekNumber int,
) error {
	// Try to get the template for the current day
	template, err := s.templateService.SelectTemplate(ctx, dayNumber, companyID, time.Now().AddDate(0, 0, -weekNumber*7))
	if err != nil {
		s.logger.Warn().
			Err(err).
			Str("username", username).
			Int("day", dayNumber).
			Int("week", weekNumber).
			Msg("Failed to select template for current day, will try alternatives")

		// No template found for this day, but we'll continue with alternatives in the frontend
		// We'll return a specific error that the frontend can handle
		return fmt.Errorf("no_template_for_day:%w", err)
	}

	// Prepare template data
	templateData := map[string]interface{}{
		"username":     username,
		"week_number":  weekNumber,
		"current_date": time.Now().Format("2006-01-02"),
		"subject":      template.Subject,
		"body":         template.Body,
	}

	// Send the email
	err = s.mailService.SendTemplateEmail(
		ctx,
		[]string{email},
		[]string{},
		[]string{},
		template.ID,
		templateData,
		companyID,
	)

	if err != nil {
		return fmt.Errorf("failed to send email: %w", err)
	}

	return nil
}

// SelectTemplatesForUsers processes users and selects appropriate templates
func (s *scraperService) SelectTemplatesForUsers(ctx context.Context, users []scraperModels.UserData) (scraperModels.BatchTemplateResult, error) {
	startTime := time.Now()
	result := scraperModels.BatchTemplateResult{
		Users: make([]scraperModels.UserTemplateData, 0, len(users)),
	}

	// Log the start of processing
	s.logger.Info().
		Int("totalUsers", len(users)).
		Msg("Starting to select templates for users")

	// Process each user
	for i, user := range users {
		s.logger.Info().
			Int("userIndex", i+1).
			Int("totalUsers", len(users)).
			Str("username", user.Username).
			Str("email", user.Email).
			Str("company", user.CompanyName).
			Msg("Processing user for template selection")

		// Find the company in our platform
		company, err := s.companyRepo.GetByName(ctx, user.CompanyName)
		if err != nil {
			s.logger.Error().
				Err(err).
				Str("company", user.CompanyName).
				Msg("Failed to find company")
			continue
		}

		// Check if user has a delay
		hasDelay, weekNumber, scoreCount, expected, err := s.checkUserDelay(ctx, user.Username, company)
		if err != nil {
			s.logger.Error().
				Err(err).
				Str("username", user.Username).
				Str("company", user.CompanyName).
				Msg("Failed to check user delay")
			continue
		}

		// Get current day of week (1-7, where 1 is Monday)
		currentDay := int(time.Now().Weekday())
		if currentDay == 0 {
			currentDay = 7 // Sunday is 7
		}

		// Try to select template based on day and week number
		template, err := s.templateService.SelectTemplate(ctx, currentDay, company.ID, time.Now().AddDate(0, 0, -weekNumber*7))

		// If no template found for this day, add user to result with empty template
		// This allows the frontend to show alternative templates
		if err != nil {
			s.logger.Warn().
				Err(err).
				Str("username", user.Username).
				Int("day", currentDay).
				Int("week", weekNumber).
				Msg("No template found for current day, frontend will show alternatives")

			// Add user with minimal data to result
			result.Users = append(result.Users, scraperModels.UserTemplateData{
				Username:         user.Username,
				Email:            user.Email,
				CompanyName:      user.CompanyName,
				HasDelay:         hasDelay,
				WeekNumber:       weekNumber,
				ScoreCount:       scoreCount,
				Expected:         expected,
				NeedsAlternative: true, // Flag to indicate alternative template needed
			})
			continue
		}

		// Prepare minimal template data
		result.Users = append(result.Users, scraperModels.UserTemplateData{
			Username:         user.Username,
			Email:            user.Email,
			CompanyName:      user.CompanyName,
			HasDelay:         hasDelay,
			WeekNumber:       weekNumber,
			ScoreCount:       scoreCount,
			Expected:         expected,
			TemplateID:       template.ID,
			TemplateName:     template.Name,
			NeedsAlternative: false,
		})
	}

	s.logger.Info().
		Int("processedUsers", len(result.Users)).
		Dur("duration", time.Since(startTime)).
		Msg("Completed template selection for users")

	return result, nil
}

// SendTemplateEmail sends an email using a template
func (s *scraperService) SendTemplateEmail(ctx context.Context, to, cc, bcc []string, templateID string, data map[string]interface{}, companyID string) error {
	// Log the email sending request
	s.logger.Info().
		Str("templateID", templateID).
		Str("companyID", companyID).
		Strs("to", to).
		Msg("Sending template email")

	// Validate inputs
	if len(to) == 0 {
		return fmt.Errorf("at least one recipient is required")
	}

	if templateID == "" {
		return fmt.Errorf("template ID is required")
	}

	if data == nil {
		return fmt.Errorf("template data cannot be nil")
	}

	// First, render the template using the template service
	rendered, err := s.templateService.RenderTemplate(ctx, templateID, data)
	if err != nil {
		s.logger.Error().
			Err(err).
			Str("templateID", templateID).
			Msg("Failed to render template")
		return fmt.Errorf("failed to render template: %w", err)
	}

	// Now send the email with the rendered content
	err = s.mailService.SendEmail(ctx, to, cc, bcc, rendered.Subject, rendered.Body, companyID)
	if err != nil {
		s.logger.Error().
			Err(err).
			Str("templateID", templateID).
			Str("companyID", companyID).
			Strs("to", to).
			Msg("Failed to send template email")
		return fmt.Errorf("failed to send template email: %w", err)
	}

	s.logger.Info().
		Str("templateID", templateID).
		Str("companyID", companyID).
		Strs("to", to).
		Msg("Template email sent successfully")

	return nil
}
