package models

// UserData represents a single user's data from the frontend
type UserData struct {
	Username    string `json:"username"`
	Email       string `json:"email"`
	CompanyName string `json:"company_name"`
}

// ProcessUsersRequest represents the request to process users
type ProcessUsersRequest struct {
	Users []UserData `json:"users" binding:"required,dive"`
}

// ProcessResult contains statistics about the processing
type ProcessResult struct {
	TotalUsers     int     `json:"total_users"`
	ProcessedUsers int     `json:"processed_users"`
	DelayedUsers   int     `json:"delayed_users"`
	EmailsSent     int     `json:"emails_sent"`
	ErrorCount     int     `json:"error_count"`
	ProcessingTime float64 `json:"processing_time_seconds"`
}

// UserDelayInfo represents information about a user's delay in completing scenarios
type UserDelayInfo struct {
	Username    string `json:"username"`
	Email       string `json:"email"`
	CompanyName string `json:"company_name"`
	HasDelay    bool   `json:"has_delay"`
	WeekNumber  int    `json:"week_number"`
	ScoreCount  int    `json:"score_count"`
	Expected    int    `json:"expected_count"`
}

// BatchProcessResult contains detailed information about the processing
type BatchProcessResult struct {
	Summary     ProcessResult   `json:"summary"`
	UserResults []UserDelayInfo `json:"user_results,omitempty"`
}

// UserTemplateData represents a user with their selected template
type UserTemplateData struct {
	Username         string                 `json:"username"`
	Email            string                 `json:"email"`
	CompanyName      string                 `json:"company_name"`
	HasDelay         bool                   `json:"has_delay"`
	WeekNumber       int                    `json:"week_number"`
	ScoreCount       int                    `json:"score_count"`
	Expected         int                    `json:"expected"`
	TemplateID       string                 `json:"template_id"`
	TemplateName     string                 `json:"template_name"`
	Subject          string                 `json:"subject"`
	Body             string                 `json:"body"`
	Data             map[string]interface{} `json:"data"`
	NeedsAlternative bool                   `json:"needs_alternative"`
}

// BatchTemplateResult represents the result of template selection for multiple users
type BatchTemplateResult struct {
	Users []UserTemplateData `json:"users"`
}
