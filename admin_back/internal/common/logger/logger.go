package logger

import (
	"io"
	"os"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/pkgerrors"

	"github.com/psynarios/admin_back/internal/common/config"
)

// Initialize sets up the global logger
func Initialize(cfg config.LoggerConfig) zerolog.Logger {
	// Configure zerolog
	zerolog.ErrorStackMarshaler = pkgerrors.MarshalStack
	zerolog.TimeFieldFormat = cfg.TimeFormat

	// Set global log level
	level, err := zerolog.ParseLevel(cfg.Level)
	if err != nil {
		level = zerolog.InfoLevel
	}
	zerolog.SetGlobalLevel(level)

	// Setup output writers
	var writers []io.Writer

	// Console output
	if cfg.Console {
		consoleWriter := zerolog.ConsoleWriter{
			Out:        os.Stdout,
			TimeFormat: cfg.TimeFormat,
		}
		writers = append(writers, consoleWriter)
	}

	// File output if configured
	if cfg.File != "" {
		logFile, err := os.OpenFile(
			cfg.File,
			os.O_APPEND|os.O_CREATE|os.O_WRONLY,
			0644,
		)
		if err == nil {
			writers = append(writers, logFile)
		}
	}

	// Create multi-writer if needed
	var output io.Writer
	if len(writers) > 1 {
		output = io.MultiWriter(writers...)
	} else if len(writers) == 1 {
		output = writers[0]
	} else {
		output = os.Stdout // Default to stdout if no writers
	}

	// Create and configure logger
	logger := zerolog.New(output).
		With().
		Timestamp().
		Caller().
		Logger()

	return logger
}

// NewContextLogger creates a new logger with context
func NewContextLogger(baseLogger zerolog.Logger, component string) zerolog.Logger {
	return baseLogger.With().Str("component", component).Logger()
}

// GetLogLevelFromEnv gets log level from environment with fallback
func GetLogLevelFromEnv(fallback zerolog.Level) zerolog.Level {
	levelStr := os.Getenv("LOG_LEVEL")
	if levelStr == "" {
		return fallback
	}

	level, err := zerolog.ParseLevel(levelStr)
	if err != nil {
		return fallback
	}

	return level
}
