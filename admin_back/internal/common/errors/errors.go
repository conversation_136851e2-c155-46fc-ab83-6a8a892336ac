package errors

import (
	"errors"
	"net/http"
)

// Common error types
var (
	ErrNotFound          = errors.New("resource not found")
	ErrBadRequest        = errors.New("bad request")
	ErrUnauthorized      = errors.New("unauthorized")
	ErrForbidden         = errors.New("forbidden")
	ErrInternalServer    = errors.New("internal server error")
	ErrConflict          = errors.New("conflict")
	ErrValidation        = errors.New("validation error")
	ErrDatabaseOperation = errors.New("database operation failed")
)

// AppError represents an application-specific error
type AppError struct {
	Err            error
	Message        string
	StatusCode     int
	OriginalError  error
	ValidationErrs map[string]string
}

// Error returns the error message
func (e *AppError) Error() string {
	if e.Message != "" {
		return e.Message
	}
	return e.Err.Error()
}

// Unwrap returns the original error
func (e *AppError) Unwrap() error {
	if e.OriginalError != nil {
		return e.OriginalError
	}
	return e.Err
}

// NewNotFoundError creates a new not found error
func NewNotFoundError(message string, originalErr error) *AppError {
	return &AppError{
		Err:           ErrNotFound,
		Message:       message,
		StatusCode:    http.StatusNotFound,
		OriginalError: originalErr,
	}
}

// NewBadRequestError creates a new bad request error
func NewBadRequestError(message string, originalErr error) *AppError {
	return &AppError{
		Err:           ErrBadRequest,
		Message:       message,
		StatusCode:    http.StatusBadRequest,
		OriginalError: originalErr,
	}
}

// NewValidationError creates a new validation error
func NewValidationError(message string, validationErrs map[string]string) *AppError {
	return &AppError{
		Err:            ErrValidation,
		Message:        message,
		StatusCode:     http.StatusBadRequest,
		ValidationErrs: validationErrs,
	}
}

// NewUnauthorizedError creates a new unauthorized error
func NewUnauthorizedError(message string) *AppError {
	return &AppError{
		Err:        ErrUnauthorized,
		Message:    message,
		StatusCode: http.StatusUnauthorized,
	}
}

// NewForbiddenError creates a new forbidden error
func NewForbiddenError(message string) *AppError {
	return &AppError{
		Err:        ErrForbidden,
		Message:    message,
		StatusCode: http.StatusForbidden,
	}
}

// NewInternalServerError creates a new internal server error
func NewInternalServerError(message string, originalErr error) *AppError {
	return &AppError{
		Err:           ErrInternalServer,
		Message:       message,
		StatusCode:    http.StatusInternalServerError,
		OriginalError: originalErr,
	}
}

// NewConflictError creates a new conflict error
func NewConflictError(message string, originalErr error) *AppError {
	return &AppError{
		Err:           ErrConflict,
		Message:       message,
		StatusCode:    http.StatusConflict,
		OriginalError: originalErr,
	}
}

// NewDatabaseError creates a new database error
func NewDatabaseError(message string, originalErr error) *AppError {
	return &AppError{
		Err:           ErrDatabaseOperation,
		Message:       message,
		StatusCode:    http.StatusInternalServerError,
		OriginalError: originalErr,
	}
}

// IsNotFoundError checks if error is a not found error
func IsNotFoundError(err error) bool {
	var appErr *AppError
	if errors.As(err, &appErr) {
		return errors.Is(appErr.Err, ErrNotFound)
	}
	return false
}

// IsBadRequestError checks if error is a bad request error
func IsBadRequestError(err error) bool {
	var appErr *AppError
	if errors.As(err, &appErr) {
		return errors.Is(appErr.Err, ErrBadRequest)
	}
	return false
}

// IsValidationError checks if error is a validation error
func IsValidationError(err error) bool {
	var appErr *AppError
	if errors.As(err, &appErr) {
		return errors.Is(appErr.Err, ErrValidation)
	}
	return false
}

// IsConflictError checks if error is a conflict error
func IsConflictError(err error) bool {
	var appErr *AppError
	if errors.As(err, &appErr) {
		return errors.Is(appErr.Err, ErrConflict)
	}
	return false
}
