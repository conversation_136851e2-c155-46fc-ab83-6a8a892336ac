package config

import (
	"os"
	"strconv"
	"time"

	"github.com/joho/godotenv"
	"github.com/rs/zerolog"

	"github.com/psynarios/admin_back/internal/common/database"
)

// Config holds the application configuration
type Config struct {
	Environment string
	Server      ServerConfig
	Database    database.Config
	Auth        AuthConfig
	Email       EmailConfig
	Logger      LoggerConfig
}

// ServerConfig holds server configuration
type ServerConfig struct {
	Host         string
	Port         string
	ReadTimeout  time.Duration
	WriteTimeout time.Duration
	IdleTimeout  time.Duration
}

// AuthConfig holds authentication configuration
type AuthConfig struct {
	AccessSecret    string
	RefreshSecret   string
	AccessLifetime  time.Duration
	RefreshLifetime time.Duration
	CookieDomain    string
	CookieSecure    bool
	CookieHTTPOnly  bool
}

// EmailConfig holds email configuration
type EmailConfig struct {
	DefaultSender  string
	DefaultName    string
	SMTPHost       string
	SMTPPort       int
	SMTPUsername   string
	SMTPPassword   string
	UseSSL         bool
	ConnectTimeout time.Duration
}

// LoggerConfig holds logger configuration
type LoggerConfig struct {
	Level      string
	TimeFormat string
	Console    bool
	File       string
}

// LoadConfig loads configuration from environment variables
func LoadConfig(logger zerolog.Logger) (*Config, error) {
	// Load .env file if it exists
	err := godotenv.Load()
	if err != nil && !os.IsNotExist(err) {
		logger.Warn().Err(err).Msg("Error loading .env file")
	}

	cfg := &Config{
		Environment: getEnv("APP_ENV", "development"),
		Server: ServerConfig{
			Host:         getEnv("SERVER_HOST", "0.0.0.0"),
			Port:         getEnv("SERVER_PORT", "8080"),
			ReadTimeout:  getDurationEnv("SERVER_READ_TIMEOUT", 15*time.Second),
			WriteTimeout: getDurationEnv("SERVER_WRITE_TIMEOUT", 15*time.Second),
			IdleTimeout:  getDurationEnv("SERVER_IDLE_TIMEOUT", 60*time.Second),
		},
		Database: database.Config{
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnv("DB_PORT", "5432"),
			User:     getEnv("DB_USER", "postgres"),
			Password: getEnv("DB_PASSWORD", "postgres"),
			DBName:   getEnv("DB_NAME", "admin_back"),
			SSLMode:  getEnv("DB_SSLMODE", "disable"),
		},
		Auth: AuthConfig{
			AccessSecret:    getEnv("AUTH_ACCESS_SECRET", "access_secret"),
			RefreshSecret:   getEnv("AUTH_REFRESH_SECRET", "refresh_secret"),
			AccessLifetime:  getDurationEnv("AUTH_ACCESS_LIFETIME", 15*time.Minute),
			RefreshLifetime: getDurationEnv("AUTH_REFRESH_LIFETIME", 7*24*time.Hour),
			CookieDomain:    getEnv("AUTH_COOKIE_DOMAIN", ""),
			CookieSecure:    getBoolEnv("AUTH_COOKIE_SECURE", true),
			CookieHTTPOnly:  getBoolEnv("AUTH_COOKIE_HTTP_ONLY", true),
		},
		Email: EmailConfig{
			DefaultSender:  getEnv("EMAIL_DEFAULT_SENDER", "<EMAIL>"),
			DefaultName:    getEnv("EMAIL_DEFAULT_NAME", "System"),
			SMTPHost:       getEnv("EMAIL_SMTP_HOST", "smtp.example.com"),
			SMTPPort:       getIntEnv("EMAIL_SMTP_PORT", 587),
			SMTPUsername:   getEnv("EMAIL_SMTP_USERNAME", "user"),
			SMTPPassword:   getEnv("EMAIL_SMTP_PASSWORD", "password"),
			UseSSL:         getBoolEnv("EMAIL_USE_SSL", false),
			ConnectTimeout: getDurationEnv("EMAIL_CONNECT_TIMEOUT", 10*time.Second),
		},
		Logger: LoggerConfig{
			Level:      getEnv("LOG_LEVEL", "info"),
			TimeFormat: getEnv("LOG_TIME_FORMAT", time.RFC3339),
			Console:    getBoolEnv("LOG_CONSOLE", true),
			File:       getEnv("LOG_FILE", ""),
		},
	}

	logger.Info().Str("environment", cfg.Environment).Msg("Configuration loaded")
	return cfg, nil
}

// Helper functions to get environment variables with fallbacks
func getEnv(key, fallback string) string {
	if value, exists := os.LookupEnv(key); exists {
		return value
	}
	return fallback
}

func getIntEnv(key string, fallback int) int {
	if value, exists := os.LookupEnv(key); exists {
		intValue, err := strconv.Atoi(value)
		if err == nil {
			return intValue
		}
	}
	return fallback
}

func getBoolEnv(key string, fallback bool) bool {
	if value, exists := os.LookupEnv(key); exists {
		boolValue, err := strconv.ParseBool(value)
		if err == nil {
			return boolValue
		}
	}
	return fallback
}

func getDurationEnv(key string, fallback time.Duration) time.Duration {
	if value, exists := os.LookupEnv(key); exists {
		duration, err := time.ParseDuration(value)
		if err == nil {
			return duration
		}
	}
	return fallback
}
