package utils

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"errors"
	"io"
	"net/http"
	"os"
	"regexp"
	"strings"
	"time"
)

var encryptionKey []byte

// InitEncryption initializes the encryption key
func InitEncryption() error {
	// Get encryption key from environment variable
	keyStr := os.Getenv("ENCRYPTION_KEY")
	if keyStr == "" {
		// For development, use a default key
		// In production, this should be a proper random key stored securely
		keyStr = "default-encryption-key-32-bytes-lon"
	}

	// Ensure the key is exactly 32 bytes for AES-256
	if len(keyStr) < 32 {
		// Pad the key if it's too short
		keyStr = keyStr + string(make([]byte, 32-len(keyStr)))
	} else if len(keyStr) > 32 {
		// Truncate if it's too long
		keyStr = keyStr[:32]
	}

	encryptionKey = []byte(keyStr)
	return nil
}

// EncryptString encrypts a string using AES-256-GCM
func EncryptString(plaintext string) (string, error) {
	// Create a new cipher block from the key
	block, err := aes.NewCipher(encryptionKey)
	if err != nil {
		return "", err
	}

	// Create a new GCM cipher
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	// Create a nonce (a unique value used only once with a key)
	nonce := make([]byte, gcm.NonceSize())
	if _, err = io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}

	// Encrypt the data
	ciphertext := gcm.Seal(nonce, nonce, []byte(plaintext), nil)

	// Return the encrypted data as a base64 encoded string
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// DecryptString decrypts a string that was encrypted with EncryptString
func DecryptString(encryptedText string) (string, error) {
	// Decode the base64 encoded string
	ciphertext, err := base64.StdEncoding.DecodeString(encryptedText)
	if err != nil {
		return "", err
	}

	// Create a new cipher block from the key
	block, err := aes.NewCipher(encryptionKey)
	if err != nil {
		return "", err
	}

	// Create a new GCM cipher
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	// Check if the ciphertext is valid
	if len(ciphertext) < gcm.NonceSize() {
		return "", errors.New("ciphertext too short")
	}

	// Extract the nonce from the ciphertext
	nonce := ciphertext[:gcm.NonceSize()]
	ciphertext = ciphertext[gcm.NonceSize():]

	// Decrypt the data
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", err
	}

	return string(plaintext), nil
}

// IsEncrypted checks if a string appears to be encrypted
// This is a heuristic and not foolproof, but helps for migration
func IsEncrypted(text string) bool {
	// Encrypted strings should be base64 encoded and have a minimum length
	if len(text) < 24 {
		return false
	}

	// Try to decode the string as base64
	_, err := base64.StdEncoding.DecodeString(text)
	if err != nil {
		return false
	}

	return true
}

// IsBase64 checks if a string is base64 encoded
func IsBase64(s string) bool {
	s = strings.TrimSpace(s)
	// Quick check before trying a decode
	if len(s) == 0 {
		return false
	}

	// Check if the string has base64 characters only
	re := regexp.MustCompile("^[A-Za-z0-9+/]*={0,2}$")
	if !re.MatchString(s) {
		return false
	}

	// Try to decode
	_, err := base64.StdEncoding.DecodeString(s)
	return err == nil
}

// EncodeBase64 encodes a string to base64
func EncodeBase64(s string) string {
	s = strings.TrimSpace(s)
	return base64.StdEncoding.EncodeToString([]byte(s))
}

// DecodeBase64 decodes a base64 string
func DecodeBase64(s string) (string, error) {
	s = strings.TrimSpace(s)
	data, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

// GetClientIP gets a client's IP address from the request
func GetClientIP(r *http.Request) string {
	// Check for X-Forwarded-For header
	xForwardedFor := r.Header.Get("X-Forwarded-For")
	if xForwardedFor != "" {
		// X-Forwarded-For may contain multiple IPs, take the first one
		ips := strings.Split(xForwardedFor, ",")
		if len(ips) > 0 {
			return strings.TrimSpace(ips[0])
		}
	}

	// Check for X-Real-IP header
	xRealIP := r.Header.Get("X-Real-IP")
	if xRealIP != "" {
		return xRealIP
	}

	// Use RemoteAddr as fallback
	ip := r.RemoteAddr
	// RemoteAddr might be in the form IP:port, remove port if present
	if colon := strings.LastIndex(ip, ":"); colon != -1 {
		ip = ip[:colon]
	}
	return ip
}

// FormatTime formats time according to a given layout
func FormatTime(t time.Time, layout string) string {
	if layout == "" {
		layout = time.RFC3339
	}
	return t.Format(layout)
}

// ParseTime parses a time string according to a given layout
func ParseTime(s string, layout string) (time.Time, error) {
	if layout == "" {
		layout = time.RFC3339
	}
	return time.Parse(layout, s)
}

// SanitizeString removes unwanted characters from a string
func SanitizeString(s string) string {
	// Remove control characters and trim spaces
	return strings.TrimSpace(regexp.MustCompile(`[\x00-\x1F]`).ReplaceAllString(s, ""))
}

// Truncate truncates a string to a maximum length
func Truncate(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen-3] + "..."
}
