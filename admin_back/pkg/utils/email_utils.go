package utils

import (
	"fmt"
	"strings"
)

// PrepareEmailHTML ensures the HTML email body is properly formatted for sending
// This function is specifically designed for the standard Psynarios email template structure
func PrepareEmailHTML(html string) string {
	// Trim any whitespace
	html = strings.TrimSpace(html)

	// Remove any newlines, tabs, or carriage returns that might have been added
	// when storing the template in the database
	html = strings.ReplaceAll(html, "\n", "")
	html = strings.ReplaceAll(html, "\r", "")
	html = strings.ReplaceAll(html, "\t", "")

	// Ensure the HTML has proper DOCTYPE and meta tags for email clients
	if !strings.Contains(html, "<!DOCTYPE") && !strings.Contains(html, "<html") {
		html = fmt.Sprintf(`<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Psynarios Email</title>
</head>
<body>
    %s
</body>
</html>`, html)
	}

	return html
}

// ReplaceTemplateVariables replaces template variables in the format {{variable}}
// with their corresponding values from the data map
func ReplaceTemplateVariables(content string, data map[string]interface{}) string {
	result := content

	for key, value := range data {
		// Convert the value to string
		strValue := fmt.Sprintf("%v", value)

		// Replace {{key}} with the value
		placeholder := fmt.Sprintf("{{%s}}", key)
		result = strings.ReplaceAll(result, placeholder, strValue)
	}

	return result
}
