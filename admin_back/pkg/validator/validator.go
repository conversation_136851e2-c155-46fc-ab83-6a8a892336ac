package validator

import (
	"fmt"
	"reflect"
	"strings"

	"github.com/go-playground/validator/v10"
)

// CustomValidator implements a custom validator
type CustomValidator struct {
	validator *validator.Validate
}

// NewCustomValidator creates a new validator
func NewCustomValidator() *CustomValidator {
	v := validator.New()

	// Register validation for struct fields using JSON tags
	v.RegisterTagNameFunc(func(fld reflect.StructField) string {
		name := strings.SplitN(fld.Tag.Get("json"), ",", 2)[0]
		if name == "-" {
			return ""
		}
		return name
	})

	// Register custom validations here
	// Example: v.RegisterValidation("my_custom_rule", myCustomRule)

	return &CustomValidator{validator: v}
}

// Validate validates a struct and returns validation errors
func (cv *CustomValidator) Validate(i interface{}) (map[string]string, error) {
	err := cv.validator.Struct(i)
	if err == nil {
		return nil, nil
	}

	// Convert validation errors to a map
	validationErrors := make(map[string]string)
	for _, err := range err.(validator.ValidationErrors) {
		fieldName := err.Field()
		validationErrors[fieldName] = formatErrorMsg(err)
	}

	return validationErrors, err
}

// formatErrorMsg formats a validation error message
func formatErrorMsg(err validator.FieldError) string {
	switch err.Tag() {
	case "required":
		return "This field is required"
	case "email":
		return "Must be a valid email address"
	case "min":
		if err.Type().Kind() == reflect.String {
			return fmt.Sprintf("Must be at least %s characters long", err.Param())
		}
		return fmt.Sprintf("Must be at least %s", err.Param())
	case "max":
		if err.Type().Kind() == reflect.String {
			return fmt.Sprintf("Must be at most %s characters long", err.Param())
		}
		return fmt.Sprintf("Must be at most %s", err.Param())
	case "oneof":
		return fmt.Sprintf("Must be one of: %s", err.Param())
	default:
		return fmt.Sprintf("Failed validation for '%s'", err.Tag())
	}
}

// ValidateVar validates a single variable
func (cv *CustomValidator) ValidateVar(field interface{}, tag string) error {
	return cv.validator.Var(field, tag)
}
