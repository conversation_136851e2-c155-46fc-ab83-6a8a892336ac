version: '3.8'

services:
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: admin-dashboard-api
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    environment:
      - PORT=8080
      - DEBUG=true
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=postgres
      - DB_PASSWORD=postgrespassword
      - DB_NAME=admin_dashboard
      - DB_SSLMODE=disable
      - JWT_ACCESS_SECRET=${JWT_ACCESS_SECRET:-access_secret_key}
      - JWT_REFRESH_SECRET=${JWT_REFRESH_SECRET:-refresh_secret_key}
      - JWT_ACCESS_EXPIRY=15m
      - JWT_REFRESH_EXPIRY=7d
      - AUTH_COOKIE_DOMAIN=localhost
      - AUTH_COOKIE_SECURE=false
      - AUTH_COOKIE_HTTP_ONLY=true
      - EMAIL_DEFAULT_SENDER=<EMAIL>
      - EMAIL_DEFAULT_NAME=Admin Dashboard
      - EMAIL_SMTP_HOST=mailhog
      - EMAIL_SMTP_PORT=1025
      - EMAIL_SMTP_USERNAME=
      - EMAIL_SMTP_PASSWORD=
      - EMAIL_USE_SSL=false
      - LOG_LEVEL=debug
      - ENCRYPTION_KEY=${ENCRYPTION_KEY:-default-encryption-key-32-bytes-lon}
    volumes:
      - ./certs:/app/certs
    restart: unless-stopped
    networks:
      - admin-network

  postgres:
    image: postgres:16-alpine
    container_name: admin-dashboard-postgres
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgrespassword
      - POSTGRES_DB=admin_dashboard
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - admin-network

  migrations:
    image: migrate/migrate
    container_name: admin-dashboard-migrations
    volumes:
      - ./migrations:/migrations
    command: ["-path", "/migrations", "-database", "**************************************************/admin_dashboard?sslmode=disable", "up"]
    depends_on:
      - postgres
    restart: on-failure
    networks:
      - admin-network

  mailhog:
    image: mailhog/mailhog
    container_name: admin-dashboard-mailhog
    ports:
      - "1025:1025" 
      - "8025:8025" 
    networks:
      - admin-network

networks:
  admin-network:
    driver: bridge

volumes:
  postgres_data:
