# Build stage
FROM golang:1.24-alpine AS builder

WORKDIR /app

# Install dependencies
RUN apk add --no-cache ca-certificates git

# Copy go mod and sum files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o admin-api ./cmd/api

# Final stage
FROM alpine:latest

# Add ca certificates and create non-root user
RUN apk --no-cache add ca-certificates && \
    addgroup -S appgroup && adduser -S appuser -G appgroup

WORKDIR /app

# Copy binary from builder
COPY --from=builder /app/admin-api .

# Copy migrations directory
COPY --from=builder /app/migrations ./migrations

# Set non-root user
USER appuser

# Expose port
EXPOSE 8080

# Command to run the application
CMD ["./admin-api"]