module.exports = ({ env }) => ({
    email: {
      config: {
        provider: 'nodemailer',
        providerOptions: {
          host: env('MAILJET_SMTP_SERVER', 'in-v3.mailjet.com'),
          port: env.int('MAILJET_SMTP_PORT', 587),
          auth: {
            user: env('MAILJET_SMTP_USERNAME'),
            pass: env('MAILJET_SMTP_PASSWORD'),
          },
        },
        settings: {
          defaultFrom: env('MAILJET_SMTP_FROM', '<EMAIL>'),
          defaultReplyTo: env('MAILJET_SMTP_REPLY_TO', '<EMAIL>'),
        },
      },
    },
    'import-export-entries': {
      enabled: true,
      config: {
        // See `Config` section.
      },
    }
  });