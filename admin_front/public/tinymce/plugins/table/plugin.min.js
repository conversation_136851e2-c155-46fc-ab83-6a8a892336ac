/**
 * TinyMCE version 7.8.0 (TBD)
 */
!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager");const t=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&(o=l=e,(n=String).prototype.isPrototypeOf(o)||(null===(r=l.constructor)||void 0===r?void 0:r.name)===n.name)?"string":t;var o,l,n,r})(t)===e,o=e=>t=>typeof t===e,l=t("string"),n=t("array"),r=o("boolean"),a=e=>undefined===e;const s=e=>!(e=>null==e)(e),c=o("function"),i=o("number"),m=()=>{},d=e=>()=>e,u=e=>e,p=(e,t)=>e===t;function b(e,...t){return(...o)=>{const l=t.concat(o);return e.apply(null,l)}}const g=e=>{e()},h=d(!1),f=d(!0);class y{constructor(e,t){this.tag=e,this.value=t}static some(e){return new y(!0,e)}static none(){return y.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?y.some(e(this.value)):y.none()}bind(e){return this.tag?e(this.value):y.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:y.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return s(e)?y.some(e):y.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}y.singletonNone=new y(!1);const w=Object.keys,S=Object.hasOwnProperty,C=(e,t)=>{const o=w(e);for(let l=0,n=o.length;l<n;l++){const n=o[l];t(e[n],n)}},v=(e,t)=>{const o={};var l;return((e,t,o,l)=>{C(e,((e,n)=>{(t(e,n)?o:l)(e,n)}))})(e,t,(l=o,(e,t)=>{l[t]=e}),m),o},T=e=>w(e).length,x=(e,t)=>A(e,t)?y.from(e[t]):y.none(),A=(e,t)=>S.call(e,t),R=(e,t)=>A(e,t)&&void 0!==e[t]&&null!==e[t],O=Array.prototype.indexOf,_=Array.prototype.push,D=(e,t)=>((e,t)=>O.call(e,t))(e,t)>-1,I=(e,t)=>{for(let o=0,l=e.length;o<l;o++)if(t(e[o],o))return!0;return!1},N=(e,t)=>{const o=[];for(let l=0;l<e;l++)o.push(t(l));return o},M=(e,t)=>{const o=e.length,l=new Array(o);for(let n=0;n<o;n++){const o=e[n];l[n]=t(o,n)}return l},P=(e,t)=>{for(let o=0,l=e.length;o<l;o++)t(e[o],o)},k=(e,t)=>{const o=[];for(let l=0,n=e.length;l<n;l++){const n=e[l];t(n,l)&&o.push(n)}return o},E=(e,t,o)=>(P(e,((e,l)=>{o=t(o,e,l)})),o),B=(e,t)=>((e,t,o)=>{for(let l=0,n=e.length;l<n;l++){const n=e[l];if(t(n,l))return y.some(n);if(o(n,l))break}return y.none()})(e,t,h),F=(e,t)=>(e=>{const t=[];for(let o=0,l=e.length;o<l;++o){if(!n(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);_.apply(t,e[o])}return t})(M(e,t)),q=(e,t)=>{for(let o=0,l=e.length;o<l;++o)if(!0!==t(e[o],o))return!1;return!0},L=(e,t)=>t>=0&&t<e.length?y.some(e[t]):y.none(),j=(e,t)=>{for(let o=0;o<e.length;o++){const l=t(e[o],o);if(l.isSome())return l}return y.none()},H=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},V={fromHtml:(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return H(o.childNodes[0])},fromTag:(e,t)=>{const o=(t||document).createElement(e);return H(o)},fromText:(e,t)=>{const o=(t||document).createTextNode(e);return H(o)},fromDom:H,fromPoint:(e,t,o)=>y.from(e.dom.elementFromPoint(t,o)).map(H)},$=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},W=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,z=(e,t)=>e.dom===t.dom,U=$;"undefined"!=typeof window?window:Function("return this;")();const G=e=>e.dom.nodeName.toLowerCase(),K=e=>e.dom.nodeType,J=e=>t=>K(t)===e,Q=J(1),X=J(3),Y=J(9),Z=J(11),ee=e=>t=>Q(t)&&G(t)===e,te=e=>y.from(e.dom.parentNode).map(V.fromDom),oe=e=>y.from(e.dom.nextSibling).map(V.fromDom),le=e=>M(e.dom.childNodes,V.fromDom),ne=e=>V.fromDom(e.dom.host),re=e=>{const t=X(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return(e=>{const t=(e=>V.fromDom(e.dom.getRootNode()))(e);return Z(o=t)&&s(o.dom.host)?y.some(t):y.none();var o})(V.fromDom(t)).fold((()=>o.body.contains(t)),(l=re,n=ne,e=>l(n(e))));var l,n};var ae=(e,t,o,l,n)=>e(o,l)?y.some(o):c(n)&&n(o)?y.none():t(o,l,n);const se=(e,t,o)=>{let l=e.dom;const n=c(o)?o:h;for(;l.parentNode;){l=l.parentNode;const e=V.fromDom(l);if(t(e))return y.some(e);if(n(e))break}return y.none()},ce=(e,t,o)=>se(e,(e=>$(e,t)),o),ie=(e,t)=>(e=>B(e.dom.childNodes,(e=>{return o=V.fromDom(e),$(o,t);var o})).map(V.fromDom))(e),me=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return W(o)?y.none():y.from(o.querySelector(e)).map(V.fromDom)})(t,e),de=(e,t,o)=>ae(((e,t)=>$(e,t)),ce,e,t,o),ue=(e,t=!1)=>{return re(e)?e.dom.isContentEditable:(o=e,de(o,"[contenteditable]")).fold(d(t),(e=>"true"===pe(e)));var o},pe=e=>e.dom.contentEditable,be=e=>t=>z(t,(e=>V.fromDom(e.getBody()))(e)),ge=e=>/^\d+(\.\d+)?$/.test(e)?e+"px":e,he=e=>V.fromDom(e.selection.getStart()),fe=(e,t)=>{let o=[];return P(le(e),(e=>{t(e)&&(o=o.concat([e])),o=o.concat(fe(e,t))})),o},ye=(e,t)=>(e=>k(le(e),(e=>$(e,t))))(e),we=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return W(o)?[]:M(o.querySelectorAll(e),V.fromDom)})(t,e),Se=(e,t,o)=>{if(!(l(o)||r(o)||i(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},Ce=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},ve=(e,t)=>y.from(Ce(e,t)),Te=(e,t)=>{e.dom.removeAttribute(t)},xe=(e,t,o=p)=>e.exists((e=>o(e,t))),Ae=(e,t,o)=>e.isSome()&&t.isSome()?y.some(o(e.getOrDie(),t.getOrDie())):y.none(),Re=(e,t)=>((e,t)=>""===t||e.length>=t.length&&e.substr(0,0+t.length)===t)(e,t),Oe=(_e=/^\s+|\s+$/g,e=>e.replace(_e,""));var _e;const De=e=>e.length>0,Ie=(e,t=10)=>{const o=parseInt(e,t);return isNaN(o)?y.none():y.some(o)},Ne=e=>void 0!==e.style&&c(e.style.getPropertyValue),Me=(e,t)=>{const o=e.dom,l=window.getComputedStyle(o).getPropertyValue(t);return""!==l||re(e)?l:Pe(o,t)},Pe=(e,t)=>Ne(e)?e.style.getPropertyValue(t):"",ke=(e,t)=>{const o=e.dom,l=Pe(o,t);return y.from(l).filter((e=>e.length>0))},Ee=(e,t,o=0)=>ve(e,t).map((e=>parseInt(e,10))).getOr(o),Be=(e,t)=>Fe(e,t,f),Fe=(e,t,o)=>F(le(e),(e=>$(e,t)?o(e)?[e]:[]:Fe(e,t,o))),qe=["tfoot","thead","tbody","colgroup"],Le=(e,t,o)=>({element:e,rowspan:t,colspan:o}),je=(e,t,o)=>({element:e,cells:t,section:o}),He=(e,t)=>de(e,"table",t),Ve=e=>Be(e,"tr"),$e=e=>He(e).fold(d([]),(e=>ye(e,"colgroup"))),We=e=>te(e).map((e=>{const t=G(e);return(e=>D(qe,e))(t)?t:"tbody"})).getOr("tbody"),ze=e=>ve(e,"data-snooker-locked-cols").bind((e=>y.from(e.match(/\d+/g)))).map((e=>((e,t)=>{const o={};for(let l=0,n=e.length;l<n;l++){const n=e[l];o[String(n)]=t(n,l)}return o})(e,f))),Ue=(e,t)=>e+","+t,Ge=e=>{const t={},o=[];var l;const n=(l=e,L(l,0)).map((e=>e.element)).bind(He).bind(ze).getOr({});let r=0,a=0,s=0;const{pass:c,fail:i}=(e=>{const t=[],o=[];for(let n=0,r=e.length;n<r;n++){const r=e[n];(l=r,"colgroup"===l.section?t:o).push(r)}var l;return{pass:t,fail:o}})(e);P(i,(e=>{const l=[];P(e.cells,(e=>{let o=0;for(;void 0!==t[Ue(s,o)];)o++;const r=R(n,o.toString()),c=((e,t,o,l,n,r)=>({element:e,rowspan:t,colspan:o,row:l,column:n,isLocked:r}))(e.element,e.rowspan,e.colspan,s,o,r);for(let l=0;l<e.colspan;l++)for(let n=0;n<e.rowspan;n++){const e=o+l,r=Ue(s+n,e);t[r]=c,a=Math.max(a,e+1)}l.push(c)})),r++,o.push(je(e.element,l,e.section)),s++}));const{columns:m,colgroups:d}=(e=>L(e,e.length-1))(c).map((e=>{const t=(e=>{const t={};let o=0;return P(e.cells,(e=>{const l=e.colspan;N(l,(n=>{const r=o+n;t[r]=((e,t,o)=>({element:e,colspan:t,column:o}))(e.element,l,r)})),o+=l})),t})(e),o=((e,t)=>({element:e,columns:t}))(e.element,((e,t)=>{const o=[];return C(e,((e,l)=>{o.push(t(e,l))})),o})(t,u));return{colgroups:[o],columns:t}})).getOrThunk((()=>({colgroups:[],columns:{}}))),p=((e,t)=>({rows:e,columns:t}))(r,a);return{grid:p,access:t,all:o,columns:m,colgroups:d}},Ke=e=>{const t=(e=>{const t=Ve(e);return((e,t)=>M(e,(e=>{if("colgroup"===G(e)){const t=M((e=>$(e,"colgroup")?ye(e,"col"):F($e(e),(e=>ye(e,"col"))))(e),(e=>{const t=Ee(e,"span",1);return Le(e,1,t)}));return je(e,t,"colgroup")}{const o=M((e=>Be(e,"th,td"))(e),(e=>{const t=Ee(e,"rowspan",1),o=Ee(e,"colspan",1);return Le(e,t,o)}));return je(e,o,t(e))}})))([...$e(e),...t],We)})(e);return Ge(t)},Je=(e,t,o)=>y.from(e.access[Ue(t,o)]),Qe=(e,t,o)=>{const l=((e,t)=>{const o=F(e.all,(e=>e.cells));return k(o,t)})(e,(e=>o(t,e.element)));return l.length>0?y.some(l[0]):y.none()},Xe=(e,t)=>y.from(e.columns[t]);var Ye=tinymce.util.Tools.resolve("tinymce.util.Tools");const Ze=(e,t,o)=>{const l=e.select("td,th",t);let n;for(let t=0;t<l.length;t++){const r=e.getStyle(l[t],o);if(a(n)&&(n=r),n!==r)return""}return n},et=(e,t,o)=>{Ye.each("left center right".split(" "),(l=>{l!==o&&e.formatter.remove("align"+l,{},t)})),o&&e.formatter.apply("align"+o,{},t)},tt=(e,t,o)=>{e.dispatch("TableModified",{...o,table:t})},ot=(e,t,o)=>((e,t)=>(e=>{const t=parseFloat(e);return isNaN(t)?y.none():y.some(t)})(e).getOr(t))(Me(e,t),o),lt=e=>((e,t)=>{const o=e.dom,l=o.getBoundingClientRect().width||o.offsetWidth;return"border-box"===t?l:((e,t,o,l)=>t-ot(e,`padding-${o}`,0)-ot(e,`padding-${l}`,0)-ot(e,`border-${o}-width`,0)-ot(e,`border-${l}-width`,0))(e,l,"left","right")})(e,"content-box");var nt=tinymce.util.Tools.resolve("tinymce.Env");const rt=N(5,(e=>{const t=`${e+1}px`;return{title:t,value:t}})),at=M(["Solid","Dotted","Dashed","Double","Groove","Ridge","Inset","Outset","None","Hidden"],(e=>({title:e,value:e.toLowerCase()}))),st="100%",ct=e=>{var t;const o=e.dom,l=null!==(t=o.getParent(e.selection.getStart(),o.isBlock))&&void 0!==t?t:e.getBody();return lt(V.fromDom(l))+"px"},it=e=>t=>t.options.get(e),mt=it("table_sizing_mode"),dt=it("table_border_widths"),ut=it("table_border_styles"),pt=it("table_cell_advtab"),bt=it("table_row_advtab"),gt=it("table_advtab"),ht=it("table_appearance_options"),ft=it("table_grid"),yt=it("table_style_by_css"),wt=it("table_cell_class_list"),St=it("table_row_class_list"),Ct=it("table_class_list"),vt=it("table_toolbar"),Tt=it("table_background_color_map"),xt=it("table_border_color_map"),At=e=>"fixed"===mt(e),Rt=e=>"responsive"===mt(e),Ot=e=>{const t=e.options,o=t.get("table_default_styles");return t.isSet("table_default_styles")?o:((e,t)=>Rt(e)||!yt(e)?t:At(e)?{...t,width:ct(e)}:{...t,width:st})(e,o)},_t=e=>{const t=e.options,o=t.get("table_default_attributes");return t.isSet("table_default_attributes")?o:((e,t)=>Rt(e)||yt(e)?t:At(e)?{...t,width:ct(e)}:{...t,width:st})(e,o)},Dt=(e,t)=>t.column>=e.startCol&&t.column+t.colspan-1<=e.finishCol&&t.row>=e.startRow&&t.row+t.rowspan-1<=e.finishRow,It=(e,t,o)=>((e,t,o)=>{const l=Qe(e,t,z),n=Qe(e,o,z);return l.bind((e=>n.map((t=>{return o=e,l=t,{startRow:Math.min(o.row,l.row),startCol:Math.min(o.column,l.column),finishRow:Math.max(o.row+o.rowspan-1,l.row+l.rowspan-1),finishCol:Math.max(o.column+o.colspan-1,l.column+l.colspan-1)};var o,l}))))})(e,t,o).bind((t=>((e,t)=>{let o=!0;const l=b(Dt,t);for(let n=t.startRow;n<=t.finishRow;n++)for(let r=t.startCol;r<=t.finishCol;r++)o=o&&Je(e,n,r).exists(l);return o?y.some(t):y.none()})(e,t))),Nt=Ke,Mt=(e,t)=>{te(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},Pt=(e,t)=>{oe(e).fold((()=>{te(e).each((e=>{kt(e,t)}))}),(e=>{Mt(e,t)}))},kt=(e,t)=>{e.dom.appendChild(t.dom)},Et=(e,t)=>{P(t,((o,l)=>{const n=0===l?e:t[l-1];Pt(n,o)}))},Bt=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},Ft=(e=>{const t=t=>e(t)?y.from(t.dom.nodeValue):y.none();return{get:o=>{if(!e(o))throw new Error("Can only get text value of a text node");return t(o).getOr("")},getOption:t,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})(X);var qt=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"];const Lt=(e,t,o,l)=>{const n=t(e,o);return r=(o,l)=>{const n=t(e,l);return jt(e,o,n)},a=n,((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(l,((e,t)=>{a=r(a,e)})),a;var r,a},jt=(e,t,o)=>t.bind((t=>o.filter(b(e.eq,t)))),Ht={up:d({selector:ce,closest:de,predicate:se,all:(e,t)=>{const o=c(t)?t:h;let l=e.dom;const n=[];for(;null!==l.parentNode&&void 0!==l.parentNode;){const e=l.parentNode,t=V.fromDom(e);if(n.push(t),!0===o(t))break;l=e}return n}}),down:d({selector:we,predicate:fe}),styles:d({get:Me,getRaw:ke,set:(e,t,o)=>{((e,t,o)=>{if(!l(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);Ne(e)&&e.style.setProperty(t,o)})(e.dom,t,o)},remove:(e,t)=>{((e,t)=>{Ne(e)&&e.style.removeProperty(t)})(e.dom,t),xe(ve(e,"style").map(Oe),"")&&Te(e,"style")}}),attrs:d({get:Ce,set:(e,t,o)=>{Se(e.dom,t,o)},remove:Te,copyTo:(e,t)=>{((e,t)=>{const o=e.dom;C(t,((e,t)=>{Se(o,t,e)}))})(t,E(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}))}}),insert:d({before:Mt,after:Pt,afterAll:Et,append:kt,appendAll:(e,t)=>{P(t,(t=>{kt(e,t)}))},prepend:(e,t)=>{(e=>(e=>{const t=e.dom.childNodes;return y.from(t[0]).map(V.fromDom)})(e))(e).fold((()=>{kt(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},wrap:(e,t)=>{Mt(e,t),kt(t,e)}}),remove:d({unwrap:e=>{const t=le(e);t.length>0&&Et(e,t),Bt(e)},remove:Bt}),create:d({nu:V.fromTag,clone:e=>V.fromDom(e.dom.cloneNode(!1)),text:V.fromText}),query:d({comparePosition:(e,t)=>e.dom.compareDocumentPosition(t.dom),prevSibling:e=>y.from(e.dom.previousSibling).map(V.fromDom),nextSibling:oe}),property:d({children:le,name:G,parent:te,document:e=>{return(t=e,Y(t)?t:V.fromDom(t.dom.ownerDocument)).dom;var t},isText:X,isComment:e=>8===K(e)||"#comment"===G(e),isElement:Q,isSpecial:e=>{const t=G(e);return D(["script","noscript","iframe","noframes","noembed","title","style","textarea","xmp"],t)},getLanguage:e=>Q(e)?ve(e,"lang"):y.none(),getText:e=>Ft.get(e),setText:(e,t)=>Ft.set(e,t),isBoundary:e=>!!Q(e)&&("body"===G(e)||D(qt,G(e))),isEmptyTag:e=>!!Q(e)&&D(["br","img","hr","input"],G(e)),isNonEditable:e=>Q(e)&&"false"===Ce(e,"contenteditable")}),eq:z,is:U},Vt=e=>ce(e,"table"),$t=(e,t,o)=>me(e,t).bind((t=>me(e,o).bind((e=>{return(o=Vt,l=[t,e],((e,t,o)=>o.length>0?((e,t,o,l)=>l(e,t,o[0],o.slice(1)))(e,t,o,Lt):y.none())(Ht,((e,t)=>o(t)),l)).map((o=>({first:t,last:e,table:o})));var o,l})))),Wt=e=>M(e,V.fromDom),zt="data-mce-selected",Ut="data-mce-first-selected",Gt="data-mce-last-selected",Kt={selected:zt,selectedSelector:"td["+zt+"],th["+zt+"]",firstSelected:Ut,firstSelectedSelector:"td["+Ut+"],th["+Ut+"]",lastSelected:Gt,lastSelectedSelector:"td["+Gt+"],th["+Gt+"]"},Jt=e=>(t,o)=>{const l=G(t),n="col"===l||"colgroup"===l?He(r=t).bind((e=>((e,t)=>((e,t)=>{const o=we(e,t);return o.length>0?y.some(o):y.none()})(e,t))(e,Kt.firstSelectedSelector))).fold(d(r),(e=>e[0])):t;var r;return de(n,e,o)},Qt=Jt("th,td,caption"),Xt=Jt("th,td"),Yt=e=>Wt(e.model.table.getSelectedCells()),Zt=(e,t)=>{const o=Xt(e),l=o.bind((e=>He(e))).map((e=>Ve(e)));return Ae(o,l,((e,o)=>k(o,(o=>I(Wt(o.dom.cells),(o=>"1"===Ce(o,t)||z(o,e))))))).getOr([])},eo=[{text:"None",value:""},{text:"Top",value:"top"},{text:"Middle",value:"middle"},{text:"Bottom",value:"bottom"}],to=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,oo=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,lo=e=>{return(t=e,Re(t,"#")?(e=>e.substring(1))(t):t).toUpperCase();var t},no=e=>{const t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()},ro=e=>{return t=no(e.red)+no(e.green)+no(e.blue),{value:lo(t)};var t},ao=/^\s*rgb\s*\(\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*(\d+)\s*\)\s*$/i,so=/^\s*rgba\s*\(\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*(\d+)\s*[,\s]\s*((?:\d?\.\d+|\d+)%?)\s*\)\s*$/i,co=(e,t,o,l)=>({red:e,green:t,blue:o,alpha:l}),io=(e,t,o,l)=>{const n=parseInt(e,10),r=parseInt(t,10),a=parseInt(o,10),s=parseFloat(l);return co(n,r,a,s)},mo=e=>{const t=ao.exec(e);if(null!==t)return y.some(io(t[1],t[2],t[3],"1"));const o=so.exec(e);return null!==o?y.some(io(o[1],o[2],o[3],o[4])):y.none()},uo=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},po=(e,t,o)=>l=>{const n=(e=>{const t=uo(y.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(y.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(y.some(e))}}})((e=>e.unbind())),r=!De(o),a=()=>{const a=Yt(e),s=l=>e.formatter.match(t,{value:o},l.dom,r);r?(l.setActive(!I(a,s)),n.set(e.formatter.formatChanged(t,(e=>l.setActive(!e)),!0))):(l.setActive(q(a,s)),n.set(e.formatter.formatChanged(t,l.setActive,!1,{value:o})))};return e.initialized?a():e.on("init",a),n.clear},bo=e=>R(e,"menu"),go=e=>M(e,(e=>{const t=e.text||e.title||"";return bo(e)?{text:t,items:go(e.menu)}:{text:t,value:e.value}})),ho=e=>e.length?y.some(go([{text:"Select...",value:"mce-no-match"},...e])):y.none(),fo=(e,t,o,l)=>M(t,(t=>{const n=t.text||t.title;return bo(t)?{type:"nestedmenuitem",text:n,getSubmenuItems:()=>fo(e,t.menu,o,l)}:{text:n,type:"togglemenuitem",onAction:()=>l(t.value),onSetup:po(e,o,t.value)}})),yo=(e,t)=>o=>{e.execCommand("mceTableApplyCellStyle",!1,{[t]:o})},wo=e=>F(e,(e=>bo(e)?[{...e,menu:wo(e.menu)}]:De(e.value)?[e]:[])),So=(e,t,o,l)=>n=>n(fo(e,t,o,l)),Co=(e,t,o)=>{const l=M(t,(e=>{return{text:e.title,value:"#"+(o=e.value,(t=o,(e=>to.test(e)||oo.test(e))(t)?y.some({value:lo(t)}):y.none()).orThunk((()=>mo(o).map(ro))).getOrThunk((()=>{const e=document.createElement("canvas");e.height=1,e.width=1;const t=e.getContext("2d");t.clearRect(0,0,e.width,e.height),t.fillStyle="#FFFFFF",t.fillStyle=o,t.fillRect(0,0,1,1);const l=t.getImageData(0,0,1,1).data,n=l[0],r=l[1],a=l[2],s=l[3];return ro(co(n,r,a,s))}))).value,type:"choiceitem"};var t,o}));return[{type:"fancymenuitem",fancytype:"colorswatch",initData:{colors:l.length>0?l:void 0,allowCustomColors:!1},onAction:t=>{const l="remove"===t.value?"":t.value;e.execCommand("mceTableApplyCellStyle",!1,{[o]:l})}}]},vo=e=>()=>{const t="header"===e.queryCommandValue("mceTableRowType")?"body":"header";e.execCommand("mceTableRowType",!1,{type:t})},To=e=>()=>{const t="th"===e.queryCommandValue("mceTableColType")?"td":"th";e.execCommand("mceTableColType",!1,{type:t})},xo=[{name:"width",type:"input",label:"Width"},{name:"celltype",type:"listbox",label:"Cell type",items:[{text:"Cell",value:"td"},{text:"Header cell",value:"th"}]},{name:"scope",type:"listbox",label:"Scope",items:[{text:"None",value:""},{text:"Row",value:"row"},{text:"Column",value:"col"},{text:"Row group",value:"rowgroup"},{text:"Column group",value:"colgroup"}]},{name:"halign",type:"listbox",label:"Horizontal align",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{name:"valign",type:"listbox",label:"Vertical align",items:eo}],Ao=e=>xo.concat((e=>ho(wt(e)).map((e=>({name:"class",type:"listbox",label:"Class",items:e}))))(e).toArray()),Ro=(e,t)=>{const o=[{name:"borderstyle",type:"listbox",label:"Border style",items:[{text:"Select...",value:""}].concat(go(ut(e)))},{name:"bordercolor",type:"colorinput",label:"Border color"},{name:"backgroundcolor",type:"colorinput",label:"Background color"}];return{title:"Advanced",name:"advanced",items:"cell"===t?[{name:"borderwidth",type:"input",label:"Border width"}].concat(o):o}},Oo=(e,t)=>{const o=e.dom;return{setAttrib:(e,l)=>{o.setAttrib(t,e,l)},setStyle:(e,l)=>{o.setStyle(t,e,l)},setFormat:(o,l)=>{""===l?e.formatter.remove(o,{value:null},t,!0):e.formatter.apply(o,{value:l},t)}}},_o=ee("th"),Do=(e,t)=>e&&t?"sectionCells":e?"section":"cells",Io=e=>{const t=M(e,(e=>(e=>{const t="thead"===e.section,o=xe((e=>{const t=k(e,(e=>_o(e.element)));return 0===t.length?y.some("td"):t.length===e.length?y.some("th"):y.none()})(e.cells),"th");return"tfoot"===e.section?{type:"footer"}:t||o?{type:"header",subType:Do(t,o)}:{type:"body"}})(e).type)),o=D(t,"header"),l=D(t,"footer");if(o||l){const e=D(t,"body");return!o||e||l?o||e||!l?y.none():y.some("footer"):y.some("header")}return y.some("body")},No=(e,t)=>j(e.all,(e=>B(e.cells,(e=>z(t,e.element))))),Mo=(e,t,o)=>{const l=(e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t})(M(t.selection,(t=>{return(l=t,((e,t,o=h)=>o(t)?y.none():D(e,G(t))?y.some(t):ce(t,e.join(","),(e=>$(e,"table")||o(e))))(["td","th"],l,n)).bind((t=>No(e,t))).filter(o);var l,n})));return n=l,l.length>0?y.some(n):y.none();var n},Po=(e,t)=>Mo(e,t,f),ko=(e,t)=>q(t,(t=>((e,t)=>No(e,t).exists((e=>!e.isLocked)))(e,t))),Eo=(e,t)=>((e,t)=>t.mergable)(0,t).filter((t=>ko(e,t.cells))),Bo=(e,t)=>((e,t)=>t.unmergable)(0,t).filter((t=>ko(e,t))),Fo=((e=>{if(!n(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],o={};P(e,((l,r)=>{const a=w(l);if(1!==a.length)throw new Error("one and only one name per case");const s=a[0],c=l[s];if(void 0!==o[s])throw new Error("duplicate key detected:"+s);if("cata"===s)throw new Error("cannot have a case named cata (sorry)");if(!n(c))throw new Error("case arguments must be an array");t.push(s),o[s]=(...o)=>{const l=o.length;if(l!==c.length)throw new Error("Wrong number of arguments to case "+s+". Expected "+c.length+" ("+c+"), got "+l);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[r].apply(null,o)},match:e=>{const l=w(e);if(t.length!==l.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+l.join(","));if(!q(t,(e=>D(l,e))))throw new Error("Not all branches were specified when using match. Specified: "+l.join(", ")+"\nRequired: "+t.join(", "));return e[s].apply(null,o)},log:e=>{console.log(e,{constructors:t,constructor:s,params:o})}}}}))})([{none:[]},{only:["index"]},{left:["index","next"]},{middle:["prev","index","next"]},{right:["prev","index"]}]),(e,t)=>{const o=Ke(e);return Po(o,t).bind((e=>{const t=e[e.length-1],l=e[0].row,n=t.row+t.rowspan,r=o.all.slice(l,n);return Io(r)})).getOr("")}),qo=e=>{return Re(e,"rgb")?mo(t=e).map(ro).map((e=>"#"+e.value)).getOr(t):e;var t},Lo=e=>{const t=V.fromDom(e);return{borderwidth:ke(t,"border-width").getOr(""),borderstyle:ke(t,"border-style").getOr(""),bordercolor:ke(t,"border-color").map(qo).getOr(""),backgroundcolor:ke(t,"background-color").map(qo).getOr("")}},jo=e=>{const t=e[0],o=e.slice(1);return P(o,(e=>{P(w(t),(o=>{C(e,((e,l)=>{const n=t[o];""!==n&&o===l&&n!==e&&(t[o]="class"===o?"mce-no-match":"")}))}))})),t},Ho=(e,t,o,l)=>B(e,(e=>!a(o.formatter.matchNode(l,t+e)))).getOr(""),Vo=b(Ho,["left","center","right"],"align"),$o=b(Ho,["top","middle","bottom"],"valign"),Wo=e=>He(V.fromDom(e)).map((t=>{const o={selection:Wt(e.cells)};return Fo(t,o)})).getOr(""),zo=(e,t)=>{const o=Ke(e),l=(e=>F(e.all,(e=>e.cells)))(o),n=k(l,(e=>I(t,(t=>z(e.element,t)))));return M(n,(e=>({element:e.element.dom,column:Xe(o,e.column).map((e=>e.element.dom))})))},Uo=(e,t,o,l)=>{const n=l.getData();l.close(),e.undoManager.transact((()=>{((e,t,o,l)=>{const n=v(l,((e,t)=>o[t]!==e));T(n)>0&&t.length>=1&&He(t[0]).each((o=>{const r=zo(o,t),a=T(v(n,((e,t)=>"scope"!==t&&"celltype"!==t)))>0,s=A(n,"celltype");(a||A(n,"scope"))&&((e,t,o,l)=>{const n=1===t.length;P(t,(t=>{const r=t.element,a=n?f:l,s=Oo(e,r);((e,t,o,l)=>{l("scope")&&e.setAttrib("scope",o.scope),l("class")&&"mce-no-match"!==o.class&&e.setAttrib("class",o.class),l("width")&&t.setStyle("width",ge(o.width))})(s,t.column.map((t=>Oo(e,t))).getOr(s),o,a),pt(e)&&((e,t,o)=>{o("backgroundcolor")&&e.setFormat("tablecellbackgroundcolor",t.backgroundcolor),o("bordercolor")&&e.setFormat("tablecellbordercolor",t.bordercolor),o("borderstyle")&&e.setFormat("tablecellborderstyle",t.borderstyle),o("borderwidth")&&e.setFormat("tablecellborderwidth",ge(t.borderwidth))})(s,o,a),l("halign")&&et(e,r,o.halign),l("valign")&&((e,t,o)=>{Ye.each("top middle bottom".split(" "),(l=>{l!==o&&e.formatter.remove("valign"+l,{},t)})),o&&e.formatter.apply("valign"+o,{},t)})(e,r,o.valign)}))})(e,r,l,b(A,n)),s&&((e,t)=>{e.execCommand("mceTableCellType",!1,{type:t.celltype,no_events:!0})})(e,l),tt(e,o.dom,{structure:s,style:a})}))})(e,t,o,n),e.focus()}))},Go=e=>{const t=Yt(e);if(0===t.length)return;const o=((e,t)=>{const o=He(t[0]).map((o=>M(zo(o,t),(t=>((e,t,o,l)=>{const n=e.dom;return{width:(a=l.getOr(t),s="width",n.getStyle(a,s)||n.getAttrib(a,s)),scope:n.getAttrib(t,"scope"),celltype:(r=t,r.nodeName.toLowerCase()),class:n.getAttrib(t,"class",""),halign:Vo(e,t),valign:$o(e,t),...o?Lo(t):{}};var r,a,s})(e,t.element,pt(e),t.column)))));return jo(o.getOrDie())})(e,t),l={type:"tabpanel",tabs:[{title:"General",name:"general",items:Ao(e)},Ro(e,"cell")]},n={type:"panel",items:[{type:"grid",columns:2,items:Ao(e)}]};e.windowManager.open({title:"Cell Properties",size:"normal",body:pt(e)?l:n,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:o,onSubmit:b(Uo,e,t,o)})},Ko=[{type:"listbox",name:"type",label:"Row type",items:[{text:"Header",value:"header"},{text:"Body",value:"body"},{text:"Footer",value:"footer"}]},{type:"listbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"Height",name:"height",type:"input"}],Jo=e=>Ko.concat((e=>ho(St(e)).map((e=>({name:"class",type:"listbox",label:"Class",items:e}))))(e).toArray()),Qo=(e,t,o,l)=>{const n=l.getData();l.close(),e.undoManager.transact((()=>{((e,t,o,l)=>{const n=v(l,((e,t)=>o[t]!==e));if(T(n)>0){const o=A(n,"type"),r=!o||T(n)>1;r&&((e,t,o,l)=>{const n=1===t.length?f:l;P(t,(t=>{const r=ye(V.fromDom(t),"td,th"),a=Oo(e,t);((e,t,o)=>{o("class")&&"mce-no-match"!==t.class&&e.setAttrib("class",t.class),o("height")&&e.setStyle("height",ge(t.height))})(a,o,n),bt(e)&&((e,t,o)=>{o("backgroundcolor")&&e.setStyle("background-color",t.backgroundcolor),o("bordercolor")&&e.setStyle("border-color",t.bordercolor),o("borderstyle")&&e.setStyle("border-style",t.borderstyle)})(a,o,n),l("height")&&P(r,(t=>{e.dom.setStyle(t.dom,"height",null)})),l("align")&&et(e,t,o.align)}))})(e,t,l,b(A,n)),o&&((e,t)=>{e.execCommand("mceTableRowType",!1,{type:t.type,no_events:!0})})(e,l),He(V.fromDom(t[0])).each((t=>tt(e,t.dom,{structure:o,style:r})))}})(e,t,o,n),e.focus()}))},Xo=e=>{const t=Zt(he(e),Kt.selected);if(0===t.length)return;const o=M(t,(t=>((e,t,o)=>{const l=e.dom;return{height:l.getStyle(t,"height")||l.getAttrib(t,"height"),class:l.getAttrib(t,"class",""),type:Wo(t),align:Vo(e,t),...o?Lo(t):{}}})(e,t.dom,bt(e)))),l=jo(o),n={type:"tabpanel",tabs:[{title:"General",name:"general",items:Jo(e)},Ro(e,"row")]},r={type:"panel",items:[{type:"grid",columns:2,items:Jo(e)}]};e.windowManager.open({title:"Row Properties",size:"normal",body:bt(e)?n:r,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:l,onSubmit:b(Qo,e,M(t,(e=>e.dom)),l)})},Yo=(e,t,o)=>{const l=o?[{type:"input",name:"cols",label:"Cols",inputMode:"numeric"},{type:"input",name:"rows",label:"Rows",inputMode:"numeric"}]:[],n=ht(e)?[{type:"input",name:"cellspacing",label:"Cell spacing",inputMode:"numeric"},{type:"input",name:"cellpadding",label:"Cell padding",inputMode:"numeric"},{type:"input",name:"border",label:"Border width"},{type:"label",label:"Caption",items:[{type:"checkbox",name:"caption",label:"Show caption"}]}]:[],r=t.length>0?[{name:"class",type:"listbox",label:"Class",items:t}]:[];return l.concat([{type:"input",name:"width",label:"Width"},{type:"input",name:"height",label:"Height"}]).concat(n).concat([{type:"listbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]}]).concat(r)},Zo=(e,t,o,n)=>{if("TD"===t.tagName||"TH"===t.tagName)l(o)&&s(n)?e.setStyle(t,o,n):e.setStyles(t,o);else if(t.children)for(let l=0;l<t.children.length;l++)Zo(e,t.children[l],o,n)},el=(e,t,o,l)=>{const n=e.dom,r=l.getData(),s=v(r,((e,t)=>o[t]!==e));l.close(),e.undoManager.transact((()=>{if(!t){const o=Ie(r.cols).getOr(1),l=Ie(r.rows).getOr(1);e.execCommand("mceInsertTable",!1,{rows:l,columns:o}),t=Xt(he(e),be(e)).bind((t=>He(t,be(e)))).map((e=>e.dom)).getOrDie()}if(T(s)>0){const o={border:A(s,"border"),bordercolor:A(s,"bordercolor"),cellpadding:A(s,"cellpadding")};((e,t,o,l)=>{const n=e.dom,r={},s={},c=yt(e),i=gt(e),m=0===parseFloat(o.border);if(a(o.class)||"mce-no-match"===o.class||(r.class=o.class),s.height=ge(o.height),c?s.width=ge(o.width):n.getAttrib(t,"width")&&(r.width=(e=>e?e.replace(/px$/,""):"")(o.width)),c?(m?(r.border=0,s["border-width"]=""):(s["border-width"]=ge(o.border),r.border=1),s["border-spacing"]=ge(o.cellspacing)):(r.border=m?0:o.border,r.cellpadding=o.cellpadding,r.cellspacing=o.cellspacing),c&&t.children){const e={};if(m?e["border-width"]="":l.border&&(e["border-width"]=ge(o.border)),l.cellpadding&&(e.padding=ge(o.cellpadding)),i&&l.bordercolor&&(e["border-color"]=o.bordercolor),!(e=>{for(const t in e)if(S.call(e,t))return!1;return!0})(e))for(let o=0;o<t.children.length;o++)Zo(n,t.children[o],e)}if(i){const e=o;s["background-color"]=e.backgroundcolor,s["border-color"]=e.bordercolor,s["border-style"]=e.borderstyle}n.setStyles(t,{...Ot(e),...s}),n.setAttribs(t,{..._t(e),...r})})(e,t,r,o);const l=n.select("caption",t)[0];(l&&!r.caption||!l&&r.caption)&&e.execCommand("mceTableToggleCaption"),et(e,t,r.align)}if(e.focus(),e.addVisual(),T(s)>0){const o=A(s,"caption"),l=!o||T(s)>1;tt(e,t,{structure:o,style:l})}}))},tl=(e,t)=>{const o=e.dom;let l,n=((e,t)=>{const o=Ot(e),l=_t(e),n=t?{borderstyle:x(o,"border-style").getOr(""),bordercolor:qo(x(o,"border-color").getOr("")),backgroundcolor:qo(x(o,"background-color").getOr(""))}:{};return{height:"",width:"100%",cellspacing:"",cellpadding:"",caption:!1,class:"",align:"",border:"",...o,...l,...n,...(()=>{const t=o["border-width"];return yt(e)&&t?{border:t}:x(l,"border").fold((()=>({})),(e=>({border:e})))})(),...{...x(o,"border-spacing").or(x(l,"cellspacing")).fold((()=>({})),(e=>({cellspacing:e}))),...x(o,"border-padding").or(x(l,"cellpadding")).fold((()=>({})),(e=>({cellpadding:e})))}}})(e,gt(e));t?(n.cols="1",n.rows="1",gt(e)&&(n.borderstyle="",n.bordercolor="",n.backgroundcolor="")):(l=o.getParent(e.selection.getStart(),"table",e.getBody()),l?n=((e,t,o)=>{const l=e.dom,n=yt(e)?l.getStyle(t,"border-spacing")||l.getAttrib(t,"cellspacing"):l.getAttrib(t,"cellspacing")||l.getStyle(t,"border-spacing"),r=yt(e)?Ze(l,t,"padding")||l.getAttrib(t,"cellpadding"):l.getAttrib(t,"cellpadding")||Ze(l,t,"padding");return{width:l.getStyle(t,"width")||l.getAttrib(t,"width"),height:l.getStyle(t,"height")||l.getAttrib(t,"height"),cellspacing:null!=n?n:"",cellpadding:null!=r?r:"",border:((t,o)=>{const l=ke(V.fromDom(o),"border-width");return yt(e)&&l.isSome()?l.getOr(""):t.getAttrib(o,"border")||Ze(e.dom,o,"border-width")||Ze(e.dom,o,"border")||""})(l,t),caption:!!l.select("caption",t)[0],class:l.getAttrib(t,"class",""),align:Vo(e,t),...o?Lo(t):{}}})(e,l,gt(e)):gt(e)&&(n.borderstyle="",n.bordercolor="",n.backgroundcolor=""));const r=ho(Ct(e));r.isSome()&&n.class&&(n.class=n.class.replace(/\s*mce\-item\-table\s*/g,""));const a={type:"grid",columns:2,items:Yo(e,r.getOr([]),t)},s=gt(e)?{type:"tabpanel",tabs:[{title:"General",name:"general",items:[a]},Ro(e,"table")]}:{type:"panel",items:[a]};e.windowManager.open({title:"Table Properties",size:"normal",body:s,onSubmit:b(el,e,l,n),buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:n})},ol=e=>{C({mceTableProps:b(tl,e,!1),mceTableRowProps:b(Xo,e),mceTableCellProps:b(Go,e),mceInsertTableDialog:b(tl,e,!0)},((t,o)=>e.addCommand(o,(()=>{return o=t,void((e=>{return(t=e,o=ee("table"),ae(((e,t)=>t(e)),se,t,o,void 0)).forall(ue);var t,o})(he(e))&&o());var o}))))},ll=u,nl=e=>{const t=(e,t)=>ve(e,t).exists((e=>parseInt(e,10)>1));return e.length>0&&q(e,(e=>t(e,"rowspan")||t(e,"colspan")))?y.some(e):y.none()},rl=(e,t,o)=>{return t.length<=1?y.none():(l=e,n=o.firstSelectedSelector,r=o.lastSelectedSelector,$t(l,n,r).bind((e=>{const t=e=>z(l,e),o="thead,tfoot,tbody,table",n=ce(e.first,o,t),r=ce(e.last,o,t);return n.bind((t=>r.bind((o=>z(t,o)?((e,t,o)=>{const l=Nt(e);return It(l,t,o)})(e.table,e.first,e.last):y.none()))))}))).map((e=>({bounds:e,cells:t})));var l,n,r},al=e=>{const t=uo(y.none()),o=uo([]);let l=y.none();const n=ee("caption"),r=e=>l.forall((t=>!t[e])),a=()=>Qt(he(e),be(e)).bind((t=>{return o=Ae(He(t),Qt((e=>V.fromDom(e.selection.getEnd()))(e),be(e)).bind(He),((o,l)=>z(o,l)?n(t)?y.some((e=>({element:e,mergable:y.none(),unmergable:y.none(),selection:[e]}))(t)):y.some(((e,t,o)=>({element:o,mergable:rl(t,e,Kt),unmergable:nl(e),selection:ll(e)}))(Yt(e),o,t)):y.none())),o.bind(u);var o})),s=e=>He(e.element).map((t=>{const o=Ke(t),l=Po(o,e).getOr([]),n=E(l,((e,t)=>(t.isLocked&&(e.onAny=!0,0===t.column?e.onFirst=!0:t.column+t.colspan>=o.grid.columns&&(e.onLast=!0)),e)),{onAny:!1,onFirst:!1,onLast:!1});return{mergeable:Eo(o,e).isSome(),unmergeable:Bo(o,e).isSome(),locked:n}})),c=()=>{t.set((e=>{let t,o=!1;return(...l)=>(o||(o=!0,t=e.apply(null,l)),t)})(a)()),l=t.get().bind(s),P(o.get(),g)},i=e=>(e(),o.set(o.get().concat([e])),()=>{o.set(k(o.get(),(t=>t!==e)))}),m=(o,l)=>i((()=>t.get().fold((()=>{o.setEnabled(!1)}),(t=>{o.setEnabled(!l(t)&&e.selection.isEditable())})))),d=(o,l,n)=>i((()=>t.get().fold((()=>{o.setEnabled(!1),o.setActive(!1)}),(t=>{o.setEnabled(!l(t)&&e.selection.isEditable()),o.setActive(n(t))})))),p=e=>l.exists((t=>t.locked[e])),b=(t,o)=>l=>d(l,(e=>n(e.element)),(()=>e.queryCommandValue(t)===o)),f=b("mceTableRowType","header"),w=b("mceTableColType","th");return e.on("NodeChange ExecCommand TableSelectorChange",c),{onSetupTable:e=>m(e,(e=>!1)),onSetupCellOrRow:e=>m(e,(e=>n(e.element))),onSetupColumn:e=>t=>m(t,(t=>n(t.element)||p(e))),onSetupPasteable:e=>t=>m(t,(t=>n(t.element)||e().isNone())),onSetupPasteableColumn:(e,t)=>o=>m(o,(o=>n(o.element)||e().isNone()||p(t))),onSetupMergeable:e=>m(e,(e=>r("mergeable"))),onSetupUnmergeable:e=>m(e,(e=>r("unmergeable"))),resetTargets:c,onSetupTableWithCaption:t=>d(t,h,(t=>He(t.element,be(e)).exists((e=>ie(e,"caption").isSome())))),onSetupTableRowHeaders:f,onSetupTableColumnHeaders:w,targets:t.get}};var sl=tinymce.util.Tools.resolve("tinymce.FakeClipboard");const cl="x-tinymce/dom-table-",il=cl+"rows",ml=cl+"columns",dl=e=>{var t;const o=null!==(t=sl.read())&&void 0!==t?t:[];return j(o,(t=>y.from(t.getType(e))))},ul=()=>dl(il),pl=()=>dl(ml),bl=e=>t=>{const o=()=>{t.setEnabled(e.selection.isEditable())};return e.on("NodeChange",o),o(),()=>{e.off("NodeChange",o)}},gl=e=>t=>{const o=()=>{t.setEnabled(e.selection.isEditable())};return e.on("NodeChange",o),o(),()=>{e.off("NodeChange",o)}};e.add("table",(e=>{const t=al(e);(e=>{const t=e.options.register;t("table_border_widths",{processor:"object[]",default:rt}),t("table_border_styles",{processor:"object[]",default:at}),t("table_cell_advtab",{processor:"boolean",default:!0}),t("table_row_advtab",{processor:"boolean",default:!0}),t("table_advtab",{processor:"boolean",default:!0}),t("table_appearance_options",{processor:"boolean",default:!0}),t("table_grid",{processor:"boolean",default:!nt.deviceType.isTouch()}),t("table_cell_class_list",{processor:"object[]",default:[]}),t("table_row_class_list",{processor:"object[]",default:[]}),t("table_class_list",{processor:"object[]",default:[]}),t("table_toolbar",{processor:"string",default:"tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol"}),t("table_background_color_map",{processor:"object[]",default:[]}),t("table_border_color_map",{processor:"object[]",default:[]})})(e),ol(e),((e,t)=>{const o=t=>()=>e.execCommand(t),l=(t,l)=>!!e.queryCommandSupported(l.command)&&(e.ui.registry.addMenuItem(t,{...l,onAction:c(l.onAction)?l.onAction:o(l.command)}),!0),n=(t,l)=>{e.queryCommandSupported(l.command)&&e.ui.registry.addToggleMenuItem(t,{...l,onAction:c(l.onAction)?l.onAction:o(l.command)})},r=t=>{e.execCommand("mceInsertTable",!1,{rows:t.numRows,columns:t.numColumns})},a=[l("tableinsertrowbefore",{text:"Insert row before",icon:"table-insert-row-above",command:"mceTableInsertRowBefore",onSetup:t.onSetupCellOrRow}),l("tableinsertrowafter",{text:"Insert row after",icon:"table-insert-row-after",command:"mceTableInsertRowAfter",onSetup:t.onSetupCellOrRow}),l("tabledeleterow",{text:"Delete row",icon:"table-delete-row",command:"mceTableDeleteRow",onSetup:t.onSetupCellOrRow}),l("tablerowprops",{text:"Row properties",icon:"table-row-properties",command:"mceTableRowProps",onSetup:t.onSetupCellOrRow}),l("tablecutrow",{text:"Cut row",icon:"cut-row",command:"mceTableCutRow",onSetup:t.onSetupCellOrRow}),l("tablecopyrow",{text:"Copy row",icon:"duplicate-row",command:"mceTableCopyRow",onSetup:t.onSetupCellOrRow}),l("tablepasterowbefore",{text:"Paste row before",icon:"paste-row-before",command:"mceTablePasteRowBefore",onSetup:t.onSetupPasteable(ul)}),l("tablepasterowafter",{text:"Paste row after",icon:"paste-row-after",command:"mceTablePasteRowAfter",onSetup:t.onSetupPasteable(ul)})],s=[l("tableinsertcolumnbefore",{text:"Insert column before",icon:"table-insert-column-before",command:"mceTableInsertColBefore",onSetup:t.onSetupColumn("onFirst")}),l("tableinsertcolumnafter",{text:"Insert column after",icon:"table-insert-column-after",command:"mceTableInsertColAfter",onSetup:t.onSetupColumn("onLast")}),l("tabledeletecolumn",{text:"Delete column",icon:"table-delete-column",command:"mceTableDeleteCol",onSetup:t.onSetupColumn("onAny")}),l("tablecutcolumn",{text:"Cut column",icon:"cut-column",command:"mceTableCutCol",onSetup:t.onSetupColumn("onAny")}),l("tablecopycolumn",{text:"Copy column",icon:"duplicate-column",command:"mceTableCopyCol",onSetup:t.onSetupColumn("onAny")}),l("tablepastecolumnbefore",{text:"Paste column before",icon:"paste-column-before",command:"mceTablePasteColBefore",onSetup:t.onSetupPasteableColumn(pl,"onFirst")}),l("tablepastecolumnafter",{text:"Paste column after",icon:"paste-column-after",command:"mceTablePasteColAfter",onSetup:t.onSetupPasteableColumn(pl,"onLast")})],i=[l("tablecellprops",{text:"Cell properties",icon:"table-cell-properties",command:"mceTableCellProps",onSetup:t.onSetupCellOrRow}),l("tablemergecells",{text:"Merge cells",icon:"table-merge-cells",command:"mceTableMergeCells",onSetup:t.onSetupMergeable}),l("tablesplitcells",{text:"Split cell",icon:"table-split-cells",command:"mceTableSplitCells",onSetup:t.onSetupUnmergeable})];ft(e)?e.ui.registry.addNestedMenuItem("inserttable",{text:"Table",icon:"table",getSubmenuItems:()=>[{type:"fancymenuitem",fancytype:"inserttable",onAction:r}],onSetup:gl(e)}):e.ui.registry.addMenuItem("inserttable",{text:"Table",icon:"table",onAction:o("mceInsertTableDialog"),onSetup:gl(e)}),e.ui.registry.addMenuItem("inserttabledialog",{text:"Insert table",icon:"table",onAction:o("mceInsertTableDialog"),onSetup:gl(e)}),l("tableprops",{text:"Table properties",onSetup:t.onSetupTable,command:"mceTableProps"}),l("deletetable",{text:"Delete table",icon:"table-delete-table",onSetup:t.onSetupTable,command:"mceTableDelete"}),D(a,!0)&&e.ui.registry.addNestedMenuItem("row",{type:"nestedmenuitem",text:"Row",getSubmenuItems:d("tableinsertrowbefore tableinsertrowafter tabledeleterow tablerowprops | tablecutrow tablecopyrow tablepasterowbefore tablepasterowafter")}),D(s,!0)&&e.ui.registry.addNestedMenuItem("column",{type:"nestedmenuitem",text:"Column",getSubmenuItems:d("tableinsertcolumnbefore tableinsertcolumnafter tabledeletecolumn | tablecutcolumn tablecopycolumn tablepastecolumnbefore tablepastecolumnafter")}),D(i,!0)&&e.ui.registry.addNestedMenuItem("cell",{type:"nestedmenuitem",text:"Cell",getSubmenuItems:d("tablecellprops tablemergecells tablesplitcells")}),e.ui.registry.addContextMenu("table",{update:()=>(t.resetTargets(),t.targets().fold(d(""),(e=>"caption"===G(e.element)?"tableprops deletetable":"cell row column | advtablesort | tableprops deletetable")))});const m=wo(Ct(e));0!==m.length&&e.queryCommandSupported("mceTableToggleClass")&&e.ui.registry.addNestedMenuItem("tableclass",{icon:"table-classes",text:"Table styles",getSubmenuItems:()=>fo(e,m,"tableclass",(t=>e.execCommand("mceTableToggleClass",!1,t))),onSetup:t.onSetupTable});const u=wo(wt(e));0!==u.length&&e.queryCommandSupported("mceTableCellToggleClass")&&e.ui.registry.addNestedMenuItem("tablecellclass",{icon:"table-cell-classes",text:"Cell styles",getSubmenuItems:()=>fo(e,u,"tablecellclass",(t=>e.execCommand("mceTableCellToggleClass",!1,t))),onSetup:t.onSetupCellOrRow}),e.queryCommandSupported("mceTableApplyCellStyle")&&(e.ui.registry.addNestedMenuItem("tablecellvalign",{icon:"vertical-align",text:"Vertical align",getSubmenuItems:()=>fo(e,eo,"tablecellverticalalign",yo(e,"vertical-align")),onSetup:t.onSetupCellOrRow}),e.ui.registry.addNestedMenuItem("tablecellborderwidth",{icon:"border-width",text:"Border width",getSubmenuItems:()=>fo(e,dt(e),"tablecellborderwidth",yo(e,"border-width")),onSetup:t.onSetupCellOrRow}),e.ui.registry.addNestedMenuItem("tablecellborderstyle",{icon:"border-style",text:"Border style",getSubmenuItems:()=>fo(e,ut(e),"tablecellborderstyle",yo(e,"border-style")),onSetup:t.onSetupCellOrRow}),e.ui.registry.addNestedMenuItem("tablecellbackgroundcolor",{icon:"cell-background-color",text:"Background color",getSubmenuItems:()=>Co(e,Tt(e),"background-color"),onSetup:t.onSetupCellOrRow}),e.ui.registry.addNestedMenuItem("tablecellbordercolor",{icon:"cell-border-color",text:"Border color",getSubmenuItems:()=>Co(e,xt(e),"border-color"),onSetup:t.onSetupCellOrRow})),n("tablecaption",{icon:"table-caption",text:"Table caption",command:"mceTableToggleCaption",onSetup:t.onSetupTableWithCaption}),n("tablerowheader",{text:"Row header",icon:"table-top-header",command:"mceTableRowType",onAction:vo(e),onSetup:t.onSetupTableRowHeaders}),n("tablecolheader",{text:"Column header",icon:"table-left-header",command:"mceTableColType",onAction:To(e),onSetup:t.onSetupTableRowHeaders})})(e,t),((e,t)=>{e.ui.registry.addMenuButton("table",{tooltip:"Table",icon:"table",onSetup:bl(e),fetch:e=>e("inserttable | cell row column | advtablesort | tableprops deletetable")});const o=t=>()=>e.execCommand(t),l=(t,l)=>{e.queryCommandSupported(l.command)&&e.ui.registry.addButton(t,{...l,onAction:c(l.onAction)?l.onAction:o(l.command)})},n=(t,l)=>{e.queryCommandSupported(l.command)&&e.ui.registry.addToggleButton(t,{...l,onAction:c(l.onAction)?l.onAction:o(l.command)})};l("tableprops",{tooltip:"Table properties",command:"mceTableProps",icon:"table",onSetup:t.onSetupTable}),l("tabledelete",{tooltip:"Delete table",command:"mceTableDelete",icon:"table-delete-table",onSetup:t.onSetupTable}),l("tablecellprops",{tooltip:"Cell properties",command:"mceTableCellProps",icon:"table-cell-properties",onSetup:t.onSetupCellOrRow}),l("tablemergecells",{tooltip:"Merge cells",command:"mceTableMergeCells",icon:"table-merge-cells",onSetup:t.onSetupMergeable}),l("tablesplitcells",{tooltip:"Split cell",command:"mceTableSplitCells",icon:"table-split-cells",onSetup:t.onSetupUnmergeable}),l("tableinsertrowbefore",{tooltip:"Insert row before",command:"mceTableInsertRowBefore",icon:"table-insert-row-above",onSetup:t.onSetupCellOrRow}),l("tableinsertrowafter",{tooltip:"Insert row after",command:"mceTableInsertRowAfter",icon:"table-insert-row-after",onSetup:t.onSetupCellOrRow}),l("tabledeleterow",{tooltip:"Delete row",command:"mceTableDeleteRow",icon:"table-delete-row",onSetup:t.onSetupCellOrRow}),l("tablerowprops",{tooltip:"Row properties",command:"mceTableRowProps",icon:"table-row-properties",onSetup:t.onSetupCellOrRow}),l("tableinsertcolbefore",{tooltip:"Insert column before",command:"mceTableInsertColBefore",icon:"table-insert-column-before",onSetup:t.onSetupColumn("onFirst")}),l("tableinsertcolafter",{tooltip:"Insert column after",command:"mceTableInsertColAfter",icon:"table-insert-column-after",onSetup:t.onSetupColumn("onLast")}),l("tabledeletecol",{tooltip:"Delete column",command:"mceTableDeleteCol",icon:"table-delete-column",onSetup:t.onSetupColumn("onAny")}),l("tablecutrow",{tooltip:"Cut row",command:"mceTableCutRow",icon:"cut-row",onSetup:t.onSetupCellOrRow}),l("tablecopyrow",{tooltip:"Copy row",command:"mceTableCopyRow",icon:"duplicate-row",onSetup:t.onSetupCellOrRow}),l("tablepasterowbefore",{tooltip:"Paste row before",command:"mceTablePasteRowBefore",icon:"paste-row-before",onSetup:t.onSetupPasteable(ul)}),l("tablepasterowafter",{tooltip:"Paste row after",command:"mceTablePasteRowAfter",icon:"paste-row-after",onSetup:t.onSetupPasteable(ul)}),l("tablecutcol",{tooltip:"Cut column",command:"mceTableCutCol",icon:"cut-column",onSetup:t.onSetupColumn("onAny")}),l("tablecopycol",{tooltip:"Copy column",command:"mceTableCopyCol",icon:"duplicate-column",onSetup:t.onSetupColumn("onAny")}),l("tablepastecolbefore",{tooltip:"Paste column before",command:"mceTablePasteColBefore",icon:"paste-column-before",onSetup:t.onSetupPasteableColumn(pl,"onFirst")}),l("tablepastecolafter",{tooltip:"Paste column after",command:"mceTablePasteColAfter",icon:"paste-column-after",onSetup:t.onSetupPasteableColumn(pl,"onLast")}),l("tableinsertdialog",{tooltip:"Insert table",command:"mceInsertTableDialog",icon:"table",onSetup:bl(e)});const r=wo(Ct(e));0!==r.length&&e.queryCommandSupported("mceTableToggleClass")&&e.ui.registry.addMenuButton("tableclass",{icon:"table-classes",tooltip:"Table styles",fetch:So(e,r,"tableclass",(t=>e.execCommand("mceTableToggleClass",!1,t))),onSetup:t.onSetupTable});const a=wo(wt(e));0!==a.length&&e.queryCommandSupported("mceTableCellToggleClass")&&e.ui.registry.addMenuButton("tablecellclass",{icon:"table-cell-classes",tooltip:"Cell styles",fetch:So(e,a,"tablecellclass",(t=>e.execCommand("mceTableCellToggleClass",!1,t))),onSetup:t.onSetupCellOrRow}),e.queryCommandSupported("mceTableApplyCellStyle")&&(e.ui.registry.addMenuButton("tablecellvalign",{icon:"vertical-align",tooltip:"Vertical align",fetch:So(e,eo,"tablecellverticalalign",yo(e,"vertical-align")),onSetup:t.onSetupCellOrRow}),e.ui.registry.addMenuButton("tablecellborderwidth",{icon:"border-width",tooltip:"Border width",fetch:So(e,dt(e),"tablecellborderwidth",yo(e,"border-width")),onSetup:t.onSetupCellOrRow}),e.ui.registry.addMenuButton("tablecellborderstyle",{icon:"border-style",tooltip:"Border style",fetch:So(e,ut(e),"tablecellborderstyle",yo(e,"border-style")),onSetup:t.onSetupCellOrRow}),e.ui.registry.addMenuButton("tablecellbackgroundcolor",{icon:"cell-background-color",tooltip:"Background color",fetch:t=>t(Co(e,Tt(e),"background-color")),onSetup:t.onSetupCellOrRow}),e.ui.registry.addMenuButton("tablecellbordercolor",{icon:"cell-border-color",tooltip:"Border color",fetch:t=>t(Co(e,xt(e),"border-color")),onSetup:t.onSetupCellOrRow})),n("tablecaption",{tooltip:"Table caption",icon:"table-caption",command:"mceTableToggleCaption",onSetup:t.onSetupTableWithCaption}),n("tablerowheader",{tooltip:"Row header",icon:"table-top-header",command:"mceTableRowType",onAction:vo(e),onSetup:t.onSetupTableRowHeaders}),n("tablecolheader",{tooltip:"Column header",icon:"table-left-header",command:"mceTableColType",onAction:To(e),onSetup:t.onSetupTableColumnHeaders})})(e,t),(e=>{const t=vt(e);t.length>0&&e.ui.registry.addContextToolbar("table",{predicate:t=>e.dom.is(t,"table")&&e.getBody().contains(t)&&e.dom.isEditable(t.parentNode),items:t,scope:"node",position:"node"})})(e)}))}();