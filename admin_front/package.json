{"name": "email-automation-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.2", "@hookform/resolvers": "^5.0.1", "@tinymce/tinymce-react": "^6.1.0", "@tiptap/extension-image": "^2.11.9", "@tiptap/react": "^2.11.9", "@tiptap/starter-kit": "^2.11.9", "date-fns": "^4.1.0", "jwt-decode": "^4.0.0", "lucide-react": "^0.507.0", "next": "15.3.1", "papaparse": "^5.5.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.2", "tinymce": "^7.8.0", "xlsx": "^0.18.5", "zod": "^3.24.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4", "typescript": "^5"}}