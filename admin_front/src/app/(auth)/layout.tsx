'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const router = useRouter();
  const [isClient, setIsClient] = useState(false);
  const [loading, setLoading] = useState(true);

  // Ensure we're on the client side
  useEffect(() => {
    setIsClient(true);
    setLoading(false);
  }, []);

  // Check for user in localStorage directly
  useEffect(() => {
    if (isClient) {
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        console.log('User found in localStorage, redirecting to dashboard');
        router.push('/dashboard');
      }
    }
  }, [isClient, router]);

  // Show loading indicator while checking
  if (loading || !isClient) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="w-16 h-16 border-t-4 border-b-4 border-indigo-500 rounded-full animate-spin"></div>
      </div>
    );
  }

  // Render auth layout for non-authenticated users
  return (
    <div className="min-h-screen bg-gray-100">
      {children}
    </div>
  );
}