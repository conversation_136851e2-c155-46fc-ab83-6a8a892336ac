'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import MailConfigForm from '@/components/mail-config/MailConfigForm';
import { mailConfigService } from '@/lib/api/mail-config';
import { MailConfig } from '@/types/mail-config';
import { useToast } from '@/context/toast-context';
import Link from 'next/link';

export default function EditMailConfigPage() {
  const router = useRouter();
  const params = useParams();
  const id = params.id as string;
  
  const [mailConfig, setMailConfig] = useState<MailConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const { showToast } = useToast();
  
  useEffect(() => {
    async function loadMailConfig() {
      if (!id) return;
      
      try {
        setLoading(true);
        console.log('Loading mail config with ID:', id);
        
        // Use our service to get the mail configuration
        const data = await mailConfigService.getMailConfig(id);
        console.log('Loaded mail config:', data.mail_config);
        
        if (data && data.mail_config) {
          setMailConfig(data.mail_config);
        } else {
          console.error('Failed to load mail config:', data);
          setError('Failed to load SMTP configuration data');
          showToast('Failed to load SMTP configuration data');
        }
      } catch (err: any) {
        console.error('Error loading mail configuration:', err);
        setError(`Failed to load SMTP configuration. ${err.message || 'Please try again.'}`);
        showToast('Failed to load SMTP configuration. Please try again.');
      } finally {
        setLoading(false);
      }
    }
    
    loadMailConfig();
  }, [id]);
  
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="w-12 h-12 border-4 border-indigo-500 rounded-full border-t-transparent animate-spin"></div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="p-4 text-sm text-red-700 bg-red-100 rounded-md">
        {error}
        <button 
          onClick={() => router.push('/mail-config')}
          className="ml-4 underline text-red-700 hover:text-red-900"
        >
          Go back
        </button>
      </div>
    );
  }
  
  if (!mailConfig) {
    return (
      <div className="p-4 text-sm text-yellow-700 bg-yellow-100 rounded-md">
        Configuration not found.
        <button 
          onClick={() => router.push('/mail-config')}
          className="ml-4 underline text-yellow-700 hover:text-yellow-900"
        >
          Go back
        </button>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Link
            href="/mail-config"
            className="mr-4 text-gray-500 hover:text-gray-700"
          >
            <i className="fas fa-arrow-left"></i>
          </Link>
          <h1 className="text-2xl font-bold text-gray-900">
            Edit SMTP Configuration: {mailConfig.sender_name || 'Unnamed'}
          </h1>
        </div>
      </div>
      
      <MailConfigForm initialData={mailConfig} isEditing={true} />
    </div>
  );
}