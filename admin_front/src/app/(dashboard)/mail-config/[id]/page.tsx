'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import { mailConfigService } from '@/lib/api/mail-config';
import { fetchAPI } from '@/lib/api/fetchAPI';
import { MailConfig } from '@/types/mail-config';
import { useToast } from '@/context/toast-context';
import ConfirmationModal from '@/components/ui/ConfirmationModal';

interface Company {
  id: string;
  name: string;
  connection_string: string;
  database_type: string;
  started_at: string;
  created_at: string;
  updated_at: string;
}

export default function MailConfigDetailPage() {
  const router = useRouter();
  const params = useParams();
  const id = params.id as string;
  
  const [mailConfig, setMailConfig] = useState<MailConfig | null>(null);
  const [company, setCompany] = useState<Company | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [testingConnection, setTestingConnection] = useState(false);
  const [isTogglingActive, setIsTogglingActive] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const { showToast } = useToast();
  
  useEffect(() => {
    async function loadData() {
      if (!id) return;
      
      try {
        setLoading(true);
        console.log('Loading mail config details for ID:', id);
        
        // Get mail configuration
        const configData = await mailConfigService.getMailConfig(id);
        console.log('Loaded mail config:', configData.mail_config);
        
        if (configData && configData.mail_config) {
          setMailConfig(configData.mail_config);
          
          // Get company details
          if (configData.mail_config.company_id) {
            console.log('Loading company data for ID:', configData.mail_config.company_id);
            
            try {
              const companyData = await fetchAPI<Company>(`/companies/${configData.mail_config.company_id}`);
              console.log('Loaded company data:', companyData);
              
              if (companyData) {
                setCompany(companyData);
              } else {
                console.warn('Company data not found for ID:', configData.mail_config.company_id);
              }
            } catch (companyErr) {
              console.error('Error loading company details:', companyErr);
              // Continue without company data
            }
          }
        } else {
          setError('Failed to load SMTP configuration data');
          showToast('Failed to load SMTP configuration data');
        }
      } catch (err: any) {
        console.error('Error loading mail configuration details:', err);
        setError(`Failed to load SMTP configuration. ${err.message || 'Please try again.'}`);
        showToast('Failed to load SMTP configuration. Please try again.');
      } finally {
        setLoading(false);
      }
    }
    
    loadData();
  }, [id]);
  
  async function handleTestConnection() {
    if (!mailConfig) return;
    
    setTestingConnection(true);
    
    try {
      // Test the configuration
      const result = await mailConfigService.testMailConfig(mailConfig);
      console.log('Test connection result:', result);
      
      if (result.success) {
        showToast('SMTP connection test successful!');
      } else {
        showToast(`SMTP connection test failed: ${result.message}`);
      }
    } catch (err: any) {
      console.error('Error testing SMTP connection:', err);
      showToast(`SMTP connection test failed: ${err.message || 'Please check your settings and try again.'}`);
    } finally {
      setTestingConnection(false);
    }
  }
  
  async function handleToggleActive() {
    if (!mailConfig) return;
    
    setIsTogglingActive(true);
    
    try {
      // Update the configuration status
      console.log('Toggling active status to:', !mailConfig.active);
      const result = await mailConfigService.toggleMailConfigStatus(mailConfig.id, !mailConfig.active);
      console.log('Toggle result:', result);
      
      // Update local state
      setMailConfig(result.mail_config);
      
      // Show success message
      showToast(`Configuration "${mailConfig.sender_name || 'Unnamed'}" ${!mailConfig.active ? 'activated' : 'deactivated'} successfully`);
    } catch (err: any) {
      console.error('Error updating mail configuration:', err);
      showToast(`Failed to update configuration status: ${err.message || 'Please try again.'}`);
    } finally {
      setIsTogglingActive(false);
    }
  }
  
  async function handleDelete() {
    if (!mailConfig) return;
    
    try {
      // Delete the configuration
      console.log('Deleting mail config with ID:', mailConfig.id);
      await mailConfigService.deleteMailConfig(mailConfig.id);
      
      // Show success message
      showToast(`Configuration "${mailConfig.sender_name || 'Unnamed'}" deleted successfully`);
      
      // Redirect back to list
      router.push('/mail-config');
    } catch (err: any) {
      console.error('Error deleting mail configuration:', err);
      showToast(`Failed to delete configuration: ${err.message || 'Please try again.'}`);
    } finally {
      setIsDeleteModalOpen(false);
    }
  }
  
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="w-12 h-12 border-4 border-indigo-500 rounded-full border-t-transparent animate-spin"></div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="p-4 text-sm text-red-700 bg-red-100 rounded-md">
        {error}
        <button 
          onClick={() => router.push('/mail-config')}
          className="ml-4 underline text-red-700 hover:text-red-900"
        >
          Go back
        </button>
      </div>
    );
  }
  
  if (!mailConfig) {
    return (
      <div className="p-4 text-sm text-yellow-700 bg-yellow-100 rounded-md">
        Configuration not found.
        <button 
          onClick={() => router.push('/mail-config')}
          className="ml-4 underline text-yellow-700 hover:text-yellow-900"
        >
          Go back
        </button>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Link
            href="/mail-config"
            className="mr-4 text-gray-500 hover:text-gray-700"
          >
            <i className="fas fa-arrow-left"></i>
          </Link>
          <h1 className="text-2xl font-bold text-gray-900">
            SMTP Configuration Details
          </h1>
        </div>
      </div>
      
      <div className="bg-white shadow-sm rounded-lg p-6">
        <div className="flex justify-between items-start">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">{mailConfig.sender_name || 'Unnamed Configuration'}</h2>
            <p className="text-sm text-gray-500">Company: {company?.name || 'Unknown Company'}</p>
          </div>
          
          <div className="flex space-x-2">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
              ${mailConfig.active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
              {mailConfig.active ? 'Active' : 'Inactive'}
            </span>
          </div>
        </div>
        
        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-sm font-medium text-gray-500">SMTP Server Details</h3>
            <div className="mt-2 space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Host:</span>
                <span className="text-sm text-gray-900">{mailConfig.smtp_host}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Port:</span>
                <span className="text-sm text-gray-900">{mailConfig.smtp_port}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Security:</span>
                <span className="text-sm text-gray-900">{mailConfig.use_ssl ? 'SSL/TLS' : 'None'}</span>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-gray-500">Authentication</h3>
            <div className="mt-2 space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Username:</span>
                <span className="text-sm text-gray-900">{mailConfig.smtp_username}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Password:</span>
                <span className="text-sm text-gray-900">••••••••••</span>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-gray-500">Email Settings</h3>
            <div className="mt-2 space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">From Email:</span>
                <span className="text-sm text-gray-900">{mailConfig.sender_email}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">From Name:</span>
                <span className="text-sm text-gray-900">{mailConfig.sender_name || 'Not set'}</span>
              </div>
            </div>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-gray-500">Dates</h3>
            <div className="mt-2 space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Created:</span>
                <span className="text-sm text-gray-900">
                  {new Date(mailConfig.created_at).toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Last Updated:</span>
                <span className="text-sm text-gray-900">
                  {new Date(mailConfig.updated_at).toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-6 flex justify-end space-x-3">
          <button
            onClick={handleToggleActive}
            disabled={isTogglingActive}
            className={`inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md 
              ${mailConfig.active 
                ? 'text-yellow-700 bg-yellow-100 hover:bg-yellow-200' 
                : 'text-green-700 bg-green-100 hover:bg-green-200'} 
              focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500`}
          >
            {isTogglingActive ? (
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : (
              <i className={`fas ${mailConfig.active ? 'fa-pause' : 'fa-play'} mr-2`}></i>
            )}
            {mailConfig.active ? 'Deactivate' : 'Activate'}
          </button>
          
          <button
            onClick={handleTestConnection}
            disabled={testingConnection}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            {testingConnection ? (
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : (
              <i className="fas fa-vial mr-2"></i>
            )}
            Test Connection
          </button>
          
          <Link
            href={`/mail-config/${mailConfig.id}/edit`} 
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <i className="fas fa-edit mr-2"></i>
            Edit
          </Link>
          
          <button
            onClick={() => setIsDeleteModalOpen(true)}
            className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            <i className="fas fa-trash-alt mr-2"></i>
            Delete
          </button>
        </div>
      </div>
      
      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={isDeleteModalOpen}
        title="Delete SMTP Configuration"
        message={`Are you sure you want to delete "${mailConfig.sender_name || 'this configuration'}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        confirmButtonClass="bg-red-600 hover:bg-red-700"
        showSuccessToast={false}
        onConfirm={handleDelete}
        onCancel={() => setIsDeleteModalOpen(false)}
      />
    </div>
  );
}