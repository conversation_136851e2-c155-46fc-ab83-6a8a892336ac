'use client';

import MailConfigForm from '@/components/mail-config/MailConfigForm';
import Link from 'next/link';

export default function NewMailConfigPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Link
            href="/mail-config"
            className="mr-4 text-gray-500 hover:text-gray-700"
          >
            <i className="fas fa-arrow-left"></i>
          </Link>
          <h1 className="text-2xl font-bold text-gray-900">New SMTP Configuration</h1>
        </div>
      </div>
      
      <MailConfigForm />
    </div>
  );
}