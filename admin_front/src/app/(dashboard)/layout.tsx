'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/auth-context';
import DashboardSidebar from '@/components/layout/dashboard-sidebar';
import DashboardHeader from '@/components/layout/dashboard-header';
import { ToastProvider } from '@/context/toast-context';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [isClient, setIsClient] = useState(false);

  // Ensure we're on the client side
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Check for user in localStorage directly to avoid any API calls
  useEffect(() => {
    if (isClient && !loading) {
      const storedUser = localStorage.getItem('user');
      if (!storedUser) {
        console.log('No user found in localStorage, redirecting to login');
        router.push('/login');
      }
    }
  }, [isClient, loading, router]);

  // Show loading state while authentication is being checked
  if (loading || !isClient) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="w-16 h-16 border-t-4 border-b-4 border-indigo-500 rounded-full animate-spin"></div>
      </div>
    );
  }

  // If no user, show nothing (the useEffect above will handle redirection)
  if (!user) {
    return null;
  }

  // Render the dashboard layout if authenticated
  return (
    <ToastProvider>
      <div className="flex h-screen bg-gray-100">
        <DashboardSidebar />
        <div className="flex flex-col flex-1 overflow-hidden">
          <DashboardHeader user={user} />
           {/* Add this line to include the debugger */}
          <main className="flex-1 overflow-y-auto p-4">
            {children}
          </main>
        </div>
      </div>
    </ToastProvider>
  );
}