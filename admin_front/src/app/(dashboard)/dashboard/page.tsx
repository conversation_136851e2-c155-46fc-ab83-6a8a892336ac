'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/context/auth-context';
import { Mail, Users, FileText, Server } from 'lucide-react';

export default function DashboardPage() {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    companies: 0,
    templates: 0,
    emailsSent: 0,
    activeConfigs: 0
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulated data fetch
    // In a real app, you would fetch this from your API
    const fetchDashboardData = async () => {
      setLoading(true);
      try {
        // Simulated API response
        const data = {
          companies: 12,
          templates: 24,
          emailsSent: 1250,
          activeConfigs: 5
        };
        
        setStats(data);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="w-12 h-12 border-t-4 border-b-4 border-indigo-500 rounded-full animate-spin"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-800">Dashboard</h1>
        <p className="text-gray-600">
          Welcome, {user?.first_name} {user?.last_name}
        </p>
      </div>

      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <StatCard 
          title="Companies" 
          value={stats.companies} 
          icon={<Users className="text-indigo-600" size={24} />} 
        />
        <StatCard 
          title="Templates" 
          value={stats.templates} 
          icon={<FileText className="text-green-600" size={24} />} 
        />
        <StatCard 
          title="Emails Sent" 
          value={stats.emailsSent} 
          icon={<Mail className="text-blue-600" size={24} />} 
        />
        <StatCard 
          title="Active Mail Configs" 
          value={stats.activeConfigs} 
          icon={<Server className="text-purple-600" size={24} />} 
        />
      </div>

      <div className="mt-8 grid grid-cols-1 gap-6 lg:grid-cols-2">
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Recent Activity</h2>
          <div className="space-y-4">
            <ActivityItem 
              title="Template Updated" 
              description="Welcome Email template was updated" 
              time="2 hours ago" 
            />
            <ActivityItem 
              title="Email Campaign Sent" 
              description="150 emails sent to Company A users" 
              time="5 hours ago" 
            />
            <ActivityItem 
              title="New Company Added" 
              description="Company B was added to the system" 
              time="Yesterday" 
            />
            <ActivityItem 
              title="Mail Config Updated" 
              description="SMTP settings were updated for Company C" 
              time="2 days ago" 
            />
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Upcoming Email Campaigns</h2>
          <div className="space-y-4">
            <UpcomingCampaign 
              company="Company D" 
              template="Day 3 Follow-up" 
              scheduledFor="Tomorrow" 
              recipients={75} 
            />
            <UpcomingCampaign 
              company="Company A" 
              template="Weekly Newsletter" 
              scheduledFor="In 2 days" 
              recipients={220} 
            />
            <UpcomingCampaign 
              company="Company E" 
              template="Product Update" 
              scheduledFor="In 5 days" 
              recipients={180} 
            />
          </div>
        </div>
      </div>
    </div>
  );
}

interface StatCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
}

function StatCard({ title, value, icon }: StatCardProps) {
  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-semibold text-gray-900 mt-1">{value}</p>
        </div>
        <div className="bg-gray-100 p-3 rounded-full">{icon}</div>
      </div>
    </div>
  );
}

interface ActivityItemProps {
  title: string;
  description: string;
  time: string;
}

function ActivityItem({ title, description, time }: ActivityItemProps) {
  return (
    <div className="flex items-start">
      <div className="bg-blue-100 rounded-full p-2 mr-3">
        <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
      </div>
      <div className="flex-1">
        <p className="font-medium text-gray-800">{title}</p>
        <p className="text-sm text-gray-600">{description}</p>
      </div>
      <p className="text-xs text-gray-500">{time}</p>
    </div>
  );
}

interface UpcomingCampaignProps {
  company: string;
  template: string;
  scheduledFor: string;
  recipients: number;
}

function UpcomingCampaign({ company, template, scheduledFor, recipients }: UpcomingCampaignProps) {
  return (
    <div className="border border-gray-200 rounded-md p-4">
      <div className="flex justify-between items-center mb-2">
        <p className="font-medium text-gray-800">{company}</p>
        <span className="bg-green-100 text-green-800 text-xs py-1 px-2 rounded-full">
          {scheduledFor}
        </span>
      </div>
      <p className="text-sm text-gray-600">Template: {template}</p>
      <p className="text-sm text-gray-600">Recipients: {recipients}</p>
    </div>
  );
}