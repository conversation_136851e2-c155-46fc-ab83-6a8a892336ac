'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { 
  getTemplateGroups, 
  createTemplateGroup, 
  updateTemplateGroup, 
  deleteTemplateGroup 
} from '@/lib/api/templates';
import { getCompanies } from '@/lib/api/companies';
import type { TemplateGroup } from '@/types/templates';
import type { Company } from '@/types/companies';

// Update the type to include company_ids array
interface TemplateGroupFormData {
  name: string;
  description: string;
  company_ids: string[]; // Changed from company_id to company_ids
  day_number: number;
  is_active?: boolean;
}

export default function TemplateGroupsPage() {
  const [groups, setGroups] = useState<TemplateGroup[]>([]);
  const [companies, setCompanies] = useState<Company[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentGroupId, setCurrentGroupId] = useState<string | null>(null);
  const [formData, setFormData] = useState<TemplateGroupFormData>({
    name: '',
    description: '',
    company_ids: [], // Changed from company_id to company_ids
    day_number: 1,
    is_active: true
  });
  
  const router = useRouter();
  
  useEffect(() => {
    async function loadData() {
      try {
        setLoading(true);
        
        // Fetch companies first
        const companiesData = await getCompanies();
        setCompanies(companiesData);
        
        // Then fetch template groups
        await fetchGroups();
        
      } catch (err) {
        console.error('Error loading initial data:', err);
        setError('Failed to load companies or template groups');
      } finally {
        setLoading(false);
      }
    }
    
    loadData();
  }, []);
  
  function getCompanyNames(companyIds: string[]): string {
    if (!companyIds || companyIds.length === 0) return 'No Companies';
    
    return companyIds.map(id => {
      const company = companies.find(c => c && c.id === id);
      return company ? company.name : 'Unknown';
    }).join(', ');
  }
  
  async function fetchGroups() {
    setError('');
    
    try {
      // Get template groups - getTemplateGroups already handles null response
      const data = await getTemplateGroups();
      
      // Filter out any null or undefined items
      const validGroups = data.filter(group => group !== null && group !== undefined);
      setGroups(validGroups);
    } catch (err) {
      console.error('Error fetching template groups:', err);
      setError('Failed to load template groups');
    }
  }
  
  function handleInputChange(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) {
    const { name, value, type } = e.target;
    
    // Handle number inputs (convert to number)
    if (type === 'number') {
      setFormData(prev => ({ ...prev, [name]: parseInt(value) || 0 }));
    } else if (name === 'company_ids' && e.target instanceof HTMLSelectElement && e.target.multiple) {
      // Handle multi-select for companies
      const select = e.target as HTMLSelectElement;
      const selectedOptions = Array.from(select.selectedOptions).map(option => option.value);
      setFormData(prev => ({ ...prev, company_ids: selectedOptions }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  }
  
  function openCreateModal() {
    setFormData({
      name: '',
      description: '',
      company_ids: companies.length > 0 ? [companies[0].id] : [],
      day_number: 1,
      is_active: true
    });
    setIsEditing(false);
    setCurrentGroupId(null);
    setIsModalOpen(true);
  }
  
  function openEditModal(group: TemplateGroup) {
    setFormData({
      name: group.name,
      description: group.description || '',
      company_ids: group.company_ids || [],
      day_number: group.day_number || 1,
      is_active: group.is_active
    });
    setIsEditing(true);
    setCurrentGroupId(group.id);
    setIsModalOpen(true);
  }
  
  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    
    if (!formData.name || formData.company_ids.length === 0) {
      setError('Name and at least one Company are required');
      return;
    }
    
    try {
      if (isEditing && currentGroupId) {
        // Update existing group
        await updateTemplateGroup(currentGroupId, formData);
      } else {
        // Create new group
        await createTemplateGroup(formData);
      }
      
      // Refresh the groups list
      await fetchGroups();
      
      // Close the modal and reset form
      setIsModalOpen(false);
      setFormData({
        name: '',
        description: '',
        company_ids: [],
        day_number: 1,
        is_active: true
      });
    } catch (err) {
      console.error('Error saving template group:', err);
      setError('Failed to save template group');
    }
  }
  
  async function handleDelete(groupId: string) {
    if (!confirm('Are you sure you want to delete this template group? This will delete all templates in this group.')) {
      return;
    }
    
    try {
      await deleteTemplateGroup(groupId);
      await fetchGroups();
    } catch (err) {
      console.error('Error deleting template group:', err);
      setError('Failed to delete template group');
    }
  }
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Template Groups</h1>
        <button 
          onClick={openCreateModal}
          disabled={companies.length === 0}
          className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Create New Group
        </button>
      </div>
      
      {error && (
        <div className="p-4 text-sm text-red-700 bg-red-100 rounded-md">
          {error}
        </div>
      )}
      
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="w-12 h-12 border-4 border-indigo-500 rounded-full border-t-transparent animate-spin"></div>
        </div>
      ) : companies.length === 0 ? (
        <div className="p-12 text-center bg-white rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900">No companies available</h3>
          <p className="mt-2 text-gray-500">You need to create at least one company before creating template groups.</p>
          <Link
            href="/companies/new"
            className="inline-block px-4 py-2 mt-4 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700"
          >
            Create Company
          </Link>
        </div>
      ) : groups.length === 0 ? (
        <div className="p-12 text-center bg-white rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900">No template groups found</h3>
          <p className="mt-2 text-gray-500">Create your first template group to get started.</p>
          <button 
            onClick={openCreateModal}
            className="px-4 py-2 mt-4 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Create Template Group
          </button>
        </div>
      ) : (
        <div className="overflow-hidden bg-white shadow sm:rounded-md">
          <ul className="divide-y divide-gray-200">
            {groups.map(group => (
              <li key={group.id}>
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex flex-col md:flex-row md:items-center">
                      <p className="text-sm font-medium text-indigo-600 truncate">
                        {group.name}
                      </p>
                      <div className="flex mt-1 space-x-2 text-xs text-gray-500 md:mt-0 md:ml-2">
                        <p>Companies: {getCompanyNames(group.company_ids)}</p>
                        <span>•</span>
                        <p>Day: {group.day_number || 'N/A'}</p>
                        <span>•</span>
                        <p>{group.is_active ? 'Active' : 'Inactive'}</p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Link 
                        href={`/templates?group=${group.id}`}
                        className="px-3 py-1 text-xs text-indigo-700 bg-indigo-100 rounded-md hover:bg-indigo-200"
                      >
                        View Templates
                      </Link>
                      <button 
                        onClick={() => openEditModal(group)}
                        className="px-3 py-1 text-xs text-blue-700 bg-blue-100 rounded-md hover:bg-blue-200"
                      >
                        Edit
                      </button>
                      <button 
                        onClick={() => handleDelete(group.id)}
                        className="px-3 py-1 text-xs text-red-700 bg-red-100 rounded-md hover:bg-red-200"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                  <div className="mt-2 text-sm text-gray-500">
                    {group.description || 'No description provided'}
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
      
      {/* Modal for Creating/Editing Template Groups */}
      {isModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center overflow-x-hidden overflow-y-auto">
          <div className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75"></div>
          
          <div className="relative z-50 w-full max-w-md p-6 mx-auto bg-white rounded-lg shadow-xl">
            <div className="absolute top-0 right-0 pt-4 pr-4">
              <button 
                onClick={() => setIsModalOpen(false)}
                className="text-gray-400 bg-white rounded-md hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                ✕
              </button>
            </div>
            
            <div className="mt-3 text-center sm:mt-0 sm:text-left">
              <h3 className="text-lg font-medium leading-6 text-gray-900">
                {isEditing ? 'Edit Template Group' : 'Create New Template Group'}
              </h3>
              
              <form onSubmit={handleSubmit} className="mt-4 space-y-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                    Group Name *
                  </label>
                  <input 
                    type="text" 
                    id="name" 
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  />
                </div>
                
                <div>
                  <label htmlFor="company_ids" className="block text-sm font-medium text-gray-700">
                    Companies *
                  </label>
                  <select 
                    id="company_ids" 
                    name="company_ids"
                    value={formData.company_ids}
                    onChange={handleInputChange}
                    required
                    multiple
                    className="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    size={Math.min(5, companies.length)}
                  >
                    {companies.map(company => (
                      <option key={company.id} value={company.id}>
                        {company.name}
                      </option>
                    ))}
                  </select>
                  <p className="mt-1 text-xs text-gray-500">Hold Ctrl/Cmd to select multiple companies</p>
                </div>
                
                <div>
                  <label htmlFor="day_number" className="block text-sm font-medium text-gray-700">
                    Day Number *
                  </label>
                  <input 
                    type="number" 
                    id="day_number" 
                    name="day_number"
                    value={formData.day_number || 1}
                    onChange={handleInputChange}
                    min="1"
                    required
                    className="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    The day number in the email sequence (e.g., Day 1, Day 5, etc.)
                  </p>
                </div>
                
                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                    Description
                  </label>
                  <textarea 
                    id="description" 
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={3}
                    className="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  ></textarea>
                </div>
                
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="is_active"
                    name="is_active"
                    checked={formData.is_active}
                    onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                    className="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                  />
                  <label htmlFor="is_active" className="block ml-2 text-sm text-gray-700">
                    Active
                  </label>
                </div>
                
                <div className="flex justify-end mt-5 space-x-3">
                  <button 
                    type="button" 
                    onClick={() => setIsModalOpen(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    Cancel
                  </button>
                  <button 
                    type="submit" 
                    className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    {isEditing ? 'Update' : 'Create'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
