'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { useRouter, useSearchParams } from 'next/navigation';
import { getAllTemplates, getTemplateGroups, deleteTemplate } from '@/lib/api/templates';
import type { Template, TemplateGroup } from '@/types/templates';

export default function TemplatesPage() {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [templateGroups, setTemplateGroups] = useState<TemplateGroup[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeFilter, setActiveFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  
  const router = useRouter();
  const searchParams = useSearchParams();
  
  useEffect(() => {
    // Check if there's a group filter in URL
    const groupParam = searchParams.get('group');
    if (groupParam) {
      setActiveFilter(groupParam);
    }
    
    fetchData();
  }, [searchParams]);

  async function fetchData() {
    setLoading(true);
    setError('');

    try {
      // Fetch template groups
      const groupsData = await getTemplateGroups();
      setTemplateGroups(groupsData);
      
      // Fetch all templates across all groups
      const templatesData = await getAllTemplates();
      setTemplates(templatesData);
    } catch (err) {
      console.error('Error loading templates:', err);
      setError('Failed to load template data');
    } finally {
      setLoading(false);
    }
  }

  async function handleDelete(template: Template) {
    if (!confirm(`Are you sure you want to delete "${template.name}"?`)) {
      return;
    }

    try {
      // Always use the real API
      await deleteTemplate(template.id);
      
      // Refresh the data after deletion
      await fetchData();
    } catch (err) {
      console.error('Error deleting template:', err);
      setError('Failed to delete template');
    }
  }

  // Filter templates based on active filter and search query
  const filteredTemplates = templates.filter(template => {
    // Skip null or undefined values
    if (!template) return false;
    
    const matchesSearch = searchQuery === '' || 
      template.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      template.subject.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesFilter = activeFilter === 'all' || template.group_id === activeFilter;
    
    return matchesSearch && matchesFilter;
  });

  function getGroupName(groupId: string): string {
    // Handle null, undefined, or empty groupId
    if (!groupId) return 'Unknown Group';
    
    const group = templateGroups.find(g => g && g.id === groupId);
    return group ? group.name : 'Unknown Group';
  }

  function getGroupDayNumber(groupId: string): number | null {
    if (!groupId) return null;
    
    const group = templateGroups.find(g => g && g.id === groupId);
    return group ? group.day_number : null;
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Email Templates</h1>
        <div className="flex space-x-3">
          <Link 
            href="/templates/groups"
            className="inline-flex items-center px-4 py-2 text-sm font-medium text-indigo-600 bg-white border border-indigo-300 rounded-md shadow-sm hover:bg-indigo-50"
          >
            Manage Groups
          </Link>
          <Link 
            href="/templates/create" 
            className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700"
          >
            New Template
          </Link>
        </div>
      </div>

      {error && (
        <div className="p-4 text-sm text-red-700 bg-red-100 rounded-md">
          {error}
        </div>
      )}

      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="w-12 h-12 border-4 border-indigo-500 rounded-full border-t-transparent animate-spin"></div>
        </div>
      ) : (
        <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
          <div className="md:w-1/4">
            <div className="sticky p-4 bg-white rounded-lg shadow top-4">
              <h3 className="text-lg font-medium text-gray-900">Filter By Group</h3>
              <ul className="mt-4 space-y-2">
                <li>
                  <button 
                    className={`flex items-center w-full px-3 py-2 text-left rounded-md ${activeFilter === 'all' ? 'bg-indigo-100 text-indigo-700' : 'text-gray-700 hover:bg-gray-100'}`}
                    onClick={() => setActiveFilter('all')}
                  >
                    All Templates
                  </button>
                </li>
                {templateGroups.map(group => (
                  <li key={group.id}>
                    <button 
                      className={`flex items-center w-full px-3 py-2 text-left rounded-md ${activeFilter === group.id ? 'bg-indigo-100 text-indigo-700' : 'text-gray-700 hover:bg-gray-100'}`}
                      onClick={() => setActiveFilter(group.id)}
                    >
                      {group.name} (Day {group.day_number})
                    </button>
                  </li>
                ))}
              </ul>
            </div>
          </div>

          <div className="md:w-3/4">
            <div className="p-4 mb-4 bg-white rounded-lg shadow">
              <div className="relative">
                <input 
                  type="text" 
                  placeholder="Search templates..." 
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full px-4 py-2 pl-10 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                />
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  🔍
                </div>
              </div>
            </div>

            {filteredTemplates.length === 0 ? (
              <div className="flex flex-col items-center justify-center p-12 bg-white rounded-lg shadow">
                <div className="p-4 text-indigo-500 bg-indigo-100 rounded-full">
                  🔍
                </div>
                <h3 className="mt-4 text-lg font-medium text-gray-900">No templates found</h3>
                <p className="mt-1 text-gray-500">
                  {searchQuery ? 'Try adjusting your search or filter.' : 'No templates match the current filter.'}
                </p>
                {activeFilter !== 'all' && (
                  <button 
                    onClick={() => setActiveFilter('all')}
                    className="px-4 py-2 mt-4 text-sm font-medium text-indigo-600 bg-white border border-indigo-300 rounded-md shadow-sm hover:bg-indigo-50"
                  >
                    Show All Templates
                  </button>
                )}
              </div>
            ) : (
              <div className="overflow-hidden bg-white rounded-lg shadow">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Template Name</th>
                      <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Group</th>
                      <th className="px-6 py-3 text-xs font-medium tracking-wider text-center text-gray-500 uppercase">Week</th>
                      <th className="px-6 py-3 text-xs font-medium tracking-wider text-center text-gray-500 uppercase">Day</th>
                      <th className="px-6 py-3 text-xs font-medium tracking-wider text-center text-gray-500 uppercase">Type</th>
                      <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredTemplates.map(template => (
                      <tr key={template.id} className={!template.is_active ? "bg-gray-50" : ""}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="text-sm font-medium text-gray-900">{template.name}</div>
                            {!template.is_active && (
                              <span className="ml-2 px-2 py-0.5 text-xs text-gray-500 bg-gray-100 rounded-full">Inactive</span>
                            )}
                          </div>
                          <div className="text-sm text-gray-500">{template.subject}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{getGroupName(template.group_id)}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <div className="px-2 py-1 inline-flex text-xs text-blue-800 bg-blue-100 rounded-full">
                            Week {template.week_number}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <div className="px-2 py-1 inline-flex text-xs text-indigo-800 bg-indigo-100 rounded-full">
                            Day {getGroupDayNumber(template.group_id)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <div className="px-2 py-1 inline-flex text-xs text-gray-800 bg-gray-100 rounded-full">
                            {template.type || 'Default'}
                          </div>
                        </td>
                        <td className="px-6 py-4 text-sm font-medium whitespace-nowrap">
                          <div className="flex space-x-3">
                            <Link 
                              href={`/templates/${template.id}`}
                              className="text-indigo-600 hover:text-indigo-900"
                            >
                              👁️
                            </Link>
                            <Link 
                              href={`/templates/${template.id}/edit`}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              ✏️
                            </Link>
                            <button 
                              className="text-red-600 hover:text-red-900"
                              onClick={() => handleDelete(template)}
                            >
                              🗑️
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}