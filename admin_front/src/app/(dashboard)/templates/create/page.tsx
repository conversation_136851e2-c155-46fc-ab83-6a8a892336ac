'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import TemplateEditor from '@/components/templates/template-editor';
import { getTemplateGroups, createTemplate } from '@/lib/api/templates';
import type { TemplateGroup, TemplateFormData } from '@/types/templates';

export default function CreateTemplatePage() {
  const [templateGroups, setTemplateGroups] = useState<TemplateGroup[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();
  
  const [formData, setFormData] = useState<TemplateFormData>({
    name: '',
    subject: '',
    content: '',
    group_id: '',
    week_number: 0, // This should be initialized to 0, not empty string
    type: 'default',
    is_active: true
  });
  
  useEffect(() => {
    async function loadGroups() {
      try {
        setLoading(true);
        
        // Always use the real API for template groups
        const groups = await getTemplateGroups();
        setTemplateGroups(groups);
        
        if (groups.length > 0) {
          setFormData(prev => ({ ...prev, group_id: groups[0].id }));
        }
      } catch (err) {
        console.error('Error loading template groups:', err);
        setError('Failed to load template groups');
      } finally {
        setLoading(false);
      }
    }
    
    loadGroups();
  }, []);
  
  function handleInputChange(e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) {
    const { name, value, type } = e.target;
    
    // Special handling for number inputs
    if (type === 'number') {
      setFormData({
        ...formData,
        [name]: value === '' ? 0 : parseInt(value, 10)
      });
      return;
    }
    
    // Handle checkbox inputs
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData({
        ...formData,
        [name]: checked
      });
      return;
    }
    
    // Default handling for other input types
    setFormData({
      ...formData,
      [name]: value
    });
  }
  
  function handleContentChange(content: string) {
    setFormData(prev => ({ ...prev, content }));
  }
  
  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setSaving(true);
    setError('');
    
    try {
      // Validate form
      if (!formData.name || !formData.subject || !formData.content || !formData.group_id) {
        throw new Error('Please fill in all required fields');
      }
      
      // Always use the real API
      await createTemplate(formData);
      
      // Redirect to templates list
      router.push('/templates');
    } catch (err) {
      console.error('Error saving template:', err);
      setError(err instanceof Error ? err.message : 'Failed to save template');
    } finally {
      setSaving(false);
    }
  }
  
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="w-12 h-12 border-4 border-indigo-500 rounded-full border-t-transparent animate-spin"></div>
      </div>
    );
  }
  
  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Create Email Template</h1>
        <Link
          href="/templates"
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50"
        >
          Cancel
        </Link>
      </div>
      
      {error && (
        <div className="p-4 mb-6 text-sm text-red-700 bg-red-100 rounded-md">
          {error}
        </div>
      )}
      
      {templateGroups.length === 0 ? (
        <div className="p-8 text-center bg-white rounded-lg shadow">
          <h2 className="text-lg font-medium text-gray-900">No Template Groups Available</h2>
          <p className="mt-2 text-gray-500">You need to create a template group before creating templates.</p>
          <Link
            href="/templates/groups"
            className="inline-block px-4 py-2 mt-4 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700"
          >
            Create Template Group
          </Link>
        </div>
      ) : (
        <div className="p-6 bg-white rounded-lg shadow">
          <form onSubmit={handleSubmit}>
            <div className="space-y-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Template Name *
                </label>
                <input 
                  type="text" 
                  id="name" 
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  disabled={saving}
                  className="block w-full mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                />
              </div>
              
              <div>
                <label htmlFor="group_id" className="block text-sm font-medium text-gray-700">
                  Template Group *
                </label>
                <select 
                  id="group_id" 
                  name="group_id"
                  value={formData.group_id}
                  onChange={handleInputChange}
                  required
                  disabled={saving}
                  className="block w-full mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                  <option value="">Select a group</option>
                  {templateGroups.map(group => (
                    <option key={group.id} value={group.id}>{group.name} (Day {group.day_number})</option>
                  ))}
                </select>
              </div>
              
              <div>
                <label htmlFor="week_number" className="block text-sm font-medium text-gray-700">
                  Week Number *
                </label>
                <input 
                  type="number" 
                  id="week_number" 
                  name="week_number"
                  value={formData.week_number}
                  onChange={handleInputChange}
                  min="0"
                  required
                  disabled={saving}
                  className="block w-full mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                />
                <p className="mt-1 text-sm text-gray-500">
                  Which week in the sequence this email is targeted for (e.g., Week 1, Week 5, etc.). Use 0 for default templates that can be used for any week.
                </p>
              </div>
              
              <div>
                <label htmlFor="type" className="block text-sm font-medium text-gray-700">
                  Template Type
                </label>
                <select 
                  id="type" 
                  name="type"
                  value={formData.type || 'default'}
                  onChange={handleInputChange}
                  disabled={saving}
                  className="block w-full mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                >
                  <option value="default">Default</option>
                  <option value="onboarding">Onboarding</option>
                  <option value="reminder">Reminder</option>
                  <option value="feedback">Feedback</option>
                </select>
                <p className="mt-1 text-sm text-gray-500">
                  Template Type
                </p>
              </div>
              
              <div>
                <label htmlFor="subject" className="block text-sm font-medium text-gray-700">
                  Email Subject *
                </label>
                <input 
                  type="text" 
                  id="subject" 
                  name="subject"
                  value={formData.subject}
                  onChange={handleInputChange}
                  required
                  disabled={saving}
                  className="block w-full mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                />
              </div>
              
              <div>
                <label htmlFor="content" className="block text-sm font-medium text-gray-700">
                  Email Content *
                </label>
                <TemplateEditor 
                  initialValue={formData.content} 
                  onChange={handleContentChange} 
                  disabled={saving}
                />
                <p className="mt-1 text-sm text-gray-500">
                  Use the editor tools to format text and insert variables.
                </p>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_active"
                  name="is_active"
                  checked={formData.is_active}
                  onChange={(e) => setFormData(prev => ({ ...prev, is_active: e.target.checked }))}
                  className="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                />
                <label htmlFor="is_active" className="block ml-2 text-sm text-gray-700">
                  Active
                </label>
              </div>
              
              <div className="flex justify-end space-x-3 pt-4">
                <Link
                  href="/templates"
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Cancel
                </Link>
                <button 
                  type="submit" 
                  disabled={saving}
                  className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                >
                  {saving ? 'Saving...' : 'Create Template'}
                </button>
              </div>
            </div>
          </form>
        </div>
      )}
    </div>
  );
}
