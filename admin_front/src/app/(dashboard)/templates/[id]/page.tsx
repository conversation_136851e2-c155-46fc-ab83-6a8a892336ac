'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { getTemplateById, getTemplateGroups, renderTemplate, deleteTemplate } from '@/lib/api/templates';
import type { Template, TemplateGroup } from '@/types/templates';
import { format } from 'date-fns';

interface PageProps {
  params: {
    id: string;
  };
}

export default function TemplateDetailPage({ params }: PageProps) {
  const [template, setTemplate] = useState<Template | null>(null);
  const [templateGroup, setTemplateGroup] = useState<TemplateGroup | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [previewContent, setPreviewContent] = useState<string>('');
  const [showPreview, setShowPreview] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const router = useRouter();
  
  useEffect(() => {
    async function loadTemplate() {
      try {
        setLoading(true);
        
        // Get the template data
        const templateData = await getTemplateById(params.id);
        
        // If no template found, set error and return
        if (!templateData) {
          setError('Template not found');
          setLoading(false);
          return;
        }
        
        setTemplate(templateData);
        
        // Get template groups
        const groupsData = await getTemplateGroups();
        
        // Find the matching group
        const group = groupsData.find(g => g && g.id === templateData.group_id) || null;
        setTemplateGroup(group);
      } catch (err) {
        console.error('Error loading template:', err);
        setError('Failed to load template');
      } finally {
        setLoading(false);
      }
    }
    
    loadTemplate();
  }, [params.id]);

  async function handlePreview() {
    if (!template) {
      setError('Cannot preview: template not found');
      return;
    }
    
    try {
      setShowPreview(true);
      
      // Sample test data for preview
      const testData = {
        name: 'John Doe',
        email: '<EMAIL>',
        company: 'Acme Inc.',
        score: '85'
      };
      
      // Try to use the real API first
      try {
        const previewHtml = await renderTemplate(params.id, testData);
        
        // Check if we got a valid response
        if (previewHtml) {
          setPreviewContent(previewHtml);
          return;
        }
      } catch (previewError) {
        console.error('Preview API error, falling back to client-side preview:', previewError);
      }
      
      // Fallback: client-side preview
      let preview = template?.content || template?.body || '';
      
      Object.entries(testData).forEach(([key, value]) => {
        const regex = new RegExp(`{{${key}}}`, 'g');
        preview = preview.replace(regex, value);
      });
      
      setPreviewContent(preview);
    } catch (err) {
      console.error('Error generating preview:', err);
      setPreviewContent('<p>Error generating preview</p>');
    }
  }
  
  async function handleDelete() {
    if (!template) return;
    
    if (!confirm(`Are you sure you want to delete "${template.name}"? This action cannot be undone.`)) {
      return;
    }
    
    try {
      setIsDeleting(true);
      
      await deleteTemplate(params.id);
      
      // Redirect to templates list
      router.push('/templates');
    } catch (err) {
      console.error('Error deleting template:', err);
      setError('Failed to delete template');
      setIsDeleting(false);
    }
  }
  
  // Format date for display
  function formatDate(dateString: string): string {
    if (!dateString) {
      return 'Unknown';
    }
    
    try {
      return format(new Date(dateString), 'MMMM d, yyyy h:mm a');
    } catch (e) {
      return 'Invalid date';
    }
  }
  
  // Get the template content whether it's in 'content' or 'body' field
  function getTemplateContent(): string {
    if (!template) return '';
    return template.content || template.body || '';
  }
  
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="w-12 h-12 border-4 border-indigo-500 rounded-full border-t-transparent animate-spin"></div>
      </div>
    );
  }
  
  if (error || !template) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="p-6 text-center bg-white rounded-lg shadow">
          <h2 className="text-xl font-medium text-red-600">Error</h2>
          <p className="mt-2 text-gray-500">{error || 'Template not found'}</p>
          <Link
            href="/templates"
            className="inline-block px-4 py-2 mt-4 text-sm font-medium text-indigo-600 bg-white border border-indigo-300 rounded-md shadow-sm hover:bg-indigo-50"
          >
            Back to Templates
          </Link>
        </div>
      </div>
    );
  }
  
  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-900">{template.name}</h1>
        <div className="flex space-x-3">
          <Link
            href="/templates"
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50"
          >
            Back
          </Link>
          <Link
            href={`/templates/${template.id}/edit`}
            className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700"
          >
            Edit Template
          </Link>
        </div>
      </div>
      
      {error && (
        <div className="p-4 mb-6 text-sm text-red-700 bg-red-100 rounded-md">
          {error}
        </div>
      )}
      
      <div className="p-6 mb-6 bg-white rounded-lg shadow">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          <div>
            <h3 className="text-sm font-medium text-gray-500">Template Group</h3>
            <p className="mt-1 text-base text-gray-900">{templateGroup?.name || 'Unknown Group'}</p>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-gray-500">Day Number (Group)</h3>
            <p className="mt-1 text-base text-gray-900">Day {templateGroup?.day_number || 'N/A'}</p>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-gray-500">Week Number</h3>
            <p className="mt-1 text-base text-gray-900">Week {template.week_number}</p>
          </div>
          
          <div className="md:col-span-2">
            <h3 className="text-sm font-medium text-gray-500">Email Subject</h3>
            <p className="mt-1 text-base text-gray-900">{template.subject}</p>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-gray-500">Template Type</h3>
            <p className="mt-1 text-base text-gray-900">{template.type || 'Default'}</p>
          </div>
          
          <div className="md:col-span-3">
            <h3 className="text-sm font-medium text-gray-500">Status</h3>
            <p className="mt-1 text-base text-gray-900">
              {template.is_active ? 
                <span className="px-2 py-1 text-sm text-green-800 bg-green-100 rounded-full">Active</span> :
                <span className="px-2 py-1 text-sm text-red-800 bg-red-100 rounded-full">Inactive</span>
              }
            </p>
          </div>
        </div>
      </div>
      
      <div className="p-6 mb-6 bg-white rounded-lg shadow">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-medium text-gray-900">Email Content</h2>
          <button
            onClick={handlePreview}
            className="px-3 py-1 text-sm text-indigo-600 bg-indigo-100 rounded-md hover:bg-indigo-200"
          >
            Preview With Test Data
          </button>
        </div>
        
        {showPreview ? (
          <div>
            <div className="p-4 mb-4 bg-gray-100 rounded-md">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-sm font-medium text-gray-700">Preview (with test data)</h3>
                <button
                  onClick={() => setShowPreview(false)}
                  className="text-sm text-gray-500 hover:text-gray-700"
                >
                  Show Original
                </button>
              </div>
              <div 
                className="p-4 bg-white border border-gray-200 rounded-md"
                dangerouslySetInnerHTML={{ __html: previewContent }}
              ></div>
            </div>
          </div>
        ) : (
          <div 
            className="p-4 bg-gray-50 border border-gray-200 rounded-md"
            dangerouslySetInnerHTML={{ __html: getTemplateContent() }}
          ></div>
        )}
      </div>
      
      {template.default_data && Object.keys(template.default_data).length > 0 && (
        <div className="p-6 mb-6 bg-white rounded-lg shadow">
          <h2 className="mb-4 text-lg font-medium text-gray-900">Default Template Variables</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-2 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Variable</th>
                  <th className="px-4 py-2 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Default Value</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {Object.entries(template.default_data).map(([key, value]) => (
                  <tr key={key}>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{key}</td>
                    <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{String(value)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
      
      {template.required_fields && template.required_fields.length > 0 && (
        <div className="p-6 mb-6 bg-white rounded-lg shadow">
          <h2 className="mb-4 text-lg font-medium text-gray-900">Required Fields</h2>
          <div className="flex flex-wrap gap-2">
            {template.required_fields.map(field => (
              <span key={field} className="px-2 py-1 text-sm bg-gray-100 rounded-md">{field}</span>
            ))}
          </div>
        </div>
      )}
      
      <div className="p-6 mb-6 bg-white rounded-lg shadow">
        <h2 className="mb-4 text-lg font-medium text-gray-900">Template Information</h2>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          <div>
            <h3 className="text-sm font-medium text-gray-500">Created</h3>
            <p className="mt-1 text-sm text-gray-900">
              {formatDate(template.created_at)}
            </p>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-gray-500">Last Updated</h3>
            <p className="mt-1 text-sm text-gray-900">
              {formatDate(template.updated_at)}
            </p>
          </div>
        </div>
      </div>
      
      <div className="p-6 bg-white rounded-lg shadow">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Danger Zone</h2>
        
        <div className="border border-red-200 rounded-md p-4">
          <h3 className="text-sm font-medium text-red-800">Delete this template</h3>
          <p className="mt-1 text-sm text-gray-500">
            Once you delete a template, it cannot be recovered. This action cannot be undone.
          </p>
          <button
            onClick={handleDelete}
            disabled={isDeleting}
            className="mt-3 px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
          >
            {isDeleting ? 'Deleting...' : 'Delete Template'}
          </button>
        </div>
      </div>
    </div>
  );
}