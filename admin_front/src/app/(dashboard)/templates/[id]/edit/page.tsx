'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import TemplateEditor from '@/components/templates/template-editor';
import { getTemplateById, getTemplateGroups, updateTemplate } from '@/lib/api/templates';
import type { Template, TemplateGroup, TemplateFormData } from '@/types/templates';
import { useToast } from '@/context/toast-context';

interface PageProps {
  params: {
    id: string;
  };
}

export default function EditTemplatePage({ params }: PageProps) {
  const [template, setTemplate] = useState<Template | null>(null);
  const [templateGroups, setTemplateGroups] = useState<TemplateGroup[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const router = useRouter();
  const { showToast } = useToast();
  
  const [formData, setFormData] = useState<TemplateFormData>({
    name: '',
    subject: '',
    content: '',
    group_id: '',
    week_number: 1,
    type: 'default',
    is_active: true,
    default_data: {},
    required_fields: []
  });
  
  useEffect(() => {
    async function loadData() {
      try {
        setLoading(true);
        
        // Load template groups
        const groups = await getTemplateGroups();
        setTemplateGroups(groups);
        
        // Load template data
        const templateData = await getTemplateById(params.id);
        
        if (!templateData) {
          setError('Template not found');
          setLoading(false);
          return;
        }
        
        setTemplate(templateData);
        
        // Set form data from template
        setFormData({
          name: templateData.name,
          subject: templateData.subject,
          // Use content if available, otherwise use body, otherwise empty string
          content: templateData.content || templateData.body || '',
          group_id: templateData.group_id,
          week_number: templateData.week_number,
          type: templateData.type || 'default',
          is_active: templateData.is_active !== undefined ? templateData.is_active : true,
          default_data: templateData.default_data || {},
          required_fields: templateData.required_fields || []
        });
      } catch (err) {
        console.error('Error loading template data:', err);
        setError('Failed to load template data');
      } finally {
        setLoading(false);
      }
    }
    
    loadData();
  }, [params.id]);
  
  function handleInputChange(e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) {
    const { name, value, type } = e.target;
    
    // Handle number inputs
    if (type === 'number') {
      setFormData(prev => ({ ...prev, [name]: parseInt(value) || 0 }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  }
  
  function handleCheckboxChange(e: React.ChangeEvent<HTMLInputElement>) {
    const { name, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: checked }));
  }
  
  function handleContentChange(content: string) {
    setFormData(prev => ({ ...prev, content }));
  }
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (saving) return;
    
    try {
      setSaving(true);
      setError('');
      
      console.log('Submitting form data:', formData); // Add logging
      
      // Ensure week_number is sent as a number, not a string
      const dataToSubmit = {
        ...formData,
        week_number: Number(formData.week_number),
        // Ensure we're sending the content in the correct field
        body: formData.content, // Backend expects 'body' field
      };
      
      await updateTemplate(params.id, dataToSubmit);
      
      // Show success message using the toast context
      showToast('Template updated successfully', 'success');
      
      // Redirect back to template details
      router.push(`/templates/${params.id}`);
    } catch (err) {
      console.error('Error updating template:', err);
      setError('Failed to update template. Please try again.');
      showToast('Failed to update template', 'error');
    } finally {
      setSaving(false);
    }
  };
  
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="w-12 h-12 border-4 border-indigo-500 rounded-full border-t-transparent animate-spin"></div>
      </div>
    );
  }
  
  if (!template) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="p-6 text-center bg-white rounded-lg shadow">
          <h2 className="text-xl font-medium text-red-600">Error</h2>
          <p className="mt-2 text-gray-500">{error || 'Template not found'}</p>
          <Link
            href="/templates"
            className="inline-block px-4 py-2 mt-4 text-sm font-medium text-indigo-600 bg-white border border-indigo-300 rounded-md shadow-sm hover:bg-indigo-50"
          >
            Back to Templates
          </Link>
        </div>
      </div>
    );
  }
  
  return (
    <div className="max-w-4xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Edit Template: {template.name}</h1>
        <Link
          href={`/templates/${params.id}`}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50"
        >
          Cancel
        </Link>
      </div>
      
      {error && (
        <div className="p-4 mb-6 text-sm text-red-700 bg-red-100 rounded-md">
          {error}
        </div>
      )}
      
      <div className="p-6 bg-white rounded-lg shadow">
        <form onSubmit={handleSubmit}>
          <div className="space-y-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Template Name *
              </label>
              <input 
                type="text" 
                id="name" 
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                disabled={saving}
                className="block w-full mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>
            
            <div>
              <label htmlFor="group_id" className="block text-sm font-medium text-gray-700">
                Template Group *
              </label>
              <select 
                id="group_id" 
                name="group_id"
                value={formData.group_id}
                onChange={handleInputChange}
                required
                disabled={saving}
                className="block w-full mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
                <option value="">Select a group</option>
                {templateGroups.map(group => (
                  <option key={group.id} value={group.id}>{group.name} (Day {group.day_number})</option>
                ))}
              </select>
            </div>
            
            <div>
              <label htmlFor="week_number" className="block text-sm font-medium text-gray-700">
                Week Number *
              </label>
              <input 
                type="number" 
                id="week_number" 
                name="week_number"
                value={formData.week_number}
                onChange={handleInputChange}
                min="0"
                required
                disabled={saving}
                className="block w-full mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
              <p className="mt-1 text-sm text-gray-500">
                Which week in the sequence this email is targeted for. Use 0 for default templates that can be used for any week.
              </p>
            </div>
            
            <div>
              <label htmlFor="type" className="block text-sm font-medium text-gray-700">
                Template Type
              </label>
              <select 
                id="type" 
                name="type"
                value={formData.type || 'default'}
                onChange={handleInputChange}
                disabled={saving}
                className="block w-full mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
                <option value="default">Default</option>
                <option value="onboarding">Onboarding</option>
                <option value="reminder">Reminder</option>
                <option value="feedback">Feedback</option>
              </select>
              <p className="mt-1 text-sm text-gray-500">
                Template type 
              </p>
            </div>
            
            <div>
              <label htmlFor="subject" className="block text-sm font-medium text-gray-700">
                Email Subject *
              </label>
              <input 
                type="text" 
                id="subject" 
                name="subject"
                value={formData.subject}
                onChange={handleInputChange}
                required
                disabled={saving}
                className="block w-full mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>
            
            <div>
              <label htmlFor="content" className="block text-sm font-medium text-gray-700">
                Email Content *
              </label>
              <TemplateEditor 
                initialValue={formData.content} 
                onChange={handleContentChange} 
                disabled={saving}
              />
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_active"
                name="is_active"
                checked={formData.is_active}
                onChange={handleCheckboxChange}
                className="w-4 h-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
              />
              <label htmlFor="is_active" className="block ml-2 text-sm text-gray-700">
                Active
              </label>
            </div>
            
            <div className="flex justify-end space-x-3 pt-4">
              <Link
                href={`/templates/${params.id}`}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </Link>
              <button 
                type="submit" 
                disabled={saving}
                className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {saving ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
