// src/app/(dashboard)/email-sender/page.tsx
'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/context/auth-context';
import ExcelUploader from '@/components/email-sender/excel-uploader';
import { selectTemplatesForUsers, sendTemplateEmails } from "@/lib/api/scraper";
import { getCompanies } from '@/lib/api/companies';
import type { Company } from '@/types/companies';
import { getTemplateById, getTemplateGroups, getTemplates } from '@/lib/api/templates';

// Define the response type
interface EmailSendingResult {
  success_count: number;
  error_count: number;
  errors: Record<string, string>;
}

interface UserTemplateData {
  username: string;
  email: string;
  company_name: string;
  has_delay: boolean;
  week_number: number;
  score_count: number;
  expected: number;
  template_id: string;
  template_name: string;
  subject: string;
  body: string;
  data: any;
  needs_alternative: boolean;
  sendAnyway?: boolean; // Make sure this is defined
}

export default function EmailSenderPage() {
  const [userData, setUserData] = useState<UserTemplateData[]>([]);
  const [sending, setSending] = useState(false);
  const [complete, setComplete] = useState(false);
  const [emailCount, setEmailCount] = useState(0);
  const [result, setResult] = useState<any | null>(null);
  const [step, setStep] = useState<'upload' | 'review' | 'complete'>('upload');
  const [sendToAll, setSendToAll] = useState(false);
  const [companies, setCompanies] = useState<Company[]>([]);
  const [previewTemplate, setPreviewTemplate] = useState<UserTemplateData | null>(null);
  const [showPreview, setShowPreview] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectingTemplateForIndex, setSelectingTemplateForIndex] = useState<number | null>(null);
  const [availableTemplates, setAvailableTemplates] = useState<Array<{id: string, name: string}>>([]);
  const [loadingTemplates, setLoadingTemplates] = useState(false);
  
  useEffect(() => {
    async function loadCompanies() {
      try {
        const companiesData = await getCompanies();
        setCompanies(companiesData);
      } catch (error) {
        console.error('Error loading companies:', error);
      }
    }
    
    loadCompanies();
  }, []);
  
  async function handleFileUploaded(data: any[]) {
    // Transform the data to match the required format
    const transformedData = data.map(item => ({
      username: item.username,
      email: item.email,
      company_name: item.company, // Map 'company' to 'company_name'
      needs_alternative: false
    }));
    
    setEmailCount(transformedData.length);
    
    // Select templates for users
    try {
      setSending(true);
      const response = await selectTemplatesForUsers(transformedData);
      
      // Initialize sendAnyway to false for all users
      // Delayed users will be automatically included in the send logic
      const usersWithSendAnyway = response.users.map((user: UserTemplateData) => ({
        ...user,
        // Set sendAnyway to false for all users by default
        sendAnyway: false
      }));
      
      console.log('Users with sendAnyway initialized:', usersWithSendAnyway);
      
      setUserData(usersWithSendAnyway);
      setStep('review');
    } catch (error) {
      console.error('Error selecting templates:', error);
    } finally {
      setSending(false);
    }
  }
  
  async function handleSendEmails() {
    setSending(true);
    setError(null); // Clear any previous errors
    
    try {
      // Debug: Log the current state of userData
      console.log('Current userData state:', userData);
      
      // Filter users to send emails to
      const usersToSend = userData.filter(user => {
        // For delayed users, always include them
        if (user.has_delay) {
          console.log(`Including delayed user: ${user.username}`);
          return true;
        }
        
        // For non-delayed users:
        // Include if they have a template AND are checked (sendAnyway = true)
        const shouldInclude = Boolean(user.sendAnyway) && Boolean(user.template_id);
        
        console.log(`User ${user.username}: sendAnyway=${user.sendAnyway}, has template=${Boolean(user.template_id)}, include=${shouldInclude}`);
        
        return shouldInclude;
      });
      
      console.log('Filtered users to send:', usersToSend.map(u => u.username));
      
      // Check if we have users to send
      if (usersToSend.length === 0) {
        setError('No users selected for sending emails. Please select users to send to.');
        setSending(false);
        return;
      }
      
      // Format users for the API call
      const formattedUsers = usersToSend.map(user => {
        const company = companies.find(c => c.name === user.company_name);
        const companyId = company ? company.id : '';
        const companyUrl = company ? company.url : '';
        
        return {
          email: user.email,
          template_id: user.template_id,
          company_id: companyId,
          data: {
            ...user.data,
            username: user.username,
            subject: user.subject,
            body: user.body,
            company_name: user.company_name,
            company_url: companyUrl, // Add company URL to the data
            week_number: user.week_number,
            score_count: user.score_count,
            expected: user.expected
          }
        };
      });
      
      console.log('API payload:', formattedUsers);
      
      // Send the emails
      const response = await sendTemplateEmails(formattedUsers, sendToAll);
      console.log('API response:', response);
      
      setResult(response);
      setStep('complete');
      setComplete(true);
    } catch (error) {
      console.error('Error sending emails:', error);
      setError(`Failed to send emails: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setSending(false);
    }
  }

  function resetProcess() {
    setUserData([]);
    setComplete(false);
    setEmailCount(0);
    setResult(null);
    setStep('upload');
    setSendToAll(false);
    setError(null);
    setSelectingTemplateForIndex(null);
    setAvailableTemplates([]);
  }
  
  function handleTemplateChange(index: number, field: 'subject' | 'body', value: string) {
    const updatedUserData = [...userData];
    updatedUserData[index][field] = value;
    updatedUserData[index].data[field] = value;
    setUserData(updatedUserData);
  }
  
  async function handleSelectTemplate(userIndex: number) {
    setLoadingTemplates(true);
    setSelectingTemplateForIndex(userIndex);
    
    try {
      const user = userData[userIndex];
      const companyId = companies.find(c => c.name === user.company_name)?.id;
      
      if (!companyId) {
        throw new Error('Company ID not found');
      }
      
      // Get template groups for this company
      const templateGroups = await getTemplateGroups(companyId);
      
      // The issue is here - templateGroups doesn't have a templates property
      // We need to fetch templates for each group
      let allTemplates: Array<{id: string, name: string}> = [];
      
      // For each group, fetch its templates
      for (const group of templateGroups) {
        try {
          const groupTemplates = await getTemplates(group.id);
          const mappedTemplates = groupTemplates.map(t => ({ 
            id: t.id, 
            name: t.name 
          }));
          allTemplates = [...allTemplates, ...mappedTemplates];
        } catch (err) {
          console.error(`Error fetching templates for group ${group.id}:`, err);
        }
      }
      
      setAvailableTemplates(allTemplates);
    } catch (error) {
      console.error('Error loading templates:', error);
      setError(`Failed to load templates: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoadingTemplates(false);
    }
  }
  
  async function applySelectedTemplate(templateId: string, templateName: string) {
    if (selectingTemplateForIndex === null) return;
    
    try {
      // Fetch the template content
      const templateData = await getTemplateById(templateId);
      
      if (!templateData) {
        throw new Error('Failed to fetch template content');
      }
      
      console.log('Fetched template data:', templateData);
      
      // Ensure we have subject and body content
      const subject = templateData.subject || '';
      const body = templateData.content || templateData.body || '';
      const defaultData = templateData.default_data || {};
      
      // Update the user with the selected template
      const updatedUsers = [...userData];
      updatedUsers[selectingTemplateForIndex] = {
        ...updatedUsers[selectingTemplateForIndex],
        template_id: templateId,
        template_name: templateName,
        subject: subject,
        body: body,
        data: defaultData,
        needs_alternative: false
      };
      
      console.log('Updated user with template:', updatedUsers[selectingTemplateForIndex]);
      
      // Immediately set the preview data to trigger a preview render
      const previewData = updatedUsers[selectingTemplateForIndex];
      setPreviewTemplate(previewData);
      
      setUserData(updatedUsers);
      setSelectingTemplateForIndex(null);
    } catch (error) {
      console.error('Error applying template:', error);
      setError(`Failed to apply template: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
  
  function cancelTemplateSelection() {
    setSelectingTemplateForIndex(null);
    setAvailableTemplates([]);
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Email Sender</h1>
      </div>
      
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="p-6">
          {step === 'upload' && (
            <ExcelUploader 
              onFileUploaded={handleFileUploaded} 
              buttonText="Select Templates" 
              isSending={sending}
            />
          )}
          
          {step === 'review' && (
            <div className="space-y-6">
              {error && (
                <div className="p-4 text-sm text-red-700 bg-red-100 rounded-md mb-4">
                  {error}
                </div>
              )}
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-medium">Review Templates</h2>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="sendToAll"
                    checked={sendToAll}
                    onChange={(e) => {
                      const newValue = e.target.checked;
                      console.log(`Setting sendToAll to ${newValue}`);
                      
                      // Update the sendToAll state
                      setSendToAll(newValue);
                      
                      // If checked, also check all non-delayed users with templates
                      // If unchecked, uncheck all non-delayed users
                      const updatedUserData = userData.map(user => {
                        // Only update non-delayed users with templates
                        if (!user.has_delay && user.template_id) {
                          return {
                            ...user,
                            sendAnyway: newValue // Set to the new value of sendToAll
                          };
                        }
                        return user;
                      });
                      
                      setUserData(updatedUserData);
                      console.log(`Updated all non-delayed users with templates to sendAnyway=${newValue}`);
                    }}
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded mr-2"
                  />
                  <label htmlFor="sendToAll" className="text-sm text-gray-700">
                    Send to all users (including those on track)
                  </label>
                </div>
              </div>
              
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        User
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Company
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Progress
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Template
                      </th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Send
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {userData.map((user, index) => (
                      <tr key={index} className={user.has_delay ? 'bg-red-50' : ''}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">{user.username}</div>
                          <div className="text-sm text-gray-500">{user.email}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{user.company_name}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {user.score_count} / {user.expected}
                          </div>
                          <div className="text-sm text-gray-500">
                            Week {user.week_number}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            user.has_delay ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                          }`}>
                            {user.has_delay ? 'Delayed' : 'On Track'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          {selectingTemplateForIndex === index ? (
                            <div className="space-y-2">
                              <div className="text-sm font-medium text-gray-900">Select Template</div>
                              {loadingTemplates ? (
                                <div className="flex items-center space-x-2">
                                  <div className="w-4 h-4 border-2 border-indigo-500 rounded-full border-t-transparent animate-spin"></div>
                                  <span className="text-xs text-gray-500">Loading templates...</span>
                                </div>
                              ) : (
                                <>
                                  {availableTemplates.length === 0 ? (
                                    <div className="text-xs text-red-500">No templates available</div>
                                  ) : (
                                    <div className="max-h-40 overflow-y-auto">
                                      {availableTemplates.map(template => (
                                        <div 
                                          key={template.id} 
                                          className="p-2 text-sm hover:bg-gray-100 cursor-pointer rounded"
                                          onClick={() => applySelectedTemplate(template.id, template.name)}
                                        >
                                          {template.name}
                                        </div>
                                      ))}
                                    </div>
                                  )}
                                  <button
                                    onClick={cancelTemplateSelection}
                                    className="text-xs text-gray-500 hover:text-gray-700"
                                  >
                                    Cancel
                                  </button>
                                </>
                              )}
                            </div>
                          ) : (
                            user.template_id ? (
                              <div>
                                <div className="text-sm font-medium text-gray-900">{user.template_name}</div>
                                <div className="flex space-x-2 mt-1">
                                  <button 
                                    onClick={() => {
                                      console.log('Setting preview template:', user);
                                      setPreviewTemplate(user);
                                      setShowPreview(true);
                                    }}
                                    className="text-xs text-indigo-600 hover:text-indigo-900"
                                  >
                                    Preview
                                  </button>
                                  <button
                                    onClick={() => handleSelectTemplate(index)}
                                    className="text-xs text-blue-600 hover:text-blue-900"
                                  >
                                    Change
                                  </button>
                                </div>
                              </div>
                            ) : (
                              <div>
                                <div className="text-sm text-gray-500">No template</div>
                                <button
                                  onClick={() => handleSelectTemplate(index)}
                                  className="mt-1 inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                >
                                  Select Template
                                </button>
                              </div>
                            )
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-center">
                          <input 
                            type="checkbox" 
                            checked={user.has_delay ? true : Boolean(user.sendAnyway)}
                            disabled={!user.template_id || user.has_delay} // Disable for delayed users or those without templates
                            onChange={(e) => {
                              // Create a new array to ensure React detects the state change
                              const updatedUserData = [...userData];
                              // Update the specific user's sendAnyway property
                              updatedUserData[index] = {
                                ...updatedUserData[index],
                                sendAnyway: e.target.checked
                              };
                              // Log the change for debugging
                              console.log(`Setting sendAnyway for ${user.username} to ${e.target.checked}`);
                              
                              // Update the state
                              setUserData(updatedUserData);
                            }}
                            className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                          />
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              <div className="flex justify-end mt-6">
                <button 
                  onClick={resetProcess}
                  className="mr-3 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50"
                >
                  Back
                </button>
                <button 
                  onClick={handleSendEmails}
                  disabled={sending}
                  className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                >
                  {sending ? 'Sending...' : 'Send Emails'}
                </button>
              </div>
            </div>
          )}
          
          {step === 'complete' && (
            <div className="text-center py-8">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                <i className="fas fa-check text-xl text-green-600"></i>
              </div>
              <h2 className="mt-3 text-lg font-medium text-gray-900">Email Processing Report</h2>
              
              {result && (
                <div className="mt-4 max-w-md mx-auto bg-gray-50 p-4 rounded-md text-left">
                  <div className="grid grid-cols-2 gap-2 text-sm mb-3">
                    <div className="text-gray-600">Total Users:</div>
                    <div className="font-medium text-gray-900">{result.success_count + result.error_count}</div>
                    
                    <div className="text-gray-600">Processed Users:</div>
                    <div className="font-medium text-gray-900">{result.success_count + result.error_count}</div>
                    
                    <div className="text-gray-600">Emails Sent:</div>
                    <div className="font-medium text-gray-900">{result.success_count}</div>
                    
                    <div className="text-gray-600">Errors:</div>
                    <div className="font-medium text-gray-900">{result.error_count}</div>
                  </div>
                  
                  {result.error_count > 0 && result.errors && (
                    <div className="mt-4 border-t pt-3">
                      <h3 className="text-sm font-medium text-red-600 mb-2">Error Details:</h3>
                      <div className="max-h-60 overflow-y-auto bg-red-50 p-3 rounded-md border border-red-200">
                        {Object.entries(result.errors).map(([email, errorMsg]) => (
                          <div key={email} className="mb-3 pb-3 border-b border-red-200 last:border-0">
                            <p className="text-sm font-medium">{email}</p>
                            <p className="text-xs text-red-700 mt-1">{typeof errorMsg === 'string' ? errorMsg : String(errorMsg)}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
              
              <div className="mt-6">
                <button 
                  type="button" 
                  onClick={resetProcess}
                  className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Send More Emails
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
      
      {/* Template Preview Modal */}
      {showPreview && previewTemplate && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-2xl w-full">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-medium text-gray-900">
                Template Preview for {previewTemplate.username}
              </h3>
              <button
                onClick={() => setShowPreview(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <span className="text-xl">×</span>
              </button>
            </div>
            
            <div className="border-b border-gray-200 p-4 bg-gray-50 mb-4">
              <div className="text-sm font-medium text-gray-800">
                Subject: {previewTemplate.subject || 'No subject'}
              </div>
            </div>
            
            <div className="p-4 border border-gray-200 rounded-md mb-4 max-h-96 overflow-y-auto">
              {previewTemplate.body ? (
                <div dangerouslySetInnerHTML={{ __html: previewTemplate.body }}></div>
              ) : (
                <div className="text-gray-500 italic">No content available</div>
              )}
            </div>
            
            <div className="flex justify-end">
              <button
                onClick={() => setShowPreview(false)}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
