'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { createCompany, testDatabaseConnection } from '@/lib/api/companies';
import type { CompanyFormData, DatabaseType } from '@/types/companies';

export default function NewCompanyPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [testingConnection, setTestingConnection] = useState(false);
  const [error, setError] = useState('');
  const [connectionStatus, setConnectionStatus] = useState<{
    tested: boolean;
    success: boolean;
    message: string;
  }>({
    tested: false,
    success: false,
    message: '',
  });
  
  const [formData, setFormData] = useState<CompanyFormData>({
    name: '',
    connection_string: '',
    database_type: 'postgresql',
    started_at: new Date().toISOString().split('T')[0],
    url: '', // Add url field with empty default
  });
  
  const databaseTypes: DatabaseType[] = [
    'postgresql', 
    'mysql', 
    'sqlite', 
    'sqlserver',
    'mongodb'
  ];
  
  function handleInputChange(e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Reset connection test status when connection details change
    if (name === 'connection_string' || name === 'database_type') {
      setConnectionStatus({
        tested: false,
        success: false,
        message: '',
      });
    }
  }
  
  async function handleTestConnection() {
    if (!formData.connection_string || !formData.database_type) {
      setConnectionStatus({
        tested: true,
        success: false,
        message: 'Connection string and database type are required',
      });
      return;
    }
    
    try {
      setTestingConnection(true);
      setError('');
      
      const result = await testDatabaseConnection(
        formData.connection_string,
        formData.database_type
      );
      
      setConnectionStatus({
        tested: true,
        success: result.success,
        message: result.message,
      });
    } catch (err) {
      console.error('Error testing connection:', err);
      setConnectionStatus({
        tested: true,
        success: false,
        message: err instanceof Error ? err.message : 'Failed to test connection',
      });
    } finally {
      setTestingConnection(false);
    }
  }
  
  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    
    // Basic validation
    if (!formData.name || !formData.connection_string || !formData.database_type) {
      setError('Please fill in all required fields');
      return;
    }
    
    try {
      setLoading(true);
      setError('');
      
      // Date is now properly formatted in the API client
      await createCompany(formData);
      
      // Redirect to companies list after successful creation
      router.push('/companies');
    } catch (err) {
      console.error('Error creating company:', err);
      setError(err instanceof Error ? err.message : 'Failed to create company');
    } finally {
      setLoading(false);
    }
  }
  
  return (
    <div className="max-w-3xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Add New Company</h1>
        <Link
          href="/companies"
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50"
        >
          Cancel
        </Link>
      </div>
      
      {error && (
        <div className="p-4 mb-6 text-sm text-red-700 bg-red-100 rounded-md">
          {error}
        </div>
      )}
      
      <div className="p-6 bg-white rounded-lg shadow">
        <form onSubmit={handleSubmit}>
          <div className="space-y-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Company Name *
              </label>
              <input 
                type="text" 
                id="name" 
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                disabled={loading}
                className="block w-full mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>
            
            <div>
              <label htmlFor="database_type" className="block text-sm font-medium text-gray-700">
                Database Type *
              </label>
              <select 
                id="database_type" 
                name="database_type"
                value={formData.database_type}
                onChange={handleInputChange}
                required
                disabled={loading}
                className="block w-full mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
                {databaseTypes.map(type => (
                  <option key={type} value={type}>
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label htmlFor="connection_string" className="block text-sm font-medium text-gray-700">
                Connection String *
              </label>
              <div className="mt-1">
                <input 
                  type="text" 
                  id="connection_string" 
                  name="connection_string"
                  value={formData.connection_string}
                  onChange={handleInputChange}
                  required
                  disabled={loading}
                  placeholder={`${formData.database_type}://username:password@hostname:port/database`}
                  className="block w-full border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Format: {formData.database_type}://username:password@hostname:port/database
                </p>
              </div>
              
              <div className="mt-2">
                {connectionStatus.tested && (
                  <div className={`mt-2 p-2 text-sm rounded-md ${
                    connectionStatus.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {connectionStatus.message}
                  </div>
                )}
              </div>
            </div>
            
            <div>
              <label htmlFor="started_at" className="block text-sm font-medium text-gray-700">
                Start Date *
              </label>
              <input 
                type="date" 
                id="started_at" 
                name="started_at"
                value={formData.started_at}
                onChange={handleInputChange}
                required
                disabled={loading}
                className="block w-full mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
              <p className="mt-1 text-xs text-gray-500">
                The date when the company started using the system
              </p>
            </div>
            <div>
              <label htmlFor="url" className="block text-sm font-medium text-gray-700">
                Company URL
              </label>
              <div className="mt-1">
                <input 
                  type="url" 
                  id="url" 
                  name="url"
                  value={formData.url || ''}
                  onChange={handleInputChange}
                  disabled={loading}
                  placeholder="https://example.com"
                  className="block w-full border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                />
                <p className="mt-1 text-xs text-gray-500">
                  This URL will be used in email templates for company-specific links
                </p>
              </div>
            </div>
            <div className="flex justify-end space-x-3 pt-4">
              <Link
                href="/companies"
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </Link>
              <button 
                type="submit" 
                disabled={loading}
                className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {loading ? 'Creating...' : 'Create Company'}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
