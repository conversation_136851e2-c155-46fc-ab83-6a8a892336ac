'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {getCompanyById, updateCompany, testDatabaseConnection, uploadCertificate} from '@/lib/api/companies';
import type { Company, CompanyFormData, DatabaseType } from '@/types/companies';

interface PageProps {
  params: {
    id: string;
  };
}

export default function EditCompanyPage({ params }: PageProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testingConnection, setTestingConnection] = useState(false);
  const [error, setError] = useState('');
  const [connectionStatus, setConnectionStatus] = useState<{
    tested: boolean;
    success: boolean;
    message: string;
  }>({
    tested: false,
    success: false,
    message: '',
  });
  
  const [formData, setFormData] = useState<CompanyFormData>({
    name: '',
    connection_string: '',
    database_type: 'postgresql',
    started_at: new Date().toISOString().split('T')[0],
    url: '', // Add url field with empty default
  });

  // Add a separate state for certificate file
  const [certificateFile, setCertificateFile] = useState<File | null>(null);
  const [uploadingCertificate, setUploadingCertificate] = useState(false);
  const [certificateStatus, setCertificateStatus] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  const databaseTypes: DatabaseType[] = [
    'postgresql', 
    'mysql', 
    'sqlite', 
    'sqlserver',
    'mongodb'
  ];


  useEffect(() => {
    async function loadCompany() {
      try {
        setLoading(true);
        
        const company = await getCompanyById(params.id);
        
        // Format date for form input (YYYY-MM-DD)
        let formattedStartDate = company?.started_at || null;
        if (formattedStartDate && formattedStartDate !== '0001-01-01T00:00:00Z') {
          // Extract just the date portion for the date input
          formattedStartDate = new Date(formattedStartDate).toISOString().split('T')[0];
        } else {
          formattedStartDate = new Date().toISOString().split('T')[0];
        }
        
        setFormData({
          name: company?.name || '',
          connection_string: company?.connection_string || '',
          database_type: company?.database_type || 'postgresql',
          started_at: formattedStartDate,
          url: company?.url || '', // Include url from company data
        });
      } catch (err) {
        console.error('Error loading company:', err);
        setError('Failed to load company details');
      } finally {
        setLoading(false);
      }
    }
    
    loadCompany();
  }, [params.id]);
  
  function handleInputChange(e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Reset connection test status when connection details change
    if (name === 'connection_string' || name === 'database_type') {
      setConnectionStatus({
        tested: false,
        success: false,
        message: '',
      });
    }
  }
  
  async function handleTestConnection() {
    if (!formData.connection_string || !formData.database_type) {
      setConnectionStatus({
        tested: true,
        success: false,
        message: 'Connection string and database type are required',
      });
      return;
    }
    
    try {
      setTestingConnection(true);
      setError('');
      
      const result = await testDatabaseConnection(
        formData.connection_string,
        formData.database_type
      );
      
      setConnectionStatus({
        tested: true,
        success: result.success,
        message: result.message,
      });
    } catch (err) {
      console.error('Error testing connection:', err);
      setConnectionStatus({
        tested: true,
        success: false,
        message: err instanceof Error ? err.message : 'Failed to test connection',
      });
    } finally {
      setTestingConnection(false);
    }
  }

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();

    // Basic validation
    if (!formData.name || !formData.connection_string || !formData.database_type) {
      setError('Please fill in all required fields');
      return;
    }

    try {
      setSaving(true);
      setError('');

      // Date is now properly formatted in the API client
      await updateCompany(params.id, formData);

      // Redirect to company details page after successful update
      router.push(`/companies/${params.id}`);
    } catch (err) {
      console.error('Error updating company:', err);
      setError(err instanceof Error ? err.message : 'Failed to update company');
    } finally {
      setSaving(false);
    }
  }

  // Separate function to handle certificate upload
  async function handleCertificateUpload(e: React.FormEvent) {
    e.preventDefault();
    
    if (!certificateFile) {
      setCertificateStatus({
        success: false,
        message: 'Please select a certificate file'
      });
      return;
    }
    
    try {
      setUploadingCertificate(true);
      setCertificateStatus(null);
      
      const result = await uploadCertificate(params.id, certificateFile);
      
      setCertificateStatus({
        success: true,
        message: 'Certificate uploaded successfully'
      });
      
      // Reset the file input
      setCertificateFile(null);
      const fileInput = document.getElementById('certificate') as HTMLInputElement;
      if (fileInput) fileInput.value = '';
    } catch (err) {
      console.error('Error uploading certificate:', err);
      setCertificateStatus({
        success: false,
        message: err instanceof Error ? err.message : 'Failed to upload certificate'
      });
    } finally {
      setUploadingCertificate(false);
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="w-12 h-12 border-4 border-indigo-500 rounded-full border-t-transparent animate-spin"></div>
      </div>
    );
  }
  
  return (
    <div className="max-w-3xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Edit Company</h1>
        <Link
          href={`/companies/${params.id}`}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50"
        >
          Cancel
        </Link>
      </div>
      
      {error && (
        <div className="p-4 mb-6 text-sm text-red-700 bg-red-100 rounded-md">
          {error}
        </div>
      )}
      
      <div className="p-6 bg-white rounded-lg shadow mb-6">
        <form onSubmit={handleSubmit}>
          <div className="space-y-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Company Name *
              </label>
              <input 
                type="text" 
                id="name" 
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
                disabled={saving}
                className="block w-full mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>
            
            <div>
              <label htmlFor="database_type" className="block text-sm font-medium text-gray-700">
                Database Type *
              </label>
              <select 
                id="database_type" 
                name="database_type"
                value={formData.database_type}
                onChange={handleInputChange}
                required
                disabled={saving}
                className="block w-full mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
                {databaseTypes.map(type => (
                  <option key={type} value={type}>
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label htmlFor="connection_string" className="block text-sm font-medium text-gray-700">
                Connection String *
              </label>
              <div className="mt-1">
                <input 
                  type="text" 
                  id="connection_string" 
                  name="connection_string"
                  value={formData.connection_string}
                  onChange={handleInputChange}
                  required
                  disabled={saving}
                  placeholder={`${formData.database_type}://username:password@hostname:port/database`}
                  className="block w-full border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                />
                <p className="mt-1 text-xs text-gray-500">
                  Format: {formData.database_type}://username:password@hostname:port/database
                </p>
              </div>
            </div>
            
            <div>
              <label htmlFor="started_at" className="block text-sm font-medium text-gray-700">
                Start Date *
              </label>
              <input 
                type="date" 
                id="started_at" 
                name="started_at"
                value={formData.started_at}
                onChange={handleInputChange}
                required
                disabled={saving}
                className="block w-full mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
              <p className="mt-1 text-xs text-gray-500">
                The date when the company started using the system
              </p>
            </div>
            
            <div>
              <label htmlFor="url" className="block text-sm font-medium text-gray-700">
                Company URL
              </label>
              <div className="mt-1">
                <input 
                  type="url" 
                  id="url" 
                  name="url"
                  value={formData.url || ''}
                  onChange={handleInputChange}
                  disabled={saving}
                  placeholder="https://example.com"
                  className="block w-full border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                />
                <p className="mt-1 text-xs text-gray-500">
                  This URL will be used in email templates for company-specific links
                </p>
              </div>
            </div>
            
            <div className="flex justify-end space-x-3 pt-4">
              <Link
                href={`/companies/${params.id}`}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </Link>
              <button 
                type="submit" 
                disabled={saving}
                className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {saving ? 'Saving...' : 'Save Changes'}
              </button>
            </div>
          </div>
        </form>
      </div>
      
      {/* Separate form for certificate upload */}
      <div className="p-6 bg-white rounded-lg shadow">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Upload SSL Certificate</h2>
        
        {certificateStatus && (
          <div className={`p-4 mb-6 text-sm rounded-md ${
            certificateStatus.success 
              ? 'text-green-700 bg-green-100' 
              : 'text-red-700 bg-red-100'
          }`}>
            {certificateStatus.message}
          </div>
        )}
        
        <form onSubmit={handleCertificateUpload}>
          <div className="space-y-6">
            <div>
              <label htmlFor="certificate" className="block text-sm font-medium text-gray-700">
                SSL Certificate File
              </label>
              <input
                type="file"
                id="certificate"
                name="certificate"
                accept=".pem,.crt,.cer,.key"
                onChange={(e) => {
                  const file = e.target.files?.[0] || null;
                  setCertificateFile(file);
                }}
                disabled={uploadingCertificate}
                className="block w-full mt-1 border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
              <p className="mt-1 text-xs text-gray-500">
                Accepted formats: .pem, .crt, .cer, .key
              </p>
            </div>
            
            <div className="flex justify-end">
              <button 
                type="submit" 
                disabled={uploadingCertificate || !certificateFile}
                className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {uploadingCertificate ? 'Uploading...' : 'Upload Certificate'}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
