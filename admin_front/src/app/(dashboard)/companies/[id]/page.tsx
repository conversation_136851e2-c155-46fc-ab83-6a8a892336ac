'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { getCompanyById, deleteCompany } from '@/lib/api/companies';
import type { Company } from '@/types/companies';
import { format } from 'date-fns';

interface PageProps {
  params: {
    id: string;
  };
}

export default function CompanyDetailPage({ params }: PageProps) {
  const router = useRouter();
  const [company, setCompany] = useState<Company | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);
  
  useEffect(() => {
    loadCompany();
  }, []);
  
  async function loadCompany() {
    try {
      setLoading(true);
      setError('');
      
      const data = await getCompanyById(params.id);
      setCompany(data);
    } catch (err) {
      console.error('Error loading company:', err);
      setError('Failed to load company details');
    } finally {
      setLoading(false);
    }
  }
  
  async function handleDelete() {
    if (!company) return;
    
    if (!confirm(`Are you sure you want to delete "${company.name}"? This action cannot be undone.`)) {
      return;
    }
    
    try {
      setIsDeleting(true);
      await deleteCompany(params.id);
      
      // Redirect to companies list after successful deletion
      router.push('/companies');
    } catch (err) {
      console.error('Error deleting company:', err);
      setError('Failed to delete company');
      setIsDeleting(false);
    }
  }
  
  // Format date for display
  function formatDate(dateString: string): string {
    if (!dateString || dateString === '0001-01-01T00:00:00Z') {
      return 'Not started';
    }
    
    try {
      return format(new Date(dateString), 'MMMM d, yyyy');
    } catch (e) {
      return 'Invalid date';
    }
  }
  
  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="w-12 h-12 border-4 border-indigo-500 rounded-full border-t-transparent animate-spin"></div>
      </div>
    );
  }
  
  if (error || !company) {
    return (
      <div className="max-w-3xl mx-auto">
        <div className="p-6 text-center bg-white rounded-lg shadow">
          <h2 className="text-xl font-medium text-red-600">Error</h2>
          <p className="mt-2 text-gray-500">{error || 'Company not found'}</p>
          <Link
            href="/companies"
            className="inline-block px-4 py-2 mt-4 text-sm font-medium text-indigo-600 bg-white border border-indigo-300 rounded-md shadow-sm hover:bg-indigo-50"
          >
            Back to Companies
          </Link>
        </div>
      </div>
    );
  }
  
  return (
    <div className="max-w-3xl mx-auto">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold text-gray-900">{company.name}</h1>
        <div className="flex space-x-3">
          <Link
            href="/companies"
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50"
          >
            Back
          </Link>
          <Link
            href={`/companies/${params.id}/edit`}
            className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700"
          >
            Edit
          </Link>
        </div>
      </div>
      
      {error && (
        <div className="p-4 mb-6 text-sm text-red-700 bg-red-100 rounded-md">
          {error}
        </div>
      )}
      
      <div className="p-6 mb-6 bg-white rounded-lg shadow">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Company Details</h2>
        
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
          <div>
            <h3 className="text-sm font-medium text-gray-500">Company Name</h3>
            <p className="mt-1 text-base text-gray-900">{company.name}</p>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-gray-500">Database Type</h3>
            <p className="mt-1 text-base text-gray-900 capitalize">{company.database_type}</p>
          </div>
          
          <div className="sm:col-span-2">
            <h3 className="text-sm font-medium text-gray-500">Connection String</h3>
            <div className="mt-1 relative">
              <p className="text-sm text-gray-900 font-mono bg-gray-50 p-2 rounded border border-gray-200 overflow-x-auto">
                {company.connection_string}
              </p>
            </div>
          </div>
          
          {company.url && (
            <div className="sm:col-span-2">
              <h3 className="text-sm font-medium text-gray-500">Company URL</h3>
              <div className="mt-1">
                <a 
                  href={company.url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-indigo-600 hover:text-indigo-800 hover:underline"
                >
                  {company.url}
                </a>
              </div>
            </div>
          )}
          
          <div>
            <h3 className="text-sm font-medium text-gray-500">Start Date</h3>
            <p className="mt-1 text-base text-gray-900">{formatDate(company.started_at)}</p>
          </div>
          
          <div>
            <h3 className="text-sm font-medium text-gray-500">Created</h3>
            <p className="mt-1 text-base text-gray-900">{formatDate(company.created_at)}</p>
          </div>
        </div>
      </div>
      <div className="p-6 bg-white rounded-lg shadow">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Danger Zone</h2>
        
        <div className="border border-red-200 rounded-md p-4">
          <h3 className="text-sm font-medium text-red-800">Delete this company</h3>
          <p className="mt-1 text-sm text-gray-500">
            Once you delete a company, all of its data will be permanently removed. This action cannot be undone.
          </p>
          <button
            onClick={handleDelete}
            disabled={isDeleting}
            className="mt-3 px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
          >
            {isDeleting ? 'Deleting...' : 'Delete Company'}
          </button>
        </div>
      </div>
    </div>
  );
}
