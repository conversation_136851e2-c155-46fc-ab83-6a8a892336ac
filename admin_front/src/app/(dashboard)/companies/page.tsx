'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { getCompanies, deleteCompany } from '@/lib/api/companies';
import type { Company } from '@/types/companies';
import { format } from 'date-fns';

export default function CompaniesPage() {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  
  useEffect(() => {
    loadCompanies();
  }, []);
  
  async function loadCompanies() {
    try {
      setLoading(true);
      setError('');
      
      // Use the actual API in both development and production
      const data = await getCompanies();
      setCompanies(data);
    } catch (err) {
      console.error('Error loading companies:', err);
      setError('Failed to load companies. Please try again.');
    } finally {
      setLoading(false);
    }
  }
  
  async function handleDelete(company: Company) {
    if (!confirm(`Are you sure you want to delete "${company.name}"? This action cannot be undone.`)) {
      return;
    }
    
    try {
      setIsDeleting(company.id);
      await deleteCompany(company.id);
      
      // Update local state after successful deletion
      setCompanies(companies.filter(c => c.id !== company.id));
      
      // Show temporary success message
      alert(`Company "${company.name}" has been deleted successfully.`);
    } catch (err) {
      console.error('Error deleting company:', err);
      alert('Failed to delete company. Please try again.');
    } finally {
      setIsDeleting(null);
    }
  }
  
  // Filter companies based on search query
  const filteredCompanies = companies.filter(company => 
    searchQuery === '' || 
    company.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    company.database_type.toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  // Format date for display
  function formatDate(dateString: string): string {
    if (!dateString || dateString === '0001-01-01T00:00:00Z') {
      return 'Not started';
    }
    
    try {
      return format(new Date(dateString), 'MMM d, yyyy');
    } catch (e) {
      return 'Invalid date';
    }
  }
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Companies</h1>
        <Link 
          href="/companies/new" 
          className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700"
        >
          New Company
        </Link>
      </div>
      
      {error && (
        <div className="p-4 text-sm text-red-700 bg-red-100 rounded-md">
          {error}
        </div>
      )}
      
      <div className="relative w-full md:w-96">
        <input 
          type="text" 
          placeholder="Search companies..." 
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full px-4 py-2 pl-10 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
        />
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          🔍
        </div>
      </div>
      
      {loading ? (
        <div className="flex items-center justify-center py-12">
          <div className="w-12 h-12 border-4 border-indigo-500 rounded-full border-t-transparent animate-spin"></div>
        </div>
      ) : filteredCompanies.length === 0 ? (
        <div className="flex flex-col items-center justify-center p-12 bg-white rounded-lg shadow">
          <div className="p-4 text-indigo-500 bg-indigo-100 rounded-full">
            🏢
          </div>
          <h3 className="mt-4 text-lg font-medium text-gray-900">No companies found</h3>
          <p className="mt-1 text-gray-500">
            {searchQuery ? 'Try adjusting your search.' : 'Create your first company to get started.'}
          </p>
          {!searchQuery && (
            <Link 
              href="/companies/new" 
              className="mt-4 inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700"
            >
              Add Company
            </Link>
          )}
        </div>
      ) : (
        <div className="overflow-hidden bg-white shadow sm:rounded-md">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Company Name</th>
                <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Database Type</th>
                <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Start Date</th>
                <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Created</th>
                <th className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredCompanies.map(company => (
                <tr key={company.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{company.name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="px-2 py-1 text-xs text-indigo-800 bg-indigo-100 rounded-full inline-block">
                      {company.database_type}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">{formatDate(company.started_at)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">{formatDate(company.created_at)}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-3">
                      <Link
                        href={`/companies/${company.id}`}
                        className="text-indigo-600 hover:text-indigo-900"
                      >
                        👁️
                      </Link>
                      <Link
                        href={`/companies/${company.id}/edit`}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        ✏️
                      </Link>
                      <button
                        onClick={() => handleDelete(company)}
                        disabled={isDeleting === company.id}
                        className="text-red-600 hover:text-red-900 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isDeleting === company.id ? '⏳' : '🗑️'}
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}