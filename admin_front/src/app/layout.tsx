import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { Providers } from './providers';
import { ApiUrlDebugger } from '@/components/debug/ApiUrlDebugger';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Email Automation Dashboard',
  description: 'Admin dashboard for email automation system',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <Providers>
          {children}
          {process.env.NODE_ENV !== 'production' && <ApiUrlDebugger />}
        </Providers>
      </body>
    </html>
  );
}
