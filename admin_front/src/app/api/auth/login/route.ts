import { NextRequest, NextResponse } from 'next/server';

// Remove the /api suffix if it's already included in the environment variable
const API_URL = process.env.NEXT_PUBLIC_API_URL 
  ? (process.env.NEXT_PUBLIC_API_URL.endsWith('/api') 
      ? process.env.NEXT_PUBLIC_API_URL 
      : `${process.env.NEXT_PUBLIC_API_URL}/api`)
  : 'http://localhost:8080/api';

// For debugging
console.log('API_URL in auth/login route:', API_URL);

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password } = body;

    if (!email || !password) {
      return NextResponse.json(
        { message: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Forward the request to the backend API
    const response = await fetch(`${API_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { message: data.message || 'Authentication failed' },
        { status: response.status }
      );
    }

    // Get any cookies from the response to pass back
    const cookies = response.headers.get('set-cookie');
    
    // Create the response
    const nextResponse = NextResponse.json(data);
    
    // If the backend sent cookies, forward them to the client
    if (cookies) {
      // Parse cookies from the Set-Cookie header
      const cookieStrings = cookies.split(', ');
      
      for (const cookieString of cookieStrings) {
        const [cookiePart] = cookieString.split(';');
        const [name, value] = cookiePart.split('=');
        
        // Set each cookie in the response
        nextResponse.cookies.set(name, value, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'lax',
          path: '/',
        });
      }
    } else {
      // If no cookies from backend, manually create an auth cookie
      // This is for demo purposes - in a real app, use proper JWT handling
      nextResponse.cookies.set('auth_token', data.token || 'demo-token', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        path: '/',
        // Set expiration to 24 hours
        maxAge: 60 * 60 * 24,
      });
    }

    return nextResponse;
  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
