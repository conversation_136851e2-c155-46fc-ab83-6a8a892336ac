import { NextRequest, NextResponse } from 'next/server';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api';

export async function GET(request: NextRequest) {
  try {
    // Get the auth token from cookies
    const authToken = request.cookies.get('auth_token')?.value;
    
    if (!authToken) {
      return NextResponse.json(
        { message: 'Authentication required' },
        { status: 401 }
      );
    }

    // Forward the request to the backend API
    const response = await fetch(`${API_URL}/auth/me`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        // Forward the auth token as both cookie and auth header for flexibility
        'Cookie': `auth_token=${authToken}`,
        'Authorization': `Bearer ${authToken}`,
      },
    });

    if (!response.ok) {
      // If the backend says the user is not authenticated, clear the cookie
      const errorResponse = NextResponse.json(
        { message: 'Authentication failed' },
        { status: response.status }
      );
      
      // Clear the invalid auth cookie
      errorResponse.cookies.set('auth_token', '', {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        path: '/',
        maxAge: 0, // Expire immediately
      });
      
      return errorResponse;
    }

    const userData = await response.json();
    return NextResponse.json(userData);
  } catch (error) {
    console.error('Auth check error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}