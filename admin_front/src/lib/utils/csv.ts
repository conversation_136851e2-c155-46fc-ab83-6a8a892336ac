// src/lib/utils/csv.ts
export interface ColumnMapping {
    sourceField: string;
    targetField: string;
    required: boolean;
  }
  
  export interface ValidationResult {
    valid: boolean;
    errors: string[];
  }
  
  export function validateRow(
    row: Record<string, any>,
    mappings: ColumnMapping[]
  ): ValidationResult {
    const errors: string[] = [];
    
    // Check for required fields
    for (const mapping of mappings) {
      if (mapping.required && (!row[mapping.sourceField] || row[mapping.sourceField].trim() === '')) {
        errors.push(`Missing required field: ${mapping.targetField}`);
      }
    }
    
    // Validate email format if email field exists
    const emailMapping = mappings.find(m => m.targetField === 'email');
    if (emailMapping && row[emailMapping.sourceField]) {
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      if (!emailRegex.test(row[emailMapping.sourceField])) {
        errors.push('Invalid email format');
      }
    }
    
    return {
      valid: errors.length === 0,
      errors
    };
  }
  
  export function mapRowData(
    row: Record<string, any>,
    mappings: ColumnMapping[]
  ): Record<string, any> {
    const result: Record<string, any> = {};
    
    for (const mapping of mappings) {
      if (mapping.sourceField && mapping.targetField) {
        result[mapping.targetField] = row[mapping.sourceField];
      }
    }
    
    return result;
  }
  
  // src/lib/utils/date.ts
  export function formatDate(date: Date | string): string {
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }
  
  export function formatTime(date: Date | string): string {
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  }
  
  export function formatDateTime(date: Date | string): string {
    const d = typeof date === 'string' ? new Date(date) : date;
    return `${formatDate(d)} at ${formatTime(d)}`;
  }
  
  export function calculateDaysBetween(startDate: Date | string, endDate: Date | string): number {
    const start = typeof startDate === 'string' ? new Date(startDate) : startDate;
    const end = typeof endDate === 'string' ? new Date(endDate) : endDate;
    
    // Reset time part to compare dates only
    const startDay = new Date(start.getFullYear(), start.getMonth(), start.getDate());
    const endDay = new Date(end.getFullYear(), end.getMonth(), end.getDate());
    
    // Calculate the difference in milliseconds
    const differenceMs = endDay.getTime() - startDay.getTime();
    
    // Convert to days and return
    return Math.floor(differenceMs / (1000 * 60 * 60 * 24));
  }