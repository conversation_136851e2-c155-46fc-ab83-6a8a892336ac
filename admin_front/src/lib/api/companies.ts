// lib/api/companies.ts
import { fetchAPI } from './fetchAPI';
import type { Company, CompanyFormData } from '@/types/companies';

// Get all companies
export async function getCompanies(): Promise<Company[]> {
  try {
    // PROBLEM: This might be using a hardcoded URL internally in fetchAPI
    // FIXED: We've already updated fetchAPI to use the correct URL
    const response = await fetchAPI<Company[] | null>('/companies');
    return response || [];
  } catch (error) {
    console.error('Failed to fetch companies:', error);
    return [];
  }
}

// Get a single company by ID
export async function getCompanyById(id: string): Promise<Company | null> {
  try {
    return await fetchAPI<Company | null>(`/companies/${id}`);
  } catch (error) {
    console.error(`Failed to fetch company ${id}:`, error);
    return null;
  }
}

// Create a new company
export async function createCompany(data: CompanyFormData): Promise<Company> {
  const formattedData = {
    ...data,
    started_at: data.started_at ? new Date(data.started_at).toISOString() : ''
  };

  return fetchAPI<Company>('/companies', {
    method: 'POST',
    body: JSON.stringify(formattedData),
  });
}

// Update an existing company
export async function updateCompany(id: string, data: CompanyFormData): Promise<Company> {
  const formattedData = {
    ...data,
    started_at: data.started_at ? new Date(data.started_at).toISOString() : ''
  };

  return fetchAPI<Company>(`/companies/${id}`, {
    method: 'PUT',
    body: JSON.stringify(formattedData),
  });
}

// Delete a company
export async function deleteCompany(id: string): Promise<void> {
  return fetchAPI<void>(`/companies/${id}`, {
    method: 'DELETE',
  });
}

// Get company metrics (e.g., user count, status)
export async function getCompanyMetrics(id: string): Promise<any> {
  try {
    return await fetchAPI<any>(`/companies/${id}/metrics`);
  } catch (error) {
    console.error(`Failed to fetch metrics for company ${id}:`, error);
    return null;
  }
}

// Test database connection
export async function testDatabaseConnection(connectionString: string, databaseType: string): Promise<{ success: boolean; message: string }> {
  return fetchAPI<{ success: boolean; message: string }>('/companies/test-connection', {
    method: 'POST',
    body: JSON.stringify({
      connection_string: connectionString,
      database_type: databaseType,
    }),
  });
}

// Upload SSL certificate for a company
export async function uploadCertificate(companyId: string, certificateFile: File): Promise<{ success: boolean; message: string }> {
  const formData = new FormData();
  formData.append('certificate', certificateFile);

  return fetchAPI<{ success: boolean; message: string }>(`/companies/${companyId}/certificate`, {
    method: 'POST',
    body: formData,
    headers: {
      // Don't set Content-Type header when using FormData
      // The browser will set it automatically with the correct boundary
    },
  });
}

// Check certificate status
export async function checkCertificate(companyId: string): Promise<{ company_id: string; use_ssl: boolean; certificate_exists: boolean }> {
  return fetchAPI<{ company_id: string; use_ssl: boolean; certificate_exists: boolean }>(`/companies/${companyId}/certificate`);
}

// Delete certificate
export async function deleteCertificate(companyId: string): Promise<{ success: boolean; message: string }> {
  return fetchAPI<{ success: boolean; message: string }>(`/companies/${companyId}/certificate`, {
    method: 'DELETE',
  });
}


