// lib/api/mail-config.ts
import { fetchAPI } from './fetchAPI';
import type {
  MailConfig,
  MailConfigResponse,
  MailConfigsListResponse,
  CreateMailConfigRequest,
  UpdateMailConfigRequest
} from '@/types/mail-config';

// Make sure this service uses fetchAPI which we just fixed
export const mailConfigService = {
  async getMailConfigs(page?: number, limit?: number): Promise<MailConfigsListResponse> {
    try {
      let endpoint = '/mail/configs';
      if (page && limit) endpoint += `?page=${page}&limit=${limit}`;
      
      // This will now use the correct API URL through fetchAPI
      const response = await fetchAPI<MailConfig[] | MailConfigsListResponse>(endpoint);
      
      // Check if response is an array (direct API response)
      if (Array.isArray(response)) {
        return {
          mail_configs: response,
          total: response.length
        };
      }
      
      // Otherwise return the response as is
      return response;
    } catch (error) {
      console.error('Error fetching mail configs:', error);
      throw error;
    }
  },

  async getMailConfig(id: string): Promise<MailConfigResponse> {
    try {
      // Handle both direct object response and wrapped response
      const response = await fetchAPI<MailConfig | MailConfigResponse>(`/mail/configs/${id}`);
      
      // Check if response is direct mail config object
      if (!('mail_config' in response)) {
        return {
          mail_config: response as MailConfig
        };
      }
      
      return response as MailConfigResponse;
    } catch (error) {
      console.error('Error in getMailConfig:', error);
      return {
        mail_config: {
          id,
          company_id: '',
          sender_email: '',
          sender_name: '',
          smtp_host: '',
          smtp_port: 587,
          smtp_username: '',
          use_ssl: true,
          active: false,
          created_at: '',
          updated_at: ''
        },
        message: error instanceof Error ? error.message : 'Failed to load mail configuration'
      };
    }
  },

  async getMailConfigsByCompany(companyId: string): Promise<MailConfigsListResponse> {
    try {
      const response = await fetchAPI<MailConfig[] | MailConfigsListResponse>(`/mail/configs/company/${companyId}`);
      
      // Check if response is an array (direct API response)
      if (Array.isArray(response)) {
        return {
          mail_configs: response,
          total: response.length
        };
      }
      
      return response;
    } catch (error) {
      console.error('Error in getMailConfigsByCompany:', error);
      return {
        mail_configs: [],
        total: 0,
        message: error instanceof Error ? error.message : 'Failed to load mail configurations'
      };
    }
  },

  async createMailConfig(data: CreateMailConfigRequest): Promise<MailConfigResponse> {
    const response = await fetchAPI<MailConfig | MailConfigResponse>('/mail/configs', {
      method: 'POST',
      body: JSON.stringify(data)
    });
    
    // Check if response is direct mail config object
    if (!('mail_config' in response)) {
      return {
        mail_config: response as MailConfig
      };
    }
    
    return response as MailConfigResponse;
  },

  async updateMailConfig(id: string, data: UpdateMailConfigRequest): Promise<MailConfigResponse> {
    const response = await fetchAPI<MailConfig | MailConfigResponse>(`/mail/configs/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
    
    // Check if response is direct mail config object
    if (!('mail_config' in response)) {
      return {
        mail_config: response as MailConfig
      };
    }
    
    return response as MailConfigResponse;
  },

  async deleteMailConfig(id: string): Promise<{ message: string }> {
    const response = await fetchAPI<{ message: string } | null>(`/mail/configs/${id}`, {
      method: 'DELETE'
    });
    
    return response || { message: 'Mail configuration deleted successfully' };
  },

  async toggleMailConfigStatus(id: string, active: boolean): Promise<MailConfigResponse> {
    return this.updateMailConfig(id, { active });
  },

  async testMailConfig(
    config: MailConfig, 
    testEmail: string
  ): Promise<{success: boolean, message?: string, error?: string}> {
    try {
      // Validate that the test email is provided and valid
      if (!testEmail || !testEmail.includes('@')) {
        return {
          success: false,
          error: 'A valid test email address is required'
        };
      }
      
      const response = await fetchAPI<{success: boolean, message?: string, error?: string}>('/mail/configs/test', {
        method: 'POST',
        body: JSON.stringify({ 
          config,
          testEmail
        }),
      });
      
      return response;
    } catch (error: any) {
      console.error('Error testing mail config:', error);
      return {
        success: false,
        error: error.message || 'Failed to test mail configuration',
      };
    }
  }
};
