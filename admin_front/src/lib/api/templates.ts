import type { Template, TemplateGroup, TemplateFormData, TemplateGroupFormData } from '@/types/templates';
import { fetchAPI } from './fetchAPI';

// Template Groups
export async function getTemplateGroups(companyId?: string): Promise<TemplateGroup[]> {
  try {
    // Using the fixed fetchAPI that uses the correct API URL
    const endpoint = companyId ? `/templates/groups?company_id=${companyId}` : '/templates/groups';
    const response = await fetchAPI<TemplateGroup[] | null>(endpoint);
    return response || [];
  } catch (error) {
    console.error('Failed to fetch template groups:', error);
    return [];
  }
}

export async function getTemplateGroupById(id: string): Promise<TemplateGroup | null> {
  try {
    return await fetchAPI<TemplateGroup | null>(`/templates/groups/${id}`);
  } catch (error) {
    console.error(`Failed to fetch template group ${id}:`, error);
    return null;
  }
}

export async function getTemplateGroupByDay(day: number, companyId: string): Promise<TemplateGroup | null> {
  try {
    return await fetchAPI<TemplateGroup | null>(`/templates/groups/day/${day}/company/${companyId}`);
  } catch (error) {
    console.error(`Failed to fetch template group for day ${day} and company ${companyId}:`, error);
    return null;
  }
}

export async function createTemplateGroup(data: TemplateGroupFormData): Promise<TemplateGroup> {
  return fetchAPI<TemplateGroup>('/templates/groups', {
    method: 'POST',
    body: JSON.stringify(data),
  });
}

export async function updateTemplateGroup(id: string, data: TemplateGroupFormData): Promise<TemplateGroup> {
  return fetchAPI<TemplateGroup>(`/templates/groups/${id}`, {
    method: 'PUT',
    body: JSON.stringify(data),
  });
}

export async function deleteTemplateGroup(id: string): Promise<void> {
  return fetchAPI<void>(`/templates/groups/${id}`, {
    method: 'DELETE',
  });
}

// Templates
export async function getTemplates(groupId: string): Promise<Template[]> {
  try {
    const response = await fetchAPI<Template[] | null>(`/templates/group/${groupId}`);
    
    // Map responses if needed
    if (response && response.length > 0) {
      return response.map(template => ({
        ...template,
        content: template.body || template.content // Map body to content if needed
      }));
    }
    
    // Return empty array if response is null
    return [];
  } catch (error) {
    console.error(`Failed to fetch templates for group ${groupId}:`, error);
    // Return empty array on error to avoid null reference errors
    return [];
  }
}

export async function getAllTemplates(): Promise<Template[]> {
  try {
    // First, get all template groups
    const groups = await getTemplateGroups();
    
    // Return empty array if no groups
    if (groups.length === 0) {
      return [];
    }
    
    // Then fetch templates for each group
    const templatesPromises = groups.map(group => getTemplates(group.id));
    
    // Wait for all requests to complete
    const templatesArrays = await Promise.all(templatesPromises);
    
    // Flatten the arrays of templates
    return templatesArrays.flat();
  } catch (error) {
    console.error('Failed to fetch all templates:', error);
    return [];
  }
}

export async function getTemplateById(id: string): Promise<Template | null> {
  try {
    const template = await fetchAPI<Template | null>(`/templates/${id}`);
    
    // Map body to content if needed
    if (template) {
      return {
        ...template,
        content: template.body || template.content // Use body or content, whichever exists
      };
    }
    
    return template;
  } catch (error) {
    console.error(`Failed to fetch template ${id}:`, error);
    return null;
  }
}

export async function createTemplate(data: TemplateFormData): Promise<Template> {
  // Ensure week_number is a number, not a string
  const weekNumber = typeof data.week_number === 'string' 
    ? parseInt(data.week_number, 10) 
    : data.week_number;
  
  // Map frontend field names to what the backend/database expects
  const requestData = {
    name: data.name,
    subject: data.subject,
    body: data.content, // Map content to body
    group_id: data.group_id,
    week_number: weekNumber || 0, // Ensure it's a number and has a default value
    type: data.type || 'default',
    default_data: data.default_data || {},
    required_fields: data.required_fields || [],
    is_active: data.is_active !== undefined ? data.is_active : true
  };
  
  console.log('API request data:', requestData); // Add debugging
  
  return fetchAPI<Template>('/templates', {
    method: 'POST',
    body: JSON.stringify(requestData),
  });
}

export async function updateTemplate(id: string, data: Partial<Template>): Promise<Template> {
  console.log('Sending update template request:', data); // Add logging
  
  // Ensure week_number is a number
  const processedData = {
    ...data,
    week_number: Number(data.week_number)
  };
  
  return fetchAPI<Template>(`/templates/${id}`, {
    method: 'PUT',
    body: JSON.stringify(processedData),
  });
}

export async function deleteTemplate(id: string): Promise<void> {
  return fetchAPI<void>(`/templates/${id}`, {
    method: 'DELETE',
  });
}

export async function renderTemplate(id: string, templateData?: Record<string, string>): Promise<string> {
  const response = await fetchAPI<{body: string} | {content: string} | string>(`/templates/${id}/render`, {
    method: 'POST',
    body: JSON.stringify(templateData || {}),
  });
  
  // Handle different response types
  if (typeof response === 'string') {
    return response;
  } else if (response && 'body' in response) {
    return response.body;
  } else if (response && 'content' in response) {
    return response.content;
  }
  
  return '';
}

export async function getAlternativeTemplates(companyId: string): Promise<Template[]> {
  try {
    const response = await fetchAPI<Template[] | null>(`/templates/alternatives/${companyId}`);
    
    if (response && response.length > 0) {
      return response;
    }
    
    return [];
  } catch (error) {
    console.error(`Failed to fetch alternative templates for company ${companyId}:`, error);
    return [];
  }
}
