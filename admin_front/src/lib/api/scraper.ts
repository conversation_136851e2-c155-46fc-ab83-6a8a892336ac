import { fetchAPI } from './fetchAPI';

export async function selectTemplatesForUsers(usersData: { username: string; email: string; company_name: string }[]): Promise<any> {
    const requestBody = {
        users: usersData.map(item => ({
            username: item.username,
            email: item.email,
            company_name: item.company_name,
        })),
    };

    return fetchAPI<any>(`/scraper/select-templates`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
    });
}

export async function sendTemplateEmails(users: any[], sendToAll: boolean = false): Promise<any> {
    const requestBody = {
        users: users.map(user => ({
            email: user.email,
            template_id: user.template_id,
            data: user.data,
            company_id: user.company_id,
        })),
        send_to_all: sendToAll,
    };

    // Add timeout configuration to prevent 504 errors
    return fetchAPI<any>(`/scraper/send-template-emails`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
        // Set a longer timeout for this specific API call
        timeout: 120000, // 2 minutes
    });
}
