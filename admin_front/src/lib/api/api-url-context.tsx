'use client';

import { createContext, useContext, ReactNode } from 'react';

interface ApiUrlContextType {
  apiUrl: string;
}

const ApiUrlContext = createContext<ApiUrlContextType>({
  apiUrl: '/api',
});

export function ApiUrlProvider({ children }: { children: ReactNode }) {
  // Get API URL from environment variable
  const apiUrl = process.env.NEXT_PUBLIC_API_URL || '/api';
  
  return (
    <ApiUrlContext.Provider value={{ apiUrl }}>
      {children}
    </ApiUrlContext.Provider>
  );
}

export function useApiUrl() {
  return useContext(ApiUrlContext);
}