import { jwtDecode } from 'jwt-decode';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api';

interface LoginCredentials {
  email: string;
  password: string;
}

interface AuthResponse {
  success: boolean;
  token?: string;
  user?: any;
  message?: string;
}

interface TokenPayload {
  sub: string;
  name: string;
  email: string;
  role: string;
  exp: number;
}

// Login and get token
export async function login(credentials: LoginCredentials): Promise<AuthResponse> {
  try {
    console.log('Login API call with credentials:', credentials.email);
    
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
      credentials: 'include', // Important for cookies
    });

    const data = await response.json();
    console.log('Login API response:', response.status, data);

    if (!response.ok) {
      return {
        success: false,
        message: data.message || 'Login failed',
      };
    }

    // Store user info in localStorage for easy access
    if (data.user) {
      localStorage.setItem('user', JSON.stringify(data.user));
    }

    return {
      success: true,
      token: data.token,
      user: data.user,
    };
  } catch (error) {
    console.error('Login error:', error);
    return {
      success: false,
      message: 'Network error occurred',
    };
  }
}

// Logout
export async function logout(): Promise<boolean> {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/logout`, {
      method: 'POST',
      credentials: 'include',
    });

    // Clear local storage
    localStorage.removeItem('user');

    return response.ok;
  } catch (error) {
    console.error('Logout error:', error);
    return false;
  }
}

// Check if user is authenticated
export function isAuthenticated(): boolean {
  // We rely on the auth_token cookie which is handled by the middleware
  // This function is mainly for client-side checks
  const user = localStorage.getItem('user');
  return !!user;
}

// Get current user
export function getCurrentUser(): any | null {
  try {
    const user = localStorage.getItem('user');
    return user ? JSON.parse(user) : null;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

// Get auth token (if needed for API calls)
export function getAuthToken(): string | null {
  try {
    // In a real app, you might want to retrieve this from a secure cookie
    const token = localStorage.getItem('token');
    return token;
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  }
}

// Check if token is expired
export function isTokenExpired(token: string): boolean {
  try {
    const decoded = jwtDecode<TokenPayload>(token);
    const currentTime = Math.floor(Date.now() / 1000);
    return decoded.exp < currentTime;
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true;
  }
}

// Validate token on the client side
export function validateToken(token: string): boolean {
  try {
    if (!token || isTokenExpired(token)) {
      return false;
    }
    return true;
  } catch (error) {
    console.error('Error validating token:', error);
    return false;
  }
}