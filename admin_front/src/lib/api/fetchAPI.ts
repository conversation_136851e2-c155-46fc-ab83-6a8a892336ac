// lib/api/fetchAPI.ts

'use client';

import { useApiUrl } from './api-url-context';

/**
 * Enhanced API fetching utility with improved error handling and CORS support
 */

const isBrowser = typeof window !== 'undefined';

// Default fallback responses for specific endpoints when network errors occur
function createEmptyResponse<T>(endpoint: string): T {
  console.warn(`Network error for ${endpoint}, returning fallback response`);
  
  // Mail configs specific fallbacks
  if (endpoint.includes('/mail/configs')) {
    if (endpoint.includes('/mail/configs/')) {
      return {
        mail_config: {
          id: '',
          name: 'Error loading configuration',
          host: '',
          port: 587,
          username: '',
          password_encrypted: false,
          from_email: '',
          from_name: '',
          company_id: '',
          use_ssl: true,
          active: false,
          created_at: '',
          updated_at: ''
        },
        message: 'Failed to load due to network error.'
      } as unknown as T;
    } else {
      return {
        mail_configs: [],
        total: 0,
        message: 'Failed to load mail configurations due to network error.'
      } as unknown as T;
    }
  }
  
  // Companies fallback
  if (endpoint.includes('/companies')) {
    if (endpoint.includes('/companies/')) {
      return {
        company: {
          id: '',
          name: 'Error loading company',
          created_at: '',
          updated_at: ''
        },
        message: 'Failed to load company due to network error.'
      } as unknown as T;
    } else {
      return {
        companies: [],
        total: 0,
        message: 'Failed to load companies due to network error.'
      } as unknown as T;
    }
  }
  
  // Default empty response
  return {} as T;
}

/**
 * Enhanced fetch API for making API requests with authentication and error handling
 * This version is for client components that can use the context
 */
export function useFetchAPI() {
  const { apiUrl } = useApiUrl();
  
  return async function fetchWithContext<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${apiUrl}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
    
    // Log the URL in development
    if (process.env.NODE_ENV !== 'production') {
      console.log(`API call to: ${url}`);
    }
    
    const isRetry = (options.headers as any)?.__isRetryAfterRefresh === true;
  
    try {
      // Setup headers with appropriate CORS handling
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
        'Origin': isBrowser ? window.location.origin : 'http://localhost:3000',
        'Accept': 'application/json',
        ...(options.headers || {})
      };
    
      // Create request options
      const requestOptions: RequestInit = {
        ...options,
        headers,
        credentials: 'include', // Always include credentials for authentication
        mode: 'cors',           // Always use CORS mode for cross-origin requests
      };
    
      // Log outgoing request (only in development)
      if (process.env.NODE_ENV !== 'production') {
        console.log(`📤 [API] ${options.method || 'GET'} ${url}`);
      }
    
      // Make the request
      const response = await fetch(url, requestOptions);
    
      // Log response status (only in development)
      if (process.env.NODE_ENV !== 'production') {
        console.log(`📥 [API] ${response.status} from ${endpoint}`);
      }
    
      // Handle error responses
      if (!response.ok) {
        // Try to parse error response
        let errorData: any = {};
        let errorText = '';
        
        const contentType = response.headers.get('content-type');
        if (contentType?.includes('application/json')) {
          try {
            errorData = await response.json();
          } catch {
            errorText = await response.text();
          }
        } else {
          errorText = await response.text();
        }
        
        // Handle token expiration
        const isTokenExpired = response.status === 401 && !isRetry &&
          (errorData.error === 'token_expired' ||
           errorData.message?.includes('expired') ||
           errorText.includes('expired')) &&
          !endpoint.includes('/auth/refresh');

        if (isTokenExpired) {
          if (process.env.NODE_ENV !== 'production') {
            console.log('🔄 [API] Token expired, attempting refresh...');
          }
          
          const refreshed = await refreshToken();
          if (refreshed) {
            if (process.env.NODE_ENV !== 'production') {
              console.log('✅ [API] Token refreshed, retrying request');
            }
            
            // Retry the original request with a fresh token
            return fetchAPI<T>(endpoint, {
              ...options,
              headers: {
                ...(options.headers || {}),
                __isRetryAfterRefresh: String(true), // Mark as retried to prevent infinite loops
              }
            });
          } else {
            // Redirect to login if refresh failed
            if (isBrowser) {
              console.error('❌ [API] Token refresh failed, redirecting to login');
              window.location.href = '/login';
            }
            throw new Error('Session expired. Please log in again.');
          }
        }
        
        // Handle unauthorized requests (after refresh attempts)
        if (response.status === 401 && isBrowser) {
          console.error('❌ [API] Unauthorized, redirecting to login');
          window.location.href = '/login';
          throw new Error('Unauthorized. Redirecting to login.');
        }
        
        // Throw a detailed error with all available information
        const errorMessage = errorData.message || errorData.error || errorText || `Error ${response.status}: Unknown API error`;
        console.error(`❌ [API] Error for ${endpoint}:`, errorMessage);
        throw new Error(errorMessage);
      }
    
      // Handle empty responses (204 No Content)
      if (response.status === 204) {
        return {} as T;
      }
    
      // Parse successful response
      const contentType = response.headers.get('content-type');
      if (contentType?.includes('application/json')) {
        const data = await response.json();
        return data;
      } else {
        const text = await response.text();
        return text as unknown as T;
      }
    } catch (error: any) {
      // Log the full error in development
      if (process.env.NODE_ENV !== 'production') {
        console.error(`❌ [API] Error in fetchAPI for ${endpoint}:`, error);
      }
    
      // For network/CORS errors, return fallback response instead of crashing
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        console.warn(`⚠️ [API] Network error for ${endpoint}, possibly CORS related`);
        return createEmptyResponse<T>(endpoint);
      }
    
      // For other errors, rethrow
      throw error;
    }
  };
}

/**
 * Standard fetch API for non-client components
 * This version uses the environment variable directly
 */
export async function fetchAPI<T>(
  endpoint: string,
  options: RequestInit & { timeout?: number } = {}
): Promise<T> {
  // Use the correct API URL from environment variable
  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api';
  const url = `${API_BASE_URL}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
  
  // Log the URL in development
  if (typeof window !== 'undefined' && process.env.NODE_ENV !== 'production') {
    console.log(`API call to: ${url}`);
  }
  
  // Extract timeout option
  const { timeout, ...fetchOptions } = options;
  
  try {
    // Create an AbortController for timeout handling
    const controller = new AbortController();
    const timeoutId = timeout ? setTimeout(() => controller.abort(), timeout) : null;
    
    const response = await fetch(url, {
      ...fetchOptions,
      headers: {
        'Content-Type': 'application/json',
        ...fetchOptions.headers,
      },
      credentials: 'include',
      signal: timeout ? controller.signal : undefined,
    });
    
    // Clear timeout if it was set
    if (timeoutId) clearTimeout(timeoutId);
    
    // Handle response status
    if (!response.ok) {
      if (response.status === 504) {
        console.error('Gateway timeout error. The server took too long to respond.');
        throw new Error('The server took too long to respond. Please try again later.');
      }
      
      // Handle other error statuses
      const errorText = await response.text();
      throw new Error(`API error ${response.status}: ${errorText}`);
    }
    
    // Parse successful response
    const contentType = response.headers.get('content-type');
    if (contentType?.includes('application/json')) {
      const data = await response.json();
      return data;
    } else {
      const text = await response.text();
      return text as unknown as T;
    }
  } catch (error: any) {
    // Handle timeout errors specifically
    if (error.name === 'AbortError') {
      console.error(`Request timeout for ${endpoint} after ${timeout}ms`);
      throw new Error('The request timed out. Please try again later.');
    }
    
    // Log the full error in development
    if (process.env.NODE_ENV !== 'production') {
      console.error(`❌ [API] Error in fetchAPI for ${endpoint}:`, error);
    }
    
    throw error;
  }
}

/**
 * Refreshes the authentication token
 * @returns Promise<boolean> - True if successful, false otherwise
 */
async function refreshToken(): Promise<boolean> {
  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api';
  
  try {
    const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': isBrowser ? window.location.origin : 'http://localhost:3000'
      },
      credentials: 'include', // Important for cookies
      mode: 'cors',
      cache: 'no-store' // Prevent caching of refresh tokens
    });
    
    if (!response.ok) {
      return false;
    }
    
    const data = await response.json();
    
    // Store new expiry time if provided
    if (data.expires_in) {
      const expiresAt = Math.floor(Date.now() / 1000) + data.expires_in;
      localStorage.setItem('session_expires_at', expiresAt.toString());
    }
    
    return true;
  } catch (error) {
    console.error('Error refreshing token:', error);
    return false;
  }
}

/**
 * Simple GET request helper function
 * @param endpoint - API endpoint
 * @returns Promise with the requested data
 */
export async function get<T>(endpoint: string): Promise<T> {
  return fetchAPI<T>(endpoint, { method: 'GET' });
}

/**
 * Simple POST request helper function
 * @param endpoint - API endpoint
 * @param data - Request body data
 * @returns Promise with the response data
 */
export async function post<T>(endpoint: string, data: any): Promise<T> {
  return fetchAPI<T>(endpoint, {
    method: 'POST',
    body: JSON.stringify(data)
  });
}

/**
 * Simple PUT request helper function
 * @param endpoint - API endpoint
 * @param data - Request body data
 * @returns Promise with the response data
 */
export async function put<T>(endpoint: string, data: any): Promise<T> {
  return fetchAPI<T>(endpoint, {
    method: 'PUT',
    body: JSON.stringify(data)
  });
}

/**
 * Simple DELETE request helper function
 * @param endpoint - API endpoint
 * @returns Promise with the response data
 */
export async function del<T>(endpoint: string): Promise<T> {
  return fetchAPI<T>(endpoint, { method: 'DELETE' });
}

// Export all functions and also a default object
export default {
  fetchAPI,
  get,
  post,
  put,
  delete: del
};
