// Company model from backend
export interface Company {
    id: string;
    name: string;
    connection_string: string;
    database_type: string;
    url?: string;
    started_at: string;  // ISO date string format
    created_at: string;
    updated_at: string;
  }
  
  // Form data for creating/updating companies
  export interface CompanyFormData {
    name: string;
    connection_string: string;
    database_type: string;
    url?: string;
    started_at: string;  // YYYY-MM-DD format for form inputs
  }
  
  // Database types available
  export type DatabaseType = 'postgresql' | 'mysql' | 'sqlite' | 'sqlserver' | 'mongodb';
  
  // API response types
  export interface ApiResponse<T> {
    data: T;
    message?: string;
    success: boolean;
  }