import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// IMPORTANT: Since we're using localStorage which is client-side only,
// we need to minimize middleware redirects that happen server-side
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Get the access token from the cookies - updated to match your actual cookie name
  const accessToken = request.cookies.get('access_token')?.value;
  
  // For the login page, let the client-side code handle redirection
  // This prevents the redirect loop
  if (pathname === '/login') {
    return NextResponse.next();
  }
  
  // If at the root, redirect to dashboard (client code will redirect to login if needed)
  if (pathname === '/') {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }
  
  // Otherwise, continue with the request and let client-side handle auth
  return NextResponse.next();
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: [
    // Only match the root and login page to avoid extra redirects
    '/',
    '/login',
  ],
};