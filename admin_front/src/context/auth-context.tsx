'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useRouter, usePathname } from 'next/navigation';

interface User {
  id: number;
  email: string;
  username: string;
  role: string;
  first_name: string;
  last_name: string;
  active: boolean;
  last_login: string;
  created_at: string;
  updated_at: string;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => Promise<void>;
  checkAuth: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [sessionExpiresAt, setSessionExpiresAt] = useState<number | null>(null);
  const router = useRouter();
  const pathname = usePathname();
  
  const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api';

  // Handle authentication errors consistently
  const handleAuthError = () => {
    setUser(null);
    localStorage.removeItem('user');
    localStorage.removeItem('session_expires_at');
    
    // Only redirect if we're not already on the login page
    if (pathname !== '/login') {
      router.push('/login');
    }
  };

  // Function to refresh token - improved error handling
  const refreshToken = async (): Promise<boolean> => {
    try {
      console.log('Refreshing token...');
      
      // PROBLEM: This is using a hardcoded API_URL variable
      // const response = await fetch(`${API_URL}/auth/refresh`, {
      
      // FIXED: Use the correct API URL from environment variable
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || '/api';
      const response = await fetch(`${apiUrl}/auth/refresh`, {
        method: 'POST',
        credentials: 'include', // Send cookies
        headers: {
          'Content-Type': 'application/json',
          'Origin': typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000'
        },
        mode: 'cors'
      });
      
      if (!response.ok) {
        console.error('Token refresh failed:', response.status);
        return false;
      }
      
      const data = await response.json();
      console.log('Token refreshed successfully');
      
      // Save new expiry time
      if (data.expires_in) {
        const expiresAt = Math.floor(Date.now() / 1000) + data.expires_in;
        setSessionExpiresAt(expiresAt);
        localStorage.setItem('session_expires_at', expiresAt.toString());
        
        // Setup next refresh
        setupAutoRefresh(expiresAt);
      }
      
      return true;
    } catch (error) {
      console.error('Token refresh error:', error);
      return false;
    }
  };

  // Setup automatic token refresh before expiry
  const setupAutoRefresh = (expiresAt: number) => {
    const now = Math.floor(Date.now() / 1000);
    const timeToRefresh = Math.max(0, (expiresAt - now - 300) * 1000); // Refresh 5 minutes before expiry
    
    console.log(`Setting up token refresh in ${timeToRefresh / 1000} seconds`);
    
    if (timeToRefresh <= 0) {
      // Token already expired or about to expire, refresh now
      refreshToken();
      return;
    }
    
    // Clean up any existing timer
    if (typeof window !== 'undefined' && window.__refreshTimer) {
      clearTimeout(window.__refreshTimer);
    }
    
    // Set timer to refresh token before it expires
    if (typeof window !== 'undefined') {
      window.__refreshTimer = setTimeout(() => {
        refreshToken();
      }, timeToRefresh);
    }
  };

  // Generic fetch API with improved CORS handling and no CSRF
  const fetchWithAuth = async (endpoint: string, options: RequestInit = {}) => {
    // PROBLEM: This is using a hardcoded API_URL variable
    // const url = `${API_URL}${endpoint}`;
    
    // FIXED: Use the correct API URL from environment variable
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || '/api';
    const url = `${apiUrl}${endpoint.startsWith('/') ? endpoint : `/${endpoint}`}`;
    
    // Add headers without CSRF
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      'Origin': typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000',
      ...options.headers,
    };
    
    try {
      console.log(`Fetching ${url} with method ${options.method || 'GET'}`);
      const response = await fetch(url, {
        ...options,
        headers,
        credentials: 'include', // Send cookies
        mode: 'cors',          // Always use CORS mode
      });
      
      // If unauthorized, try to refresh token
      if (response.status === 401) {
        // Check if token refresh already attempted
        if (options.headers && (options.headers as any).__refreshAttempted) {
          throw new Error('Authentication failed even after token refresh');
        }
        
        const refreshSuccessful = await refreshToken();
        if (refreshSuccessful) {
          // Retry the original request
          return fetchWithAuth(endpoint, {
            ...options,
            headers: {
              ...options.headers,
              __refreshAttempted: true, // Mark to prevent infinite loops
            },
          });
        } else {
          throw new Error('Token refresh failed');
        }
      }
      
      return response;
    } catch (error) {
      console.error(`Fetch error for ${endpoint}:`, error);
      throw error;
    }
  };

  const checkAuth = async (): Promise<boolean> => {
    try {
      console.log('Checking authentication with server...');
      
      // PROBLEM: This is using a hardcoded API_URL variable
      // const response = await fetchWithAuth('/auth/status', {
      //   method: 'GET',
      // });
      
      // FIXED: Use the correct API URL from environment variable
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || '/api';
      console.log('Using API URL for status check:', apiUrl);
      
      const response = await fetch(`${apiUrl}/auth/status`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Origin': typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000'
        },
        credentials: 'include', // Important for cookies
        mode: 'cors'
      });

      console.log('Auth check response status:', response.status);
      
      if (response.ok) {
        const data = await response.json();
        console.log('Auth status:', data);
        
        if (data.authenticated && data.user) {
          setUser(data.user);
          localStorage.setItem('user', JSON.stringify(data.user));
          
          // Update session expiry if provided
          if (data.expires_in) {
            const expiresAt = Math.floor(Date.now() / 1000) + data.expires_in;
            setSessionExpiresAt(expiresAt);
            localStorage.setItem('session_expires_at', expiresAt.toString());
            
            // Setup auto-refresh
            setupAutoRefresh(expiresAt);
          }
          
          return true;
        } else {
          console.log('Auth failed: not authenticated');
          setUser(null);
          localStorage.removeItem('user');
          localStorage.removeItem('session_expires_at');
          return false;
        }
      } else {
        console.log('Auth check failed');
        setUser(null);
        localStorage.removeItem('user');
        localStorage.removeItem('session_expires_at');
        return false;
      }
    } catch (error) {
      console.error('Auth check error:', error);
      return false;
    }
  };

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      console.log('Attempting login...');
      
      // PROBLEM: This is using a hardcoded API_URL variable
      // const response = await fetch(`${API_URL}/auth/login`, {
      
      // FIXED: Use the correct API URL from environment variable
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || '/api';
      const response = await fetch(`${apiUrl}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Origin': typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000'
        },
        body: JSON.stringify({ email, password }),
        credentials: 'include', // Important for storing cookies
        mode: 'cors'          // Explicitly set CORS mode
      });

      console.log('Login response status:', response.status);
      
      if (!response.ok) {
        const errorData = await response.json();
        console.error('Login failed:', errorData.message || errorData.error);
        return false;
      }
      
      const data = await response.json();
      console.log('Login successful:', data);
      
      // Store user data
      setUser(data.user);
      localStorage.setItem('user', JSON.stringify(data.user));
      
      // Store session expiry
      if (data.expires_in) {
        const expiresAt = Math.floor(Date.now() / 1000) + data.expires_in;
        setSessionExpiresAt(expiresAt);
        localStorage.setItem('session_expires_at', expiresAt.toString());
        
        // Setup auto-refresh
        setupAutoRefresh(expiresAt);
      }
      
      return true;
    } catch (error) {
      console.error('Login error:', error);
      return false;
    }
  };

  const logout = async (): Promise<void> => {
    try {
      // Clear refresh timer
      if (typeof window !== 'undefined' && window.__refreshTimer) {
        clearTimeout(window.__refreshTimer);
      }
      
      // PROBLEM: This is using a hardcoded API_URL variable
      // await fetch(`${API_URL}/auth/logout`, {
      
      // FIXED: Use the correct API URL from environment variable
      const apiUrl = process.env.NEXT_PUBLIC_API_URL || '/api';
      await fetch(`${apiUrl}/auth/logout`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Origin': typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000'
        },
        credentials: 'include',
        mode: 'cors'
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Always clear local state regardless of API response
      setUser(null);
      setSessionExpiresAt(null);
      localStorage.removeItem('user');
      localStorage.removeItem('session_expires_at');
      router.push('/login');
    }
  };

  // Add this function to your auth-context.tsx file
  const debugApiUrl = () => {
    if (typeof window !== 'undefined' && process.env.NODE_ENV !== 'production') {
      console.log('Debug API URL information:');
      console.log('- process.env.NEXT_PUBLIC_API_URL:', process.env.NEXT_PUBLIC_API_URL);
      console.log('- window.location.origin:', window.location.origin);
      console.log('- Current pathname:', window.location.pathname);
      
      // Check if there's a global API_URL variable
      console.log('- API_URL variable:', typeof API_URL !== 'undefined' ? API_URL : 'Not defined');
      
      // Check localStorage for any stored API URL
      console.log('- localStorage API_URL:', localStorage.getItem('api_url'));
    }
  };

  // Check authentication status on mount
  useEffect(() => {
    debugApiUrl();
    
    const initAuth = async () => {
      try {
        // First check localStorage for a user
        const storedUser = localStorage.getItem('user');
        const storedExpiresAt = localStorage.getItem('session_expires_at');
        
        let isValidSession = false;
        
        if (storedUser && storedExpiresAt) {
          try {
            const userData = JSON.parse(storedUser);
            const expiresAt = parseInt(storedExpiresAt, 10);
            const now = Math.floor(Date.now() / 1000);
            
            console.log('Found user in localStorage:', userData);
            console.log(`Session expires at: ${new Date(expiresAt * 1000).toLocaleString()}`);
            
            if (expiresAt > now) {
              // User session is still valid according to localStorage
              setUser(userData);
              setSessionExpiresAt(expiresAt);
              isValidSession = true;
              
              // Setup auto-refresh
              setupAutoRefresh(expiresAt);
              
              // If we're on the login page but have a valid user session,
              // redirect to dashboard
              if (pathname === '/login') {
                console.log('Redirecting from login to dashboard');
                router.push('/dashboard');
              }
            } else {
              console.log('Stored session has expired, trying to refresh');
              // Session expired, attempt to refresh
              const refreshSuccessful = await refreshToken();
              if (refreshSuccessful) {
                // If refresh worked, verify with server
                isValidSession = await checkAuth();
              } else {
                isValidSession = false;
              }
            }
          } catch (e) {
            console.error('Error parsing stored user', e);
            localStorage.removeItem('user');
            localStorage.removeItem('session_expires_at');
          }
        }
        
        // If we couldn't establish a valid session from localStorage,
        // check with the server
        if (!isValidSession) {
          console.log('No valid session found in localStorage, checking with server');
          isValidSession = await checkAuth();
        }
        
        if (!isValidSession) {
          console.log('Could not establish a valid session');
          handleAuthError();
        }
      } catch (error) {
        console.error('Error during initial auth check:', error);
        handleAuthError();
      } finally {
        setLoading(false);
      }
    };
    
    initAuth();
    
    // Setup event listeners for window focus/visibility to check auth
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        checkAuth();
      }
    };
    
    const handleFocus = () => {
      checkAuth();
    };
    
    if (typeof window !== 'undefined') {
      document.addEventListener('visibilitychange', handleVisibilityChange);
      window.addEventListener('focus', handleFocus);
    
      return () => {
        document.removeEventListener('visibilitychange', handleVisibilityChange);
        window.removeEventListener('focus', handleFocus);
        
        // Clear refresh timer
        if (window.__refreshTimer) {
          clearTimeout(window.__refreshTimer);
        }
      };
    }
  }, [router, pathname]);

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        login,
        logout,
        checkAuth,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

// Add missing type declaration
declare global {
  interface Window {
    __refreshTimer: ReturnType<typeof setTimeout>;
  }
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
