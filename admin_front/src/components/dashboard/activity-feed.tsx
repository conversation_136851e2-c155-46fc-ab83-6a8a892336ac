// src/components/dashboard/activity-feed.tsx
export interface Activity {
    type: 'template_updated' | 'campaign_started' | 'company_added' | 'email_sent' | 'smtp_configured';
    name?: string;
    company?: string;
    count?: number;
    time: string;
  }
  
  interface ActivityFeedProps {
    activities: Activity[];
  }
  
  export default function ActivityFeed({ activities }: ActivityFeedProps) {
    // Helper function to get activity icon
    function getActivityIcon(type: Activity['type']) {
      switch (type) {
        case 'template_updated': return 'fa-file-alt';
        case 'campaign_started': return 'fa-paper-plane';
        case 'company_added': return 'fa-building';
        case 'email_sent': return 'fa-envelope';
        case 'smtp_configured': return 'fa-cog';
        default: return 'fa-bell';
      }
    }
    
    // Helper function to get activity color
    function getActivityColor(type: Activity['type']) {
      switch (type) {
        case 'template_updated': return 'text-indigo-500 bg-indigo-100';
        case 'campaign_started': return 'text-green-500 bg-green-100';
        case 'company_added': return 'text-blue-500 bg-blue-100';
        case 'email_sent': return 'text-purple-500 bg-purple-100';
        case 'smtp_configured': return 'text-gray-500 bg-gray-100';
        default: return 'text-gray-500 bg-gray-100';
      }
    }
    
    // Helper function to get activity title
    function getActivityTitle(activity: Activity) {
      switch (activity.type) {
        case 'template_updated':
          return `Template Updated: ${activity.name}`;
        case 'campaign_started':
          return `Campaign Started: ${activity.name}`;
        case 'company_added':
          return `Company Added: ${activity.name}`;
        case 'email_sent':
          return `${activity.count} Emails Sent`;
        case 'smtp_configured':
          return `SMTP Configured`;
        default:
          return 'Activity';
      }
    }
    
    // Helper function to get activity description
    function getActivityDescription(activity: Activity) {
      switch (activity.type) {
        case 'template_updated':
        case 'campaign_started':
          return activity.company;
        case 'company_added':
          return '';
        case 'email_sent':
          return activity.company;
        case 'smtp_configured':
          return activity.company;
        default:
          return '';
      }
    }
    
    return (
      <div className="p-6 bg-white rounded-lg shadow">
        <h2 className="text-lg font-medium text-gray-900">Recent Activity</h2>
        <div className="mt-4 divide-y divide-gray-200">
          {activities.map((activity, index) => (
            <div key={index} className="py-3">
              <div className="flex">
                <div className={`flex-shrink-0 w-10 h-10 rounded-full ${getActivityColor(activity.type)} flex items-center justify-center`}>
                  <i className={`fas ${getActivityIcon(activity.type)}`}></i>
                </div>
                <div className="flex-1 ml-4">
                  <div className="flex justify-between">
                    <div>
                      <span className="text-sm font-medium text-gray-900">{getActivityTitle(activity)}</span>
                      {getActivityDescription(activity) && (
                        <p className="text-sm text-gray-500">{getActivityDescription(activity)}</p>
                      )}
                    </div>
                  <span className="text-sm text-gray-500">{activity.time}</span>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}