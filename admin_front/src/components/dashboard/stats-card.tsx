interface StatsCardProps {
    title: string;
    value: number | string;
    trend: string;
    icon: string;
    color: 'indigo' | 'green' | 'blue' | 'purple' | 'red' | 'yellow';
  }
  
  export default function StatsCard({
    title,
    value,
    trend,
    icon,
    color
  }: StatsCardProps) {
    const colorClasses = {
      indigo: 'text-indigo-500 bg-indigo-100',
      green: 'text-green-500 bg-green-100',
      blue: 'text-blue-500 bg-blue-100',
      purple: 'text-purple-500 bg-purple-100',
      red: 'text-red-500 bg-red-100',
      yellow: 'text-yellow-500 bg-yellow-100',
    };
    
    const isTrendPositive = trend.startsWith('+');
    
    return (
      <div className="p-6 bg-white rounded-lg shadow">
        <div className="flex items-center">
          <div className={`p-3 rounded-full ${colorClasses[color]}`}>
            <i className={`fas fa-${icon}`}></i>
          </div>
          <div className="ml-4">
            <h2 className="text-sm font-medium text-gray-600">{title}</h2>
            <div className="flex items-baseline">
              <p className="text-2xl font-semibold text-gray-900">{value}</p>
              <span className={`ml-2 text-sm font-medium ${isTrendPositive ? 'text-green-600' : 'text-red-600'}`}>
                {trend}
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  }