'use client';

import React, { useRef } from 'react';
import 'tinymce/tinymce';
import 'tinymce/themes/silver';
import 'tinymce/icons/default';
import 'tinymce/plugins/link';
import 'tinymce/plugins/image';
import 'tinymce/plugins/code';
import 'tinymce/plugins/lists';
import 'tinymce/plugins/table';
import {Editor} from "@tinymce/tinymce-react";

interface TemplateEditorProps {
  initialValue: string;
  onChange: (content: string) => void;
  disabled?: boolean;
}

const defaultEmailTemplate = `
<table width="100%" cellspacing="0" cellpadding="0" style="background-color: #c6f1bb; padding: 20px;">
  <tr>
    <td align="center">
      <table width="600" cellspacing="0" cellpadding="0" style="background-color: #5abc91; border-radius: 8px; text-align: center; color: #ffffff; font-family: Arial, sans-serif;">
        <tr>
          <td style="padding: 20px;">
            <img style="display: block; margin: 0 auto 10px;" src="https://www.psynarios.com/landing/psynarios-logo-light.png" alt="Logo" height="200">
            <h1 style="font-size: 32px; margin: 0;">Changez ici </h1>
            <p style="font-size: 18px; margin: 20px 0;">changer ici</p>
            <a href="#" style="display: inline-block; background-color: #ffffff; color: #007bff; text-decoration: none; padding: 12px 24px; border-radius: 8px; font-weight: bold;">
              Découvrir mon scénario
            </a>
          </td>
        </tr>
      </table>
    </td>
  </tr>
</table>
`;

const TemplateEditor: React.FC<TemplateEditorProps> = ({
  initialValue,
  onChange,
  disabled = false,
}) => {
  const editorRef = useRef<any>(null);
  
  // Determine if we should use the default template
  const editorInitialValue = initialValue || defaultEmailTemplate;
  
  return (
    <div className="template-editor space-y-4">
      <p className="text-sm text-gray-600">
        Cliquez sur le bouton <strong>Découvrir mon scénario</strong> puis utilisez l'outil 🔗 pour modifier le lien.
      </p>

      <Editor
        onInit={(evt, editor) => (editorRef.current = editor)}
        initialValue={editorInitialValue}
        tinymceScriptSrc="/tinymce/tinymce.min.js"
        onEditorChange={(content) => onChange(content)}
        init={{
          height: 500,
          menubar: false,
          plugins: ['link', 'image', 'lists', 'code', 'table'],
          toolbar:
            'undo redo | bold italic | alignleft aligncenter alignright | bullist numlist | link image | code',
          content_style:
            'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
          readonly: disabled,
          base_url: '/tinymce',
          suffix: '.min',
        }}
      />
    </div>
  );
};

export default TemplateEditor;
