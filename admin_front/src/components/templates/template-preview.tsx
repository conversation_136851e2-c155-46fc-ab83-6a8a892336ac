'use client';

import { useState, useEffect } from 'react';

interface TemplateVariable {
  name: string;
  description: string;
  example: string;
}

const AVAILABLE_VARIABLES: TemplateVariable[] = [
  { name: 'USER_NAME', description: 'User\'s full name', example: '<PERSON>' },
  { name: 'USER_EMAIL', description: 'User\'s email address', example: '<EMAIL>' },
  { name: 'COMPANY_NAME', description: 'Company name', example: 'Acme Inc.' },
  { name: 'CURRENT_DATE', description: 'Current date', example: 'May 3, 2025' },
  { name: 'START_DATE', description: 'User onboarding start date', example: 'January 15, 2025' },
  { name: 'DAY_NUMBER', description: 'Day number in sequence', example: '5' },
];

interface TemplatePreviewProps {
  templateId: string;
  subject: string;
  content: string;
}

export default function TemplatePreview({ templateId, subject, content }: TemplatePreviewProps) {
  const [testData, setTestData] = useState<Record<string, string>>({});
  const [previewHtml, setPreviewHtml] = useState('');
  const [previewSubject, setPreviewSubject] = useState('');
  const [loading, setLoading] = useState(false);
  
  // Initialize test data with example values
  useEffect(() => {
    const initialData: Record<string, string> = {};
    AVAILABLE_VARIABLES.forEach(variable => {
      initialData[variable.name] = variable.example;
    });
    setTestData(initialData);
  }, []);
  
  // Generate preview whenever test data or content changes
  useEffect(() => {
    generatePreview();
  }, [testData, content, subject]);
  
  function generatePreview() {
    setLoading(true);
    
    try {
      // Replace variables in content
      let processedContent = content;
      let processedSubject = subject;
      
      // Replace each variable with its test value
      Object.entries(testData).forEach(([key, value]) => {
        const variablePattern = new RegExp(`{{${key}}}`, 'g');
        processedContent = processedContent.replace(variablePattern, value);
        processedSubject = processedSubject.replace(variablePattern, value);
      });
      
      setPreviewHtml(processedContent);
      setPreviewSubject(processedSubject);
    } catch (error) {
      console.error('Failed to generate preview:', error);
    } finally {
      setLoading(false);
    }
  }
  
  function handleTestDataChange(key: string, value: string) {
    setTestData(prev => ({ ...prev, [key]: value }));
  }
  
  return (
    <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-6">
      <div className="md:col-span-1">
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Template Preview</h3>
          <p className="text-sm text-gray-600 mb-4">
            Customize test data to see how your template will look with different values:
          </p>
          
          <div className="space-y-3">
            {AVAILABLE_VARIABLES.map(variable => (
              <div key={variable.name} className="flex flex-col">
                <label className="text-sm font-medium text-gray-700 mb-1">
                  {variable.name}
                </label>
                <input
                  type="text"
                  value={testData[variable.name] || ''}
                  onChange={(e) => handleTestDataChange(variable.name, e.target.value)}
                  className="border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                />
              </div>
            ))}
          </div>
          
          <button
            type="button"
            onClick={generatePreview}
            disabled={loading}
            className="mt-4 inline-flex items-center px-3 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            {loading ? 'Generating...' : 'Refresh Preview'}
          </button>
        </div>
      </div>
      
      <div className="md:col-span-2">
        <div className="bg-white rounded-lg shadow overflow-hidden">
          <div className="border-b border-gray-200 p-4 bg-gray-50">
            <div className="text-sm font-medium text-gray-800">
              Subject: {previewSubject}
            </div>
          </div>
          
          <div className="p-6 min-h-[300px]">
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="w-8 h-8 border-4 border-indigo-500 rounded-full border-t-transparent animate-spin"></div>
              </div>
            ) : (
              <div className="email-preview" dangerouslySetInnerHTML={{ __html: previewHtml }}></div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}