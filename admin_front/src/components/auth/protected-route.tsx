'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/auth-context';

interface ProtectedRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
}

export default function ProtectedRoute({ 
  children, 
  redirectTo = '/login' 
}: ProtectedRouteProps) {
  const { user, loading, checkAuth } = useAuth();
  const router = useRouter();

  useEffect(() => {
    const verifyAuth = async () => {
      if (!loading && !user) {
        const isAuthenticated = await checkAuth();
        if (!isAuthenticated) {
          router.push(redirectTo);
        }
      }
    };

    verifyAuth();
  }, [user, loading, checkAuth, router, redirectTo]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="w-16 h-16 border-t-4 border-b-4 border-indigo-500 rounded-full animate-spin"></div>
      </div>
    );
  }

  // If not authenticated, the useEffect will handle the redirect
  // We just return null while redirecting
  if (!user) {
    return null;
  }

  return <>{children}</>;
}