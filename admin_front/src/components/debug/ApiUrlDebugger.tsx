'use client';

import { useEffect, useState } from 'react';

export function ApiUrlDebugger() {
  const [apiUrl, setApiUrl] = useState<string>('');
  const [origin, setOrigin] = useState<string>('');
  
  useEffect(() => {
    // Only run in development
    if (process.env.NODE_ENV === 'production') return;
    
    setApiUrl(process.env.NEXT_PUBLIC_API_URL || '/api');
    setOrigin(window.location.origin);
    
    // Log API URL info
    console.log('API URL Debugger:');
    console.log('- NEXT_PUBLIC_API_URL:', process.env.NEXT_PUBLIC_API_URL);
    console.log('- window.location.origin:', window.location.origin);
    
    // Monitor fetch calls
    const originalFetch = window.fetch;
    window.fetch = function(input, init) {
      const url = typeof input === 'string' ? input : input instanceof Request ? input.url : String(input);
      
      // Check for localhost URLs
      if (url.includes('localhost')) {
        console.error('⚠️ LOCALHOST URL DETECTED:', url);
        console.trace('Stack trace for localhost URL');
      }
      
      return originalFetch.call(this, input, init);
    };
    
    return () => {
      // Restore original fetch
      window.fetch = originalFetch;
    };
  }, []);
  
  // Only show in development
  if (process.env.NODE_ENV === 'production') {
    return null;
  }
  
  return (
    <div style={{ 
      position: 'fixed', 
      bottom: '10px', 
      right: '10px', 
      background: '#f0f0f0', 
      padding: '10px', 
      border: '1px solid #ccc',
      zIndex: 9999,
      fontSize: '12px'
    }}>
      <div><strong>API URL:</strong> {apiUrl}</div>
      <div><strong>Origin:</strong> {origin}</div>
    </div>
  );
}