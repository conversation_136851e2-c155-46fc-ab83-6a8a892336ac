// components/email-sender/excel-uploader.tsx
'use client';

import React, { useState, useRef } from 'react';
import * as XLSX from 'xlsx';

interface ExcelUploaderProps {
  onFileUploaded: (data: any[]) => void;
  buttonText?: string;
  isSending?: boolean;
}

const ExcelUploader: React.FC<ExcelUploaderProps> = ({ 
  onFileUploaded,
  buttonText = "Confirm",
  isSending = false
}) => {
  const [file, setFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [columns, setColumns] = useState<string[]>([]);
  const [preview, setPreview] = useState<any[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (!selectedFile) return;

    setFile(selectedFile);
    setIsLoading(true);
    setError(null);

    try {
      const data = await readExcelFile(selectedFile);
      const headers = Object.keys(data[0] || {});
      
      // Check if required columns exist
      const requiredColumns = ['username', 'email', 'company'];
      const missingColumns = requiredColumns.filter(col => 
        !headers.some(header => header.toLowerCase() === col.toLowerCase())
      );

      if (missingColumns.length > 0) {
        setError(`Missing required columns: ${missingColumns.join(', ')}. Please make sure your Excel file has these columns.`);
        setFile(null);
        if (fileInputRef.current) fileInputRef.current.value = '';
        setIsLoading(false);
        return;
      }

      setColumns(headers);
      setPreview(data.slice(0, 5)); // Show first 5 rows as preview
    } catch (err) {
      console.error('Error reading Excel file:', err);
      setError('Failed to read Excel file. Please make sure it\'s a valid Excel file.');
      setFile(null);
      if (fileInputRef.current) fileInputRef.current.value = '';
    } finally {
      setIsLoading(false);
    }
  };

  const readExcelFile = (file: File): Promise<any[]> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        try {
          const data = e.target?.result;
          const workbook = XLSX.read(data, { type: 'binary', cellDates: true });
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];
          
          // Convert to JSON with header row
          const result = XLSX.utils.sheet_to_json(worksheet, { header: 'A', blankrows: false });
          
          // Extract header row and remove it from data
          const headers = result[0] as Record<string, string>;
          const rows = result.slice(1);
          
          // Map rows to objects with header keys
          const mappedData = rows.map(row => {
            const mappedRow: Record<string, any> = {};
            Object.keys(row as Record<string, any>).forEach(key => {
              const headerKey = headers[key];
              mappedRow[headerKey] = (row as Record<string, any>)[key];
            });
            return mappedRow;
          });
          
          resolve(mappedData);
        } catch (err) {
          reject(err);
        }
      };
      
      reader.onerror = (err) => {
        reject(err);
      };
      
      reader.readAsBinaryString(file);
    });
  };

  const handleUpload = async () => {
    if (!file) return;
    
    setIsLoading(true);
    
    try {
      const data = await readExcelFile(file);
      onFileUploaded(data);
    } catch (err) {
      console.error('Error processing Excel data:', err);
      setError('Failed to process Excel data. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    e.stopPropagation();
    
    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      const fileInput = fileInputRef.current;
      if (fileInput) {
        // Create a DataTransfer object and add the dropped file
        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(files[0]);
        fileInput.files = dataTransfer.files;
        
        // Trigger the change event
        const event = new Event('change', { bubbles: true });
        fileInput.dispatchEvent(event);
      }
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-medium text-gray-900">Upload Excel File</h2>
        <p className="mt-1 text-sm text-gray-500">
          Upload an Excel file containing the email data.
        </p>
      </div>
      
      <div 
        className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center"
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept=".xlsx,.xls"
          onChange={handleFileChange}
          className="hidden"
        />
        
        {!file ? (
          <div className="space-y-4">
            <div className="mx-auto h-12 w-12 text-gray-400">
              <i className="fas fa-file-excel text-3xl"></i>
            </div>
            <div className="text-sm text-gray-600">
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                className="relative font-medium text-indigo-600 hover:text-indigo-500 focus:outline-none"
              >
                <span>Upload a file</span>
              </button>{' '}
              or drag and drop
            </div>
            <p className="text-xs text-gray-500">
              Excel files only (.xlsx, .xls)
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-center">
              <i className="fas fa-file-excel text-green-500 text-3xl mr-2"></i>
              <span className="text-sm font-medium text-gray-900">{file.name}</span>
            </div>
            
            <button
              type="button"
              onClick={() => {
                setFile(null);
                setColumns([]);
                setPreview([]);
                if (fileInputRef.current) fileInputRef.current.value = '';
              }}
              className="text-sm text-red-600 hover:text-red-500"
            >
              Remove file
            </button>
          </div>
        )}
      </div>
      
      {error && (
        <div className="p-4 text-sm text-red-700 bg-red-100 rounded-md">
          {error}
        </div>
      )}
      
      {file && columns.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-md font-medium text-gray-900">File Preview</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  {columns.map((column, index) => (
                    <th 
                      key={index}
                      scope="col" 
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      {column}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {preview.map((row, rowIndex) => (
                  <tr key={rowIndex}>
                    {columns.map((column, colIndex) => (
                      <td 
                        key={`${rowIndex}-${colIndex}`}
                        className="px-6 py-4 whitespace-nowrap text-sm text-gray-500"
                      >
                        {row[column]?.toString() || ''}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          <div className="text-sm text-gray-500">
            Showing {preview.length} of {file ? 'many' : '0'} rows
          </div>
        </div>
      )}
      
      <div className="flex justify-end">
        <button
          type="button"
          onClick={handleUpload}
          disabled={!file || isLoading || !!error || isSending}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
        >
          {isLoading || isSending ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {isLoading ? 'Processing...' : 'Sending Emails...'}
            </>
          ) : (
            <>
              <i className="fas fa-paper-plane mr-2"></i>
              {buttonText}
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default ExcelUploader;
