// src/components/email-sender/template-selector.tsx
'use client';

import { useEffect, useState } from 'react';
import { getAllTemplates, getTemplateGroups, getAlternativeTemplates } from '@/lib/api/templates';

interface Template {
  id: string;
  name: string;
  subject: string;
  group_id: string;
  day: number;
}

interface TemplateGroup {
  id: string;
  name: string;
  day_number: number;
  company_ids: string[]; // Changed from company_id to company_ids
}

interface TemplateSelectorProps {
  onTemplateSelected: (templateId: string, templateName: string) => void;
  companyId?: string;
}

export default function TemplateSelector({ onTemplateSelected, companyId }: TemplateSelectorProps) {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [templateGroups, setTemplateGroups] = useState<TemplateGroup[]>([]);
  const [alternativeTemplates, setAlternativeTemplates] = useState<Template[]>([]);
  const [selectedGroupId, setSelectedGroupId] = useState<string>('');
  const [selectedTemplateId, setSelectedTemplateId] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showAlternatives, setShowAlternatives] = useState(false);
  
  useEffect(() => {
    async function loadData() {
      setLoading(true);
      
      try {
        // Fetch all template groups
        const groupsData = await getTemplateGroups();
        
        // Filter groups by company if companyId is provided
        const filteredGroups = companyId 
          ? groupsData.filter(group => group.company_ids.includes(companyId))
          : groupsData;
        
        setTemplateGroups(filteredGroups);
        
        // Fetch all templates
        const templatesData = await getAllTemplates();
        
        // Filter templates to only include those from the filtered groups
        const groupIds = filteredGroups.map(group => group.id);
        const filteredTemplates = templatesData.filter(template => 
          groupIds.includes(template.group_id)
        );
        
        setTemplates(filteredTemplates.map(t => ({
          ...t,
          day: t.week_number
        })) as Template[]);

        // If we have a companyId, also fetch alternative templates
        if (companyId) {
          try {
            const alternatives = await getAlternativeTemplates(companyId);
            setAlternativeTemplates(alternatives.map(t => ({
              ...t,
              day: t.week_number
            })) as Template[]);
          } catch (altErr) {
            console.error('Error loading alternative templates:', altErr);
            // Don't set error here, as this is optional functionality
          }
        }
      } catch (err) {
        console.error('Error loading templates:', err);
        setError('Failed to load templates');
      } finally {
        setLoading(false);
      }
    }
    
    loadData();
  }, [companyId]);
  
  // Filter templates by group
  const filteredTemplates = selectedGroupId
    ? templates.filter(template => template.group_id === selectedGroupId)
    : templates;
  
  // Get the templates to display based on whether we're showing alternatives
  const displayTemplates = showAlternatives ? alternativeTemplates : filteredTemplates;
  
  function handleTemplateClick(templateId: string) {
    setSelectedTemplateId(templateId);
    
    // Find the template name
    const template = templates.find(t => t.id === templateId);
    if (template) {
      onTemplateSelected(templateId, template.name);
    }
  }
  
  function toggleAlternatives() {
    setShowAlternatives(!showAlternatives);
    setSelectedTemplateId(''); // Clear selection when switching views
  }
  
  function handleContinue() {
    if (selectedTemplateId) {
      onTemplateSelected(selectedTemplateId, templates.find(t => t.id === selectedTemplateId)?.name || '');
    } else {
      setError('Please select a template');
    }
  }
  
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-medium text-gray-900">Select Email Template</h2>
        <p className="text-sm text-gray-500 mt-1">
          You can select any template from this company, regardless of the day.
        </p>
        {error && (
          <div className="p-4 mt-2 text-sm text-red-700 bg-red-100 rounded-md">
            {error}
          </div>
        )}
      </div>
      
      {loading ? (
        <div className="flex justify-center py-12">
          <div className="w-10 h-10 border-4 border-indigo-500 rounded-full border-t-transparent animate-spin"></div>
        </div>
      ) : (
        <>
          <div className="mb-6 flex justify-between items-center">
            <div className="flex-1">
              <label htmlFor="group" className="block text-sm font-medium text-gray-700 mb-1">
                Filter by Group
              </label>
              <select 
                id="group" 
                value={selectedGroupId}
                onChange={(e) => setSelectedGroupId(e.target.value)}
                className="block w-full border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                disabled={showAlternatives}
              >
                <option value="">All Template Groups</option>
                {templateGroups.map(group => (
                  <option key={group.id} value={group.id}>{group.name} (Day {group.day_number})</option>
                ))}
              </select>
            </div>
            
            {alternativeTemplates.length > 0 && (
              <div className="ml-4 flex items-center">
                <button
                  type="button"
                  onClick={toggleAlternatives}
                  className="px-4 py-2 text-sm font-medium text-indigo-600 bg-white border border-indigo-300 rounded-md shadow-sm hover:bg-indigo-50"
                >
                  {showAlternatives ? 'Show Regular Templates' : 'Show Alternative Templates'}
                </button>
              </div>
            )}
          </div>
          
          {displayTemplates.length === 0 ? (
            <div className="p-6 text-center bg-gray-50 rounded-lg border border-gray-200">
              <p className="text-gray-500">
                {showAlternatives 
                  ? 'No alternative templates available.' 
                  : 'No templates found for this selection.'}
              </p>
              {!showAlternatives && alternativeTemplates.length > 0 && (
                <button
                  type="button"
                  onClick={toggleAlternatives}
                  className="mt-4 px-4 py-2 text-sm font-medium text-indigo-600 bg-white border border-indigo-300 rounded-md shadow-sm hover:bg-indigo-50"
                >
                  View Alternative Templates
                </button>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {displayTemplates.map(template => (
                <div 
                  key={template.id}
                  className={`p-4 border rounded-lg cursor-pointer ${
                    selectedTemplateId === template.id 
                      ? 'border-indigo-500 bg-indigo-50' 
                      : 'border-gray-300 hover:border-indigo-300'
                  }`}
                  onClick={() => handleTemplateClick(template.id)}
                >
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="text-sm font-medium text-gray-900">{template.name}</h3>
                    <span className="px-2 py-1 text-xs text-indigo-800 bg-indigo-100 rounded-full">
                      {showAlternatives ? 'Alternative' : `Day ${templateGroups.find(g => g.id === template.group_id)?.day_number || 'N/A'}`}
                    </span>
                  </div>
                  <p className="text-sm text-gray-500 mb-3">{template.subject}</p>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-500">
                      {templateGroups.find(g => g.id === template.group_id)?.name || 'Unknown Group'}
                    </span>
                    {selectedTemplateId === template.id && (
                      <i className="fas fa-check-circle text-indigo-500"></i>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
          
          <div className="flex justify-end pt-4 border-t border-gray-200">
            <button 
              type="button" 
              onClick={handleContinue}
              disabled={!selectedTemplateId}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              Continue
            </button>
          </div>
        </>
      )}
    </div>
  );
}
