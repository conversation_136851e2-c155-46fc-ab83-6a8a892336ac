'use client';

import { useState } from 'react';

interface DataMapperProps {
  userData: any[];
  onDataMapped: (data: any[]) => void;
}

interface MappingField {
  sourceField: string;
  targetField: string;
}

export default function DataMapper({ userData, onDataMapped }: DataMapperProps) {
  const [mappings, setMappings] = useState<MappingField[]>([
    { sourceField: '', targetField: 'username' },
    { sourceField: '', targetField: 'email' },
    { sourceField: '', targetField: 'company' },
    { sourceField: '', targetField: 'startDate' }
  ]);
  
  const [error, setError] = useState('');
  
  // Extract headers from user data
  const headers = userData.length > 0 ? Object.keys(userData[0]) : [];
  
  // Auto-map fields on component mount
  useState(() => {
    if (headers.length > 0 && userData.length > 0) {
      const newMappings = mappings.map(mapping => {
        // Look for exact match
        let sourceField = headers.find(
          h => h.toLowerCase() === mapping.targetField.toLowerCase()
        );
        
        // If no exact match, look for partial match
        if (!sourceField) {
          sourceField = headers.find(
            h => h.toLowerCase().includes(mapping.targetField.toLowerCase())
          );
        }
        
        return {
          ...mapping,
          sourceField: sourceField || ''
        };
      });
      
      setMappings(newMappings);
    }
  });
  
  function updateMapping(index: number, sourceField: string) {
    const newMappings = [...mappings];
    newMappings[index].sourceField = sourceField;
    setMappings(newMappings);
  }
  
  function handleSubmit() {
    // Validate required fields
    const requiredFields = ['username', 'email', 'company'];
    const missingFields = requiredFields.filter(field => 
      !mappings.some(m => m.targetField === field && m.sourceField)
    );
    
    if (missingFields.length > 0) {
      setError(`Missing required field mapping: ${missingFields.join(', ')}`);
      return;
    }
    
    // Map data according to mappings
    const mappedData = userData.map(row => {
      const mappedRow: Record<string, any> = {};
      
      mappings.forEach(mapping => {
        if (mapping.sourceField && mapping.targetField) {
          mappedRow[mapping.targetField] = row[mapping.sourceField];
        }
      });
      
      return mappedRow;
    });
    
    onDataMapped(mappedData);
  }
  
  function getFieldLabel(fieldId: string): string {
    const labels: Record<string, string> = {
      username: 'Username',
      email: 'Email Address',
      company: 'Company',
      startDate: 'Start Date'
    };
    
    return labels[fieldId] || fieldId;
  }
  
  function isFieldRequired(fieldId: string): boolean {
    return ['username', 'email', 'company'].includes(fieldId);
  }
  
  return (
    <div className="space-y-6">
      <h2 className="text-lg font-medium text-gray-900">Map User Data</h2>
      {error && (
        <div className="p-4 text-sm text-red-700 bg-red-100 rounded-md">
          {error}
        </div>
      )}
      
      <div className="bg-white rounded-lg overflow-hidden border border-gray-200">
        <div className="p-4 border-b border-gray-200">
          <h3 className="text-sm font-medium text-gray-700">Map Fields</h3>
          <p className="mt-1 text-xs text-gray-500">
            Match the columns from your Excel file to the required fields.
          </p>
        </div>
        
        <div className="p-4">
          <div className="space-y-4">
            {mappings.map((mapping, index) => (
              <div key={index} className="flex items-center space-x-4">
                <div className="w-1/3">
                  <label className="block text-sm font-medium text-gray-700">
                    {getFieldLabel(mapping.targetField)}
                    {isFieldRequired(mapping.targetField) && (
                      <span className="text-red-500">*</span>
                    )}
                  </label>
                </div>
                <div className="w-2/3">
                  <select 
                    value={mapping.sourceField}
                    onChange={(e) => updateMapping(index, e.target.value)}
                    className="block w-full border border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  >
                    <option value="">-- Select a column --</option>
                    {headers.map((header) => (
                      <option key={header} value={header}>{header}</option>
                    ))}
                  </select>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="p-4 bg-gray-50 border-t border-gray-200">
          <div className="flex justify-end">
            <button 
              type="button" 
              onClick={handleSubmit}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Continue
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}